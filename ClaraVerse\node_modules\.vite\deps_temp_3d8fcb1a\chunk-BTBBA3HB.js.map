{"version": 3, "sources": ["../../highlight.js/lib/languages/dart.js"], "sourcesContent": ["/*\nLanguage: Dart\nRequires: markdown.js\nAuthor: <PERSON> <<EMAIL>>\nDescription: Dart a modern, object-oriented language developed by Google. For more information see https://www.dartlang.org/\nWebsite: https://dart.dev\nCategory: scripting\n*/\n\n/** @type LanguageFn */\nfunction dart(hljs) {\n  const SUBST = {\n    className: 'subst',\n    variants: [{\n      begin: '\\\\$[A-Za-z0-9_]+'\n    }]\n  };\n\n  const BRACED_SUBST = {\n    className: 'subst',\n    variants: [{\n      begin: /\\$\\{/,\n      end: /\\}/\n    }],\n    keywords: 'true false null this is new super'\n  };\n\n  const STRING = {\n    className: 'string',\n    variants: [\n      {\n        begin: 'r\\'\\'\\'',\n        end: '\\'\\'\\''\n      },\n      {\n        begin: 'r\"\"\"',\n        end: '\"\"\"'\n      },\n      {\n        begin: 'r\\'',\n        end: '\\'',\n        illegal: '\\\\n'\n      },\n      {\n        begin: 'r\"',\n        end: '\"',\n        illegal: '\\\\n'\n      },\n      {\n        begin: '\\'\\'\\'',\n        end: '\\'\\'\\'',\n        contains: [\n          hljs.BACKSLASH_ESCAPE,\n          SUBST,\n          BRACED_SUBST\n        ]\n      },\n      {\n        begin: '\"\"\"',\n        end: '\"\"\"',\n        contains: [\n          hljs.BACKSLASH_ESCAPE,\n          SUBST,\n          BRACED_SUBST\n        ]\n      },\n      {\n        begin: '\\'',\n        end: '\\'',\n        illegal: '\\\\n',\n        contains: [\n          hljs.BACKSLASH_ESCAPE,\n          SUBST,\n          BRACED_SUBST\n        ]\n      },\n      {\n        begin: '\"',\n        end: '\"',\n        illegal: '\\\\n',\n        contains: [\n          hljs.BACKSLASH_ESCAPE,\n          SUBST,\n          BRACED_SUBST\n        ]\n      }\n    ]\n  };\n  BRACED_SUBST.contains = [\n    hljs.C_NUMBER_MODE,\n    STRING\n  ];\n\n  const BUILT_IN_TYPES = [\n    // dart:core\n    'Comparable',\n    'DateTime',\n    'Duration',\n    'Function',\n    'Iterable',\n    'Iterator',\n    'List',\n    'Map',\n    'Match',\n    'Object',\n    'Pattern',\n    'RegExp',\n    'Set',\n    'Stopwatch',\n    'String',\n    'StringBuffer',\n    'StringSink',\n    'Symbol',\n    'Type',\n    'Uri',\n    'bool',\n    'double',\n    'int',\n    'num',\n    // dart:html\n    'Element',\n    'ElementList'\n  ];\n  const NULLABLE_BUILT_IN_TYPES = BUILT_IN_TYPES.map((e) => `${e}?`);\n\n  const KEYWORDS = {\n    keyword: 'abstract as assert async await break case catch class const continue covariant default deferred do ' +\n      'dynamic else enum export extends extension external factory false final finally for Function get hide if ' +\n      'implements import in inferface is late library mixin new null on operator part required rethrow return set ' +\n      'show static super switch sync this throw true try typedef var void while with yield',\n    built_in:\n      BUILT_IN_TYPES\n        .concat(NULLABLE_BUILT_IN_TYPES)\n        .concat([\n          // dart:core\n          'Never',\n          'Null',\n          'dynamic',\n          'print',\n          // dart:html\n          'document',\n          'querySelector',\n          'querySelectorAll',\n          'window'\n        ]),\n    $pattern: /[A-Za-z][A-Za-z0-9_]*\\??/\n  };\n\n  return {\n    name: 'Dart',\n    keywords: KEYWORDS,\n    contains: [\n      STRING,\n      hljs.COMMENT(\n        /\\/\\*\\*(?!\\/)/,\n        /\\*\\//,\n        {\n          subLanguage: 'markdown',\n          relevance: 0\n        }\n      ),\n      hljs.COMMENT(\n        /\\/{3,} ?/,\n        /$/, {\n          contains: [{\n            subLanguage: 'markdown',\n            begin: '.',\n            end: '$',\n            relevance: 0\n          }]\n        }\n      ),\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      {\n        className: 'class',\n        beginKeywords: 'class interface',\n        end: /\\{/,\n        excludeEnd: true,\n        contains: [\n          {\n            beginKeywords: 'extends implements'\n          },\n          hljs.UNDERSCORE_TITLE_MODE\n        ]\n      },\n      hljs.C_NUMBER_MODE,\n      {\n        className: 'meta',\n        begin: '@[A-Za-z]+'\n      },\n      {\n        begin: '=>' // No markup, just a relevance booster\n      }\n    ]\n  };\n}\n\nmodule.exports = dart;\n"], "mappings": ";;;;;AAAA;AAAA;AAUA,aAAS,KAAK,MAAM;AAClB,YAAM,QAAQ;AAAA,QACZ,WAAW;AAAA,QACX,UAAU,CAAC;AAAA,UACT,OAAO;AAAA,QACT,CAAC;AAAA,MACH;AAEA,YAAM,eAAe;AAAA,QACnB,WAAW;AAAA,QACX,UAAU,CAAC;AAAA,UACT,OAAO;AAAA,UACP,KAAK;AAAA,QACP,CAAC;AAAA,QACD,UAAU;AAAA,MACZ;AAEA,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,SAAS;AAAA,UACX;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,SAAS;AAAA,UACX;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,cACR,KAAK;AAAA,cACL;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,cACR,KAAK;AAAA,cACL;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,SAAS;AAAA,YACT,UAAU;AAAA,cACR,KAAK;AAAA,cACL;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,SAAS;AAAA,YACT,UAAU;AAAA,cACR,KAAK;AAAA,cACL;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,mBAAa,WAAW;AAAA,QACtB,KAAK;AAAA,QACL;AAAA,MACF;AAEA,YAAM,iBAAiB;AAAA;AAAA,QAErB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QAEA;AAAA,QACA;AAAA,MACF;AACA,YAAM,0BAA0B,eAAe,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG;AAEjE,YAAM,WAAW;AAAA,QACf,SAAS;AAAA,QAIT,UACE,eACG,OAAO,uBAAuB,EAC9B,OAAO;AAAA;AAAA,UAEN;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA;AAAA,UAEA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC;AAAA,QACL,UAAU;AAAA,MACZ;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,UAAU;AAAA,QACV,UAAU;AAAA,UACR;AAAA,UACA,KAAK;AAAA,YACH;AAAA,YACA;AAAA,YACA;AAAA,cACE,aAAa;AAAA,cACb,WAAW;AAAA,YACb;AAAA,UACF;AAAA,UACA,KAAK;AAAA,YACH;AAAA,YACA;AAAA,YAAK;AAAA,cACH,UAAU,CAAC;AAAA,gBACT,aAAa;AAAA,gBACb,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,WAAW;AAAA,cACb,CAAC;AAAA,YACH;AAAA,UACF;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AAAA,YACE,WAAW;AAAA,YACX,eAAe;AAAA,YACf,KAAK;AAAA,YACL,YAAY;AAAA,YACZ,UAAU;AAAA,cACR;AAAA,gBACE,eAAe;AAAA,cACjB;AAAA,cACA,KAAK;AAAA,YACP;AAAA,UACF;AAAA,UACA,KAAK;AAAA,UACL;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,OAAO;AAAA;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}