/**
 * 📝 SESSION MANAGER
 * Gère les sessions de chat de manière isolée
 */

import { useState, useCallback, useEffect } from 'react';
import { claraDB } from '../../../db/claraDatabase';
import type { 
  ClaraChatSession, 
  ClaraMessage, 
  ClaraSessionConfig 
} from '../../../types/clara_assistant_types';

export interface SessionManagerState {
  currentSession: ClaraChatSession | null;
  sessions: ClaraChatSession[];
  messages: ClaraMessage[];
  isLoadingSessions: boolean;
  hasMoreSessions: boolean;
  sessionPage: number;
  isLoadingMoreSessions: boolean;
}

export interface SessionManagerActions {
  createNewSession: () => Promise<ClaraChatSession>;
  selectSession: (sessionId: string) => Promise<void>;
  loadMoreSessions: () => Promise<void>;
  handleSessionAction: (sessionId: string, action: 'star' | 'archive' | 'delete') => Promise<void>;
  addMessage: (message: ClaraMessage) => void;
  updateMessage: (messageId: string, updates: Partial<ClaraMessage>) => void;
  clearMessages: () => void;
}

export const useSessionManager = (sessionConfig: ClaraSessionConfig): SessionManagerState & SessionManagerActions => {
  // État des sessions
  const [currentSession, setCurrentSession] = useState<ClaraChatSession | null>(null);
  const [sessions, setSessions] = useState<ClaraChatSession[]>([]);
  const [messages, setMessages] = useState<ClaraMessage[]>([]);
  const [isLoadingSessions, setIsLoadingSessions] = useState(true);
  const [hasMoreSessions, setHasMoreSessions] = useState(true);
  const [sessionPage, setSessionPage] = useState(0);
  const [isLoadingMoreSessions, setIsLoadingMoreSessions] = useState(false);

  // Générer un ID unique
  const generateId = () => `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

  // Créer une nouvelle session
  const createNewSession = useCallback(async (): Promise<ClaraChatSession> => {
    try {
      const newSession: ClaraChatSession = {
        id: generateId(),
        title: 'New Chat',
        createdAt: new Date(),
        updatedAt: new Date(),
        messageCount: 0,
        isStarred: false,
        isArchived: false,
        config: sessionConfig
      };

      // Sauvegarder en base
      await claraDB.saveChatSession(newSession);
      
      // Mettre à jour l'état
      setSessions(prev => [newSession, ...prev]);
      setCurrentSession(newSession);
      setMessages([]);

      console.log('✅ New session created:', newSession.id);
      return newSession;
    } catch (error) {
      console.error('❌ Failed to create new session:', error);
      throw error;
    }
  }, [sessionConfig]);

  // Sélectionner une session
  const selectSession = useCallback(async (sessionId: string) => {
    if (currentSession?.id === sessionId) return;

    try {
      console.log(`🔄 Loading session: ${sessionId}`);
      
      // Charger la session
      const session = await claraDB.getChatSession(sessionId);
      if (!session) {
        console.error(`❌ Session not found: ${sessionId}`);
        return;
      }

      // Charger les messages
      const sessionMessages = await claraDB.getChatMessages(sessionId);
      
      // Mettre à jour l'état
      setCurrentSession(session);
      setMessages(sessionMessages);

      console.log(`✅ Session loaded: ${session.title} (${sessionMessages.length} messages)`);
    } catch (error) {
      console.error('❌ Failed to load session:', error);
    }
  }, [currentSession]);

  // Charger plus de sessions (pagination)
  const loadMoreSessions = useCallback(async () => {
    if (isLoadingMoreSessions || !hasMoreSessions) return;

    setIsLoadingMoreSessions(true);
    try {
      const nextPage = sessionPage + 1;
      const newSessions = await claraDB.getChatSessions(nextPage, 20);
      
      if (newSessions.length === 0) {
        setHasMoreSessions(false);
      } else {
        setSessions(prev => [...prev, ...newSessions]);
        setSessionPage(nextPage);
      }
    } catch (error) {
      console.error('❌ Failed to load more sessions:', error);
    } finally {
      setIsLoadingMoreSessions(false);
    }
  }, [isLoadingMoreSessions, hasMoreSessions, sessionPage]);

  // Actions sur les sessions
  const handleSessionAction = useCallback(async (sessionId: string, action: 'star' | 'archive' | 'delete') => {
    try {
      const session = sessions.find(s => s.id === sessionId);
      if (!session) return;

      let updatedSession: ClaraChatSession;

      switch (action) {
        case 'star':
          updatedSession = { ...session, isStarred: !session.isStarred };
          await claraDB.updateChatSession(sessionId, { isStarred: updatedSession.isStarred });
          break;
          
        case 'archive':
          updatedSession = { ...session, isArchived: !session.isArchived };
          await claraDB.updateChatSession(sessionId, { isArchived: updatedSession.isArchived });
          break;
          
        case 'delete':
          await claraDB.deleteChatSession(sessionId);
          setSessions(prev => prev.filter(s => s.id !== sessionId));
          
          // Si c'est la session courante, créer une nouvelle
          if (currentSession?.id === sessionId) {
            const newSession = await createNewSession();
            setCurrentSession(newSession);
            setMessages([]);
          }
          return;
      }

      // Mettre à jour la liste des sessions
      setSessions(prev => prev.map(s => s.id === sessionId ? updatedSession : s));
      
      // Mettre à jour la session courante si nécessaire
      if (currentSession?.id === sessionId) {
        setCurrentSession(updatedSession);
      }

      console.log(`✅ Session ${action} completed for: ${sessionId}`);
    } catch (error) {
      console.error(`❌ Failed to ${action} session:`, error);
    }
  }, [sessions, currentSession, createNewSession]);

  // Ajouter un message
  const addMessage = useCallback((message: ClaraMessage) => {
    setMessages(prev => [...prev, message]);
    
    // Mettre à jour le compteur de messages de la session
    if (currentSession) {
      const updatedSession = {
        ...currentSession,
        messageCount: currentSession.messageCount + 1,
        updatedAt: new Date()
      };
      setCurrentSession(updatedSession);
      
      // Sauvegarder en base
      claraDB.updateChatSession(currentSession.id, {
        messageCount: updatedSession.messageCount,
        updatedAt: updatedSession.updatedAt
      });
    }
  }, [currentSession]);

  // Mettre à jour un message
  const updateMessage = useCallback((messageId: string, updates: Partial<ClaraMessage>) => {
    setMessages(prev => prev.map(msg => 
      msg.id === messageId ? { ...msg, ...updates } : msg
    ));
  }, []);

  // Vider les messages
  const clearMessages = useCallback(() => {
    setMessages([]);
  }, []);

  // Charger les sessions initiales
  useEffect(() => {
    const loadInitialSessions = async () => {
      try {
        setIsLoadingSessions(true);
        const initialSessions = await claraDB.getChatSessions(0, 20);
        setSessions(initialSessions);
        setSessionPage(0);
        setHasMoreSessions(initialSessions.length === 20);
        
        console.log(`✅ Loaded ${initialSessions.length} initial sessions`);
      } catch (error) {
        console.error('❌ Failed to load initial sessions:', error);
      } finally {
        setIsLoadingSessions(false);
      }
    };

    loadInitialSessions();
  }, []);

  // Initialiser avec une nouvelle session si aucune n'existe
  useEffect(() => {
    const initializeSession = async () => {
      if (!isLoadingSessions && sessions.length === 0 && !currentSession) {
        console.log('🆕 No sessions found, creating initial session...');
        await createNewSession();
      }
    };

    initializeSession();
  }, [isLoadingSessions, sessions.length, currentSession, createNewSession]);

  return {
    // État
    currentSession,
    sessions,
    messages,
    isLoadingSessions,
    hasMoreSessions,
    sessionPage,
    isLoadingMoreSessions,
    
    // Actions
    createNewSession,
    selectSession,
    loadMoreSessions,
    handleSessionAction,
    addMessage,
    updateMessage,
    clearMessages
  };
};
