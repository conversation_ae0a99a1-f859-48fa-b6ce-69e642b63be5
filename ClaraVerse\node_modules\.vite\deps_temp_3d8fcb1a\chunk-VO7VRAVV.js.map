{"version": 3, "sources": ["../../highlight.js/lib/languages/cmake.js"], "sourcesContent": ["/*\nLanguage: CMake\nDescription: CMake is an open-source cross-platform system for build automation.\nAuthor: <PERSON> <i<PERSON>@kalnitsky.org>\nWebsite: https://cmake.org\n*/\n\n/** @type LanguageFn */\nfunction cmake(hljs) {\n  return {\n    name: 'CMake',\n    aliases: ['cmake.in'],\n    case_insensitive: true,\n    keywords: {\n      keyword:\n        // scripting commands\n        'break cmake_host_system_information cmake_minimum_required cmake_parse_arguments ' +\n        'cmake_policy configure_file continue elseif else endforeach endfunction endif endmacro ' +\n        'endwhile execute_process file find_file find_library find_package find_path ' +\n        'find_program foreach function get_cmake_property get_directory_property ' +\n        'get_filename_component get_property if include include_guard list macro ' +\n        'mark_as_advanced math message option return separate_arguments ' +\n        'set_directory_properties set_property set site_name string unset variable_watch while ' +\n        // project commands\n        'add_compile_definitions add_compile_options add_custom_command add_custom_target ' +\n        'add_definitions add_dependencies add_executable add_library add_link_options ' +\n        'add_subdirectory add_test aux_source_directory build_command create_test_sourcelist ' +\n        'define_property enable_language enable_testing export fltk_wrap_ui ' +\n        'get_source_file_property get_target_property get_test_property include_directories ' +\n        'include_external_msproject include_regular_expression install link_directories ' +\n        'link_libraries load_cache project qt_wrap_cpp qt_wrap_ui remove_definitions ' +\n        'set_source_files_properties set_target_properties set_tests_properties source_group ' +\n        'target_compile_definitions target_compile_features target_compile_options ' +\n        'target_include_directories target_link_directories target_link_libraries ' +\n        'target_link_options target_sources try_compile try_run ' +\n        // CTest commands\n        'ctest_build ctest_configure ctest_coverage ctest_empty_binary_directory ctest_memcheck ' +\n        'ctest_read_custom_files ctest_run_script ctest_sleep ctest_start ctest_submit ' +\n        'ctest_test ctest_update ctest_upload ' +\n        // deprecated commands\n        'build_name exec_program export_library_dependencies install_files install_programs ' +\n        'install_targets load_command make_directory output_required_files remove ' +\n        'subdir_depends subdirs use_mangled_mesa utility_source variable_requires write_file ' +\n        'qt5_use_modules qt5_use_package qt5_wrap_cpp ' +\n        // core keywords\n        'on off true false and or not command policy target test exists is_newer_than ' +\n        'is_directory is_symlink is_absolute matches less greater equal less_equal ' +\n        'greater_equal strless strgreater strequal strless_equal strgreater_equal version_less ' +\n        'version_greater version_equal version_less_equal version_greater_equal in_list defined'\n    },\n    contains: [\n      {\n        className: 'variable',\n        begin: /\\$\\{/,\n        end: /\\}/\n      },\n      hljs.HASH_COMMENT_MODE,\n      hljs.QUOTE_STRING_MODE,\n      hljs.NUMBER_MODE\n    ]\n  };\n}\n\nmodule.exports = cmake;\n"], "mappings": ";;;;;AAAA;AAAA;AAQA,aAAS,MAAM,MAAM;AACnB,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAC,UAAU;AAAA,QACpB,kBAAkB;AAAA,QAClB,UAAU;AAAA,UACR;AAAA;AAAA,YAEE;AAAA;AAAA,QAiCJ;AAAA,QACA,UAAU;AAAA,UACR;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,QACP;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}