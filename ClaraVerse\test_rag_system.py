#!/usr/bin/env python3
"""
🧪 SCRIPT DE TEST COMPLET DU SYSTÈME RAG + LM STUDIO
Vérifie tous les composants critiques du système
"""

import requests
import json
import time
import sys
from pathlib import Path

def test_component(name, test_func):
    """Teste un composant et affiche le résultat"""
    print(f"\n🧪 Test: {name}")
    print("-" * 50)
    try:
        start_time = time.time()
        result = test_func()
        duration = time.time() - start_time
        
        if result:
            print(f"✅ {name} - OK ({duration:.2f}s)")
            return True
        else:
            print(f"❌ {name} - ÉCHEC ({duration:.2f}s)")
            return False
    except Exception as e:
        print(f"💥 {name} - ERREUR: {e}")
        return False

def test_backend_health():
    """Test la santé du backend"""
    try:
        response = requests.get('http://localhost:8000/', timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"   Backend: {data.get('service', 'Unknown')}")
            return True
        return False
    except:
        return False

def test_lm_studio_connection():
    """Test la connexion à LM Studio"""
    try:
        # Test direct
        response = requests.get('http://localhost:1234/v1/models', timeout=5)
        if response.status_code == 200:
            models = response.json().get('data', [])
            print(f"   LM Studio direct: {len(models)} modèles")
            
            # Test via proxy
            proxy_response = requests.get('http://localhost:8000/proxy/lmstudio/models', timeout=5)
            if proxy_response.status_code == 200:
                proxy_models = proxy_response.json().get('data', [])
                print(f"   LM Studio proxy: {len(proxy_models)} modèles")
                return True
        return False
    except:
        return False

def test_rag_premium_service():
    """Test le service RAG Premium"""
    try:
        response = requests.get('http://localhost:8000/rag/status', timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"   RAG Status: {data.get('status', 'Unknown')}")
            print(f"   Documents: {data.get('total_documents', 0)}")
            return True
        return False
    except:
        return False

def test_rag_search_fast_mode():
    """Test la recherche RAG en mode rapide"""
    try:
        search_data = {
            "query": "test query",
            "use_cache": True,
            "use_lightrag": False,  # Mode rapide
            "use_vector_store": True,
            "limit": 5,
            "fast_mode": True
        }
        
        response = requests.post(
            'http://localhost:8000/rag/search',
            json=search_data,
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"   Résultats: {len(data.get('vector_results', []))}")
            print(f"   Mode rapide: {data.get('fast_mode', False)}")
            return True
        return False
    except:
        return False

def test_rag_search_performance_mode():
    """Test la recherche RAG en mode performance"""
    try:
        search_data = {
            "query": "test query",
            "use_cache": True,
            "use_lightrag": True,  # Mode performance
            "use_vector_store": True,
            "limit": 8,
            "fast_mode": False
        }
        
        response = requests.post(
            'http://localhost:8000/rag/search',
            json=search_data,
            timeout=15
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"   Résultats: {len(data.get('vector_results', []))}")
            print(f"   LightRAG: {len(data.get('combined_response', ''))}")
            return True
        return False
    except:
        return False

def test_lm_studio_chat():
    """Test le chat LM Studio via proxy"""
    try:
        chat_data = {
            "model": "qwen3-4b",
            "messages": [
                {"role": "user", "content": "Bonjour, test rapide"}
            ],
            "max_tokens": 50,
            "temperature": 0.7
        }
        
        response = requests.post(
            'http://localhost:8000/proxy/lmstudio/chat',
            json=chat_data,
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            content = data.get('choices', [{}])[0].get('message', {}).get('content', '')
            print(f"   Réponse: {content[:50]}...")
            return True
        return False
    except:
        return False

def main():
    """Fonction principale de test"""
    print("🚀 TEST COMPLET DU SYSTÈME RAG + LM STUDIO")
    print("=" * 60)
    
    tests = [
        ("Backend Health", test_backend_health),
        ("LM Studio Connection", test_lm_studio_connection),
        ("RAG Premium Service", test_rag_premium_service),
        ("RAG Search (Fast Mode)", test_rag_search_fast_mode),
        ("RAG Search (Performance Mode)", test_rag_search_performance_mode),
        ("LM Studio Chat", test_lm_studio_chat),
    ]
    
    results = []
    for name, test_func in tests:
        results.append(test_component(name, test_func))
    
    # Résumé
    print("\n" + "=" * 60)
    print("📊 RÉSUMÉ DES TESTS")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    for i, (name, _) in enumerate(tests):
        status = "✅ PASS" if results[i] else "❌ FAIL"
        print(f"{status} {name}")
    
    print(f"\n🎯 RÉSULTAT GLOBAL: {passed}/{total} tests réussis")
    
    if passed == total:
        print("🎉 TOUS LES TESTS SONT PASSÉS ! Le système est opérationnel.")
        return 0
    else:
        print("⚠️  Certains tests ont échoué. Vérifiez les services.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
