{"version": 3, "sources": ["../../highlight.js/lib/languages/clojure.js"], "sourcesContent": ["/*\nLanguage: Clojure\nDescription: Clojure syntax (based on lisp.js)\nAuthor: mfornos\nWebsite: https://clojure.org\nCategory: lisp\n*/\n\n/** @type LanguageFn */\nfunction clojure(hljs) {\n  const SYMBOLSTART = 'a-zA-Z_\\\\-!.?+*=<>&#\\'';\n  const SYMBOL_RE = '[' + SYMBOLSTART + '][' + SYMBOLSTART + '0-9/;:]*';\n  const globals = 'def defonce defprotocol defstruct defmulti defmethod defn- defn defmacro deftype defrecord';\n  const keywords = {\n    $pattern: SYMBOL_RE,\n    'builtin-name':\n      // Clojure keywords\n      globals + ' ' +\n      'cond apply if-not if-let if not not= =|0 <|0 >|0 <=|0 >=|0 ==|0 +|0 /|0 *|0 -|0 rem ' +\n      'quot neg? pos? delay? symbol? keyword? true? false? integer? empty? coll? list? ' +\n      'set? ifn? fn? associative? sequential? sorted? counted? reversible? number? decimal? ' +\n      'class? distinct? isa? float? rational? reduced? ratio? odd? even? char? seq? vector? ' +\n      'string? map? nil? contains? zero? instance? not-every? not-any? libspec? -> ->> .. . ' +\n      'inc compare do dotimes mapcat take remove take-while drop letfn drop-last take-last ' +\n      'drop-while while intern condp case reduced cycle split-at split-with repeat replicate ' +\n      'iterate range merge zipmap declare line-seq sort comparator sort-by dorun doall nthnext ' +\n      'nthrest partition eval doseq await await-for let agent atom send send-off release-pending-sends ' +\n      'add-watch mapv filterv remove-watch agent-error restart-agent set-error-handler error-handler ' +\n      'set-error-mode! error-mode shutdown-agents quote var fn loop recur throw try monitor-enter ' +\n      'monitor-exit macroexpand macroexpand-1 for dosync and or ' +\n      'when when-not when-let comp juxt partial sequence memoize constantly complement identity assert ' +\n      'peek pop doto proxy first rest cons cast coll last butlast ' +\n      'sigs reify second ffirst fnext nfirst nnext meta with-meta ns in-ns create-ns import ' +\n      'refer keys select-keys vals key val rseq name namespace promise into transient persistent! conj! ' +\n      'assoc! dissoc! pop! disj! use class type num float double short byte boolean bigint biginteger ' +\n      'bigdec print-method print-dup throw-if printf format load compile get-in update-in pr pr-on newline ' +\n      'flush read slurp read-line subvec with-open memfn time re-find re-groups rand-int rand mod locking ' +\n      'assert-valid-fdecl alias resolve ref deref refset swap! reset! set-validator! compare-and-set! alter-meta! ' +\n      'reset-meta! commute get-validator alter ref-set ref-history-count ref-min-history ref-max-history ensure sync io! ' +\n      'new next conj set! to-array future future-call into-array aset gen-class reduce map filter find empty ' +\n      'hash-map hash-set sorted-map sorted-map-by sorted-set sorted-set-by vec vector seq flatten reverse assoc dissoc list ' +\n      'disj get union difference intersection extend extend-type extend-protocol int nth delay count concat chunk chunk-buffer ' +\n      'chunk-append chunk-first chunk-rest max min dec unchecked-inc-int unchecked-inc unchecked-dec-inc unchecked-dec unchecked-negate ' +\n      'unchecked-add-int unchecked-add unchecked-subtract-int unchecked-subtract chunk-next chunk-cons chunked-seq? prn vary-meta ' +\n      'lazy-seq spread list* str find-keyword keyword symbol gensym force rationalize'\n  };\n\n  const SIMPLE_NUMBER_RE = '[-+]?\\\\d+(\\\\.\\\\d+)?';\n\n  const SYMBOL = {\n    begin: SYMBOL_RE,\n    relevance: 0\n  };\n  const NUMBER = {\n    className: 'number',\n    begin: SIMPLE_NUMBER_RE,\n    relevance: 0\n  };\n  const STRING = hljs.inherit(hljs.QUOTE_STRING_MODE, {\n    illegal: null\n  });\n  const COMMENT = hljs.COMMENT(\n    ';',\n    '$',\n    {\n      relevance: 0\n    }\n  );\n  const LITERAL = {\n    className: 'literal',\n    begin: /\\b(true|false|nil)\\b/\n  };\n  const COLLECTION = {\n    begin: '[\\\\[\\\\{]',\n    end: '[\\\\]\\\\}]'\n  };\n  const HINT = {\n    className: 'comment',\n    begin: '\\\\^' + SYMBOL_RE\n  };\n  const HINT_COL = hljs.COMMENT('\\\\^\\\\{', '\\\\}');\n  const KEY = {\n    className: 'symbol',\n    begin: '[:]{1,2}' + SYMBOL_RE\n  };\n  const LIST = {\n    begin: '\\\\(',\n    end: '\\\\)'\n  };\n  const BODY = {\n    endsWithParent: true,\n    relevance: 0\n  };\n  const NAME = {\n    keywords: keywords,\n    className: 'name',\n    begin: SYMBOL_RE,\n    relevance: 0,\n    starts: BODY\n  };\n  const DEFAULT_CONTAINS = [\n    LIST,\n    STRING,\n    HINT,\n    HINT_COL,\n    COMMENT,\n    KEY,\n    COLLECTION,\n    NUMBER,\n    LITERAL,\n    SYMBOL\n  ];\n\n  const GLOBAL = {\n    beginKeywords: globals,\n    lexemes: SYMBOL_RE,\n    end: '(\\\\[|#|\\\\d|\"|:|\\\\{|\\\\)|\\\\(|$)',\n    contains: [\n      {\n        className: 'title',\n        begin: SYMBOL_RE,\n        relevance: 0,\n        excludeEnd: true,\n        // we can only have a single title\n        endsParent: true\n      }\n    ].concat(DEFAULT_CONTAINS)\n  };\n\n  LIST.contains = [\n    hljs.COMMENT('comment', ''),\n    GLOBAL,\n    NAME,\n    BODY\n  ];\n  BODY.contains = DEFAULT_CONTAINS;\n  COLLECTION.contains = DEFAULT_CONTAINS;\n  HINT_COL.contains = [ COLLECTION ];\n\n  return {\n    name: 'Clojure',\n    aliases: [ 'clj' ],\n    illegal: /\\S/,\n    contains: [\n      LIST,\n      STRING,\n      HINT,\n      HINT_COL,\n      COMMENT,\n      KEY,\n      COLLECTION,\n      NUMBER,\n      LITERAL\n    ]\n  };\n}\n\nmodule.exports = clojure;\n"], "mappings": ";;;;;AAAA;AAAA;AASA,aAAS,QAAQ,MAAM;AACrB,YAAM,cAAc;AACpB,YAAM,YAAY,MAAM,cAAc,OAAO,cAAc;AAC3D,YAAM,UAAU;AAChB,YAAM,WAAW;AAAA,QACf,UAAU;AAAA,QACV;AAAA;AAAA,UAEE,UAAU;AAAA;AAAA,MA4Bd;AAEA,YAAM,mBAAmB;AAEzB,YAAM,SAAS;AAAA,QACb,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AACA,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AACA,YAAM,SAAS,KAAK,QAAQ,KAAK,mBAAmB;AAAA,QAClD,SAAS;AAAA,MACX,CAAC;AACD,YAAM,UAAU,KAAK;AAAA,QACnB;AAAA,QACA;AAAA,QACA;AAAA,UACE,WAAW;AAAA,QACb;AAAA,MACF;AACA,YAAM,UAAU;AAAA,QACd,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AACA,YAAM,aAAa;AAAA,QACjB,OAAO;AAAA,QACP,KAAK;AAAA,MACP;AACA,YAAM,OAAO;AAAA,QACX,WAAW;AAAA,QACX,OAAO,QAAQ;AAAA,MACjB;AACA,YAAM,WAAW,KAAK,QAAQ,UAAU,KAAK;AAC7C,YAAM,MAAM;AAAA,QACV,WAAW;AAAA,QACX,OAAO,aAAa;AAAA,MACtB;AACA,YAAM,OAAO;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,MACP;AACA,YAAM,OAAO;AAAA,QACX,gBAAgB;AAAA,QAChB,WAAW;AAAA,MACb;AACA,YAAM,OAAO;AAAA,QACX;AAAA,QACA,WAAW;AAAA,QACX,OAAO;AAAA,QACP,WAAW;AAAA,QACX,QAAQ;AAAA,MACV;AACA,YAAM,mBAAmB;AAAA,QACvB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,YAAM,SAAS;AAAA,QACb,eAAe;AAAA,QACf,SAAS;AAAA,QACT,KAAK;AAAA,QACL,UAAU;AAAA,UACR;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,WAAW;AAAA,YACX,YAAY;AAAA;AAAA,YAEZ,YAAY;AAAA,UACd;AAAA,QACF,EAAE,OAAO,gBAAgB;AAAA,MAC3B;AAEA,WAAK,WAAW;AAAA,QACd,KAAK,QAAQ,WAAW,EAAE;AAAA,QAC1B;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,WAAK,WAAW;AAChB,iBAAW,WAAW;AACtB,eAAS,WAAW,CAAE,UAAW;AAEjC,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAE,KAAM;AAAA,QACjB,SAAS;AAAA,QACT,UAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}