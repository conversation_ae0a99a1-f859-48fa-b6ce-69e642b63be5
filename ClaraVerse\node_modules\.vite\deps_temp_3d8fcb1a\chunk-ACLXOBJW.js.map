{"version": 3, "sources": ["../../refractor/lang/etlua.js"], "sourcesContent": ["'use strict'\nvar refractorLua = require('./lua.js')\nvar refractorMarkupTemplating = require('./markup-templating.js')\nmodule.exports = etlua\netlua.displayName = 'etlua'\netlua.aliases = []\nfunction etlua(Prism) {\n  Prism.register(refractorLua)\n  Prism.register(refractorMarkupTemplating)\n  ;(function (Prism) {\n    Prism.languages.etlua = {\n      delimiter: {\n        pattern: /^<%[-=]?|-?%>$/,\n        alias: 'punctuation'\n      },\n      'language-lua': {\n        pattern: /[\\s\\S]+/,\n        inside: Prism.languages.lua\n      }\n    }\n    Prism.hooks.add('before-tokenize', function (env) {\n      var pattern = /<%[\\s\\S]+?%>/g\n      Prism.languages['markup-templating'].buildPlaceholders(\n        env,\n        'etlua',\n        pattern\n      )\n    })\n    Prism.hooks.add('after-tokenize', function (env) {\n      Prism.languages['markup-templating'].tokenizePlaceholders(env, 'etlua')\n    })\n  })(Prism)\n}\n"], "mappings": ";;;;;;;;;;;AAAA;AAAA;AACA,QAAI,eAAe;AACnB,QAAI,4BAA4B;AAChC,WAAO,UAAU;AACjB,UAAM,cAAc;AACpB,UAAM,UAAU,CAAC;AACjB,aAAS,MAAM,OAAO;AACpB,YAAM,SAAS,YAAY;AAC3B,YAAM,SAAS,yBAAyB;AACvC,OAAC,SAAUA,QAAO;AACjB,QAAAA,OAAM,UAAU,QAAQ;AAAA,UACtB,WAAW;AAAA,YACT,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA,gBAAgB;AAAA,YACd,SAAS;AAAA,YACT,QAAQA,OAAM,UAAU;AAAA,UAC1B;AAAA,QACF;AACA,QAAAA,OAAM,MAAM,IAAI,mBAAmB,SAAU,KAAK;AAChD,cAAI,UAAU;AACd,UAAAA,OAAM,UAAU,mBAAmB,EAAE;AAAA,YACnC;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACF,CAAC;AACD,QAAAA,OAAM,MAAM,IAAI,kBAAkB,SAAU,KAAK;AAC/C,UAAAA,OAAM,UAAU,mBAAmB,EAAE,qBAAqB,KAAK,OAAO;AAAA,QACxE,CAAC;AAAA,MACH,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}