/**
 * 🏪 OPTIMIZED DOCUMENT STORE - Zustand-based Global State
 * 
 * Features:
 * - Intelligent memoization
 * - Selective subscriptions
 * - Performance monitoring
 * - Automatic cleanup
 * - Unified RAG integration
 */

import { create } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { DocumentInfo } from '../types/gdpr-types';

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

export interface SearchResult {
  id: string;
  content: string;
  score: number;
  metadata: {
    source_file: string;
    collection: string;
    chunk_index: number;
    [key: string]: any;
  };
}

export interface RAGSearchResponse {
  lightrag_results: string;
  vector_results: SearchResult[];
  combined_response: string;
  sources: string[];
  query: string;
  processing_time: number;
  timestamp: number;
  from_cache: boolean;
}

export interface DocumentState {
  // Document Selection
  selectedDocuments: DocumentInfo[];
  availableDocuments: DocumentInfo[];
  
  // UI State
  isDocumentPanelVisible: boolean;
  isLoading: boolean;
  error: string | null;
  
  // Search State
  lastSearchQuery: string;
  searchResults: SearchResult[];
  ragResponse: RAGSearchResponse | null;
  searchHistory: string[];
  
  // Performance Metrics
  metrics: {
    totalSearches: number;
    averageSearchTime: number;
    cacheHitRate: number;
    lastSearchTime: number;
  };
  
  // Configuration
  config: {
    maxSelectedDocuments: number;
    autoSearch: boolean;
    cacheEnabled: boolean;
    searchDebounceMs: number;
    useUnifiedRAG: boolean;
    useLightRAG: boolean;
    useVectorStore: boolean;
  };
}

export interface DocumentActions {
  // Document Management
  addDocument: (document: DocumentInfo) => void;
  removeDocument: (documentId: number | string) => void;
  clearDocuments: () => void;
  clearSelectedDocuments: () => void; // 🚀 NOUVELLE MÉTHODE
  setSession: (sessionId: string) => void; // 🚀 NOUVELLE MÉTHODE
  setAvailableDocuments: (documents: DocumentInfo[]) => void;
  loadAvailableDocuments: (forceReload?: boolean) => Promise<void>;
  reloadDocuments: () => Promise<void>;
  
  // UI Actions
  toggleDocumentPanel: () => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  // Search Actions
  searchInDocuments: (query: string) => Promise<RAGSearchResponse | null>;
  legacySearchInDocuments: (query: string) => Promise<SearchResult[]>;
  clearSearchResults: () => void;
  addToSearchHistory: (query: string) => void;
  
  // Configuration
  updateConfig: (config: Partial<DocumentState['config']>) => void;
  
  // Utilities
  reset: () => void;
  getMetrics: () => DocumentState['metrics'];
}

export type DocumentStore = DocumentState & DocumentActions;

// ============================================================================
// INITIAL STATE
// ============================================================================

const initialState: DocumentState = {
  selectedDocuments: [],
  availableDocuments: [],
  isDocumentPanelVisible: false,
  isLoading: false,
  error: null,
  lastSearchQuery: '',
  searchResults: [],
  ragResponse: null,
  searchHistory: [],
  metrics: {
    totalSearches: 0,
    averageSearchTime: 0,
    cacheHitRate: 0,
    lastSearchTime: 0
  },
  config: {
    maxSelectedDocuments: 5,
    autoSearch: true,
    cacheEnabled: true,
    searchDebounceMs: 300,
    useUnifiedRAG: true,
    useLightRAG: true,
    useVectorStore: true
  }
};

// ============================================================================
// API FUNCTIONS
// ============================================================================

const API_BASE_URL = 'http://localhost:8000';

async function callUnifiedRAGSearch(
  query: string,
  config: DocumentState['config']
): Promise<RAGSearchResponse> {
  try {
    console.log('🔍 Calling unified RAG search:', { query: query.slice(0, 50), config });

    const response = await fetch(`${API_BASE_URL}/rag/search`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query,
        use_cache: config.cacheEnabled,
        use_lightrag: config.useLightRAG,
        use_vector_store: config.useVectorStore,
        limit: 8
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('🚨 Unified RAG API error:', response.status, errorText);
      throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
    }

    const result = await response.json();
    console.log('✅ Unified RAG response:', {
      lightrag_length: result.lightrag_results?.length || 0,
      vector_results: result.vector_results?.length || 0,
      combined_length: result.combined_response?.length || 0
    });

    return result;
  } catch (error) {
    console.error('❌ Unified RAG search failed:', error);
    throw error;
  }
}

async function callLegacySearch(
  query: string,
  documents: DocumentInfo[]
): Promise<SearchResult[]> {
  console.log('🔍 Legacy search starting:', { query: query.slice(0, 50), documents: documents.length });

  // Group documents by collection
  const documentsByCollection = documents.reduce((acc, doc) => {
    if (!acc[doc.collectionName]) {
      acc[doc.collectionName] = [];
    }
    acc[doc.collectionName].push(doc.filename);
    return acc;
  }, {} as Record<string, string[]>);

  console.log('📂 Documents by collection:', documentsByCollection);

  const searchPromises = Object.entries(documentsByCollection).map(async ([collectionName, filenames]) => {
    const response = await fetch(`${API_BASE_URL}/documents/search`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query,
        collection_name: collectionName,
        k: 8,
        filter: {
          source_file: {
            $in: filenames
          }
        }
      }),
    });

    if (!response.ok) {
      console.warn(`Search failed for collection ${collectionName}:`, response.status);
      return { results: [] };
    }

    const data = await response.json();
    return data;
  });

  const results = await Promise.all(searchPromises);
  const allResults = results.flatMap(r => r.results || []);
  
  return allResults
    .map((result, index) => ({
      id: `result_${index}`,
      content: result.content,
      score: result.score || result.metadata?.score || 0,
      metadata: {
        ...result.metadata,
        chunk_index: index
      }
    }))
    .sort((a, b) => b.score - a.score)
    .slice(0, 8);
}

// ============================================================================
// STORE IMPLEMENTATION
// ============================================================================

// 🚀 ISOLATION PAR SESSION : Persistence helpers avec sessionId
let currentSessionId: string | null = null;

const setCurrentSession = (sessionId: string) => {
  currentSessionId = sessionId;
  console.log('📄 Document store session changed to:', sessionId);
};

// Initialiser une session par défaut au démarrage
const initializeDefaultSession = () => {
  if (!currentSessionId) {
    const defaultSessionId = `session-${Date.now()}`;
    setCurrentSession(defaultSessionId);
  }
};

// Initialiser immédiatement
initializeDefaultSession();

const getStorageKey = () => {
  if (!currentSessionId) {
    // Note: Session ID not yet initialized at startup - this is normal
    return 'clara-document-selections-default';
  }
  return `clara-document-selections-${currentSessionId}`;
};

const saveToStorage = (selectedDocuments: DocumentInfo[]) => {
  try {
    const key = getStorageKey();
    localStorage.setItem(key, JSON.stringify(selectedDocuments));
    console.log('💾 Saved document selections for session:', currentSessionId, 'count:', selectedDocuments.length);
  } catch (error) {
    console.warn('Failed to save document selections:', error);
  }
};

const loadFromStorage = (): DocumentInfo[] => {
  try {
    const key = getStorageKey();
    const stored = localStorage.getItem(key);
    const documents = stored ? JSON.parse(stored) : [];
    console.log('📄 Loaded document selections for session:', currentSessionId, 'count:', documents.length);
    return documents;
  } catch (error) {
    console.warn('Failed to load document selections:', error);
    return [];
  }
};

export const useDocumentStore = create<DocumentStore>()(
  devtools(
    subscribeWithSelector(
      immer((set, get) => ({
        ...initialState,
        // Load persisted selections on initialization
        selectedDocuments: loadFromStorage(),
        
        // ========================================================================
        // DOCUMENT MANAGEMENT
        // ========================================================================
        
        addDocument: (document: DocumentInfo) => {
          set((state) => {
            const isAlreadySelected = state.selectedDocuments.some(d => d.id === document.id);
            if (isAlreadySelected) {
              console.log('📄 Document already selected:', document.filename);
              return;
            }

            // Check max limit
            if (state.selectedDocuments.length >= state.config.maxSelectedDocuments) {
              // Remove oldest document
              state.selectedDocuments.shift();
              console.log('📄 Removed oldest document due to limit');
            }

            state.selectedDocuments.push(document);

            console.log('📄 Document selected:', document.filename);
            console.log('📋 Total selected:', state.selectedDocuments.length);

            // Save to localStorage
            saveToStorage(state.selectedDocuments);

            state.error = null;
          });
        },
        
        removeDocument: (documentId: number | string) => {
          set((state) => {
            console.log('🗑️ Store removeDocument called with:', documentId, 'type:', typeof documentId);
            console.log('🗑️ Current selected documents:', state.selectedDocuments.map(d => ({ id: d.id, type: typeof d.id, filename: d.filename })));

            // Use flexible comparison (== instead of ===) to handle string/number differences
            const removed = state.selectedDocuments.find(d => d.id == documentId);
            if (removed) {
              console.log('📄 Document found for removal:', removed.filename);
            } else {
              console.log('❌ Document not found for removal with ID:', documentId);
            }

            const beforeCount = state.selectedDocuments.length;
            state.selectedDocuments = state.selectedDocuments.filter(d => d.id != documentId);
            const afterCount = state.selectedDocuments.length;

            console.log('📊 Documents before removal:', beforeCount, 'after:', afterCount);

            // Save to localStorage
            saveToStorage(state.selectedDocuments);
          });
        },
        
        clearDocuments: () => {
          set((state) => {
            state.selectedDocuments = [];
            state.searchResults = [];
            state.ragResponse = null;
            state.lastSearchQuery = '';
            state.error = null;

            // Clear from localStorage
            saveToStorage([]);

            console.log('🗑️ All documents cleared');
          });
        },

        // 🗑️ NOUVELLE MÉTHODE : Désélectionner tous les documents après envoi
        clearSelectedDocuments: () => {
          set((state) => {
            const count = state.selectedDocuments.length;
            state.selectedDocuments = [];

            // Save to localStorage
            saveToStorage([]);

            console.log(`🗑️ ${count} documents désélectionnés après envoi`);
          });
        },

        // 🚀 NOUVELLE MÉTHODE : Changer de session
        setSession: (sessionId: string) => {
          console.log('🔄 Changing document store session from', currentSessionId, 'to', sessionId);

          // Sauvegarder les documents actuels avant de changer
          const currentState = get();
          if (currentSessionId) {
            saveToStorage(currentState.selectedDocuments);
          }

          // Changer de session
          setCurrentSession(sessionId);

          // Charger les documents de la nouvelle session
          const newDocuments = loadFromStorage();

          set((state) => {
            state.selectedDocuments = newDocuments;
            state.searchResults = [];
            state.ragResponse = null;
            state.lastSearchQuery = '';
            state.error = null;
          });

          console.log('✅ Session changed, loaded', newDocuments.length, 'documents for session', sessionId);
        },
        
        setAvailableDocuments: (documents: DocumentInfo[]) => {
          set((state) => {
            state.availableDocuments = documents;
            console.log('📚 Available documents updated:', documents.length);
          });
        },

        loadAvailableDocuments: async (forceReload = false) => {
          const currentState = get();

          // Prevent multiple simultaneous loads
          if (currentState.isLoading) {
            console.log('📄 Documents already loading, skipping...');
            return;
          }

          // Don't reload if we already have documents (unless forced)
          if (!forceReload && currentState.availableDocuments.length > 0) {
            console.log('📄 Documents already loaded, skipping...');
            return;
          }

          set((state) => ({ ...state, isLoading: true, error: null }));

          try {
            // 🚀 UTILISER L'ENDPOINT UNIFIÉ POUR LA SYNCHRONISATION
            const response = await fetch('http://localhost:8000/documents', {
              method: 'GET',
              headers: { 'Content-Type': 'application/json' }
            });

            if (!response.ok) {
              throw new Error(`Failed to load documents: ${response.status}`);
            }

            const data = await response.json();
            const documents = data.documents || [];

            // Formater les documents pour le store AVEC LE CONTENU
            const formattedDocuments = documents.map((doc: any) => ({
              id: doc.id,
              filename: doc.filename,
              fileType: doc.file_type || 'unknown',
              collectionName: doc.collection_name || 'default',
              chunkCount: doc.chunk_count || 0,
              isAnonymized: doc.metadata?.isAnonymized || false,
              metadata: doc.metadata || {},
              content: doc.content || '' // 🔧 AJOUT DU CONTENU !
            }));

            set((state) => ({
              ...state,
              availableDocuments: formattedDocuments,
              isLoading: false
            }));

            console.log('📄 Loaded available documents:', formattedDocuments.length);

          } catch (error) {
            console.error('❌ Failed to load available documents:', error);
            set((state) => ({
              ...state,
              error: error instanceof Error ? error.message : 'Failed to load documents',
              isLoading: false
            }));
          }
        },

        // Fonction pour forcer le rechargement (après upload/suppression)
        reloadDocuments: async () => {
          const loadAvailableDocuments = get().loadAvailableDocuments;
          await loadAvailableDocuments(true); // Force reload
        },
        
        // ========================================================================
        // UI ACTIONS
        // ========================================================================
        
        toggleDocumentPanel: () => {
          set((state) => {
            state.isDocumentPanelVisible = !state.isDocumentPanelVisible;
            console.log('👁️ Document panel:', state.isDocumentPanelVisible ? 'shown' : 'hidden');
          });
        },
        
        setLoading: (loading: boolean) => {
          set((state) => {
            state.isLoading = loading;
          });
        },
        
        setError: (error: string | null) => {
          set((state) => {
            state.error = error;
            if (error) {
              console.error('❌ Document store error:', error);
            }
          });
        },
        
        // ========================================================================
        // SEARCH ACTIONS
        // ========================================================================
        
        searchInDocuments: async (query: string) => {
          const state = get();
          
          if (!state.selectedDocuments.length) {
            set((state) => {
              state.error = 'No documents selected for search';
            });
            return null;
          }
          
          if (!query.trim()) {
            return null;
          }
          
          set((state) => {
            state.isLoading = true;
            state.error = null;
            state.lastSearchQuery = query;
          });
          
          const startTime = performance.now();
          
          try {
            console.log('🔍 Unified RAG Search:', query);
            
            let ragResponse: RAGSearchResponse;
            
            if (state.config.useUnifiedRAG) {
              // Use unified RAG service
              ragResponse = await callUnifiedRAGSearch(query, state.config);
            } else {
              // Fallback to legacy search
              const legacyResults = await callLegacySearch(query, state.selectedDocuments);
              ragResponse = {
                lightrag_results: '',
                vector_results: legacyResults,
                combined_response: legacyResults.map(r => r.content).join('\n\n'),
                sources: Array.from(new Set(legacyResults.map(r => r.metadata.source_file))),
                query,
                processing_time: performance.now() - startTime,
                timestamp: Date.now(),
                from_cache: false
              };
            }
            
            const searchTime = performance.now() - startTime;
            
            set((state) => {
              state.ragResponse = ragResponse;
              state.searchResults = ragResponse.vector_results;
              state.isLoading = false;
              state.metrics.totalSearches++;
              state.metrics.lastSearchTime = searchTime;
              state.metrics.averageSearchTime = (state.metrics.averageSearchTime + searchTime) / 2;

              if (ragResponse.from_cache) {
                state.metrics.cacheHitRate =
                  ((state.metrics.cacheHitRate * (state.metrics.totalSearches - 1)) + 100) /
                  state.metrics.totalSearches;
              }
            });
            
            // Add to search history
            get().addToSearchHistory(query);
            
            console.log(`🔍 Search completed in ${searchTime.toFixed(2)}ms:`, ragResponse.vector_results.length, 'results');
            return ragResponse;
            
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Search failed';
            
            set((state) => {
              state.isLoading = false;
              state.error = errorMessage;
              state.searchResults = [];
              state.ragResponse = null;
            });
            
            console.error('❌ Search error:', error);
            return null;
          }
        },
        
        legacySearchInDocuments: async (query: string) => {
          const state = get();
          
          if (!state.selectedDocuments.length) {
            return [];
          }
          
          try {
            return await callLegacySearch(query, state.selectedDocuments);
          } catch (error) {
            console.error('❌ Legacy search error:', error);
            return [];
          }
        },
        
        clearSearchResults: () => {
          set((state) => {
            state.searchResults = [];
            state.ragResponse = null;
            state.lastSearchQuery = '';
          });
        },
        
        addToSearchHistory: (query: string) => {
          set((state) => {
            // Remove if already exists
            const index = state.searchHistory.indexOf(query);
            if (index > -1) {
              state.searchHistory.splice(index, 1);
            }
            
            // Add to beginning
            state.searchHistory.unshift(query);
            
            // Keep only last 20 searches
            if (state.searchHistory.length > 20) {
              state.searchHistory = state.searchHistory.slice(0, 20);
            }
          });
        },
        
        // ========================================================================
        // CONFIGURATION
        // ========================================================================
        
        updateConfig: (newConfig: Partial<DocumentState['config']>) => {
          set((state) => {
            Object.assign(state.config, newConfig);
            console.log('⚙️ Config updated:', newConfig);
          });
        },
        
        // ========================================================================
        // UTILITIES
        // ========================================================================
        
        reset: () => {
          set(() => ({
            ...initialState,
            // Keep available documents
            availableDocuments: get().availableDocuments
          }));
          console.log('🔄 Document store reset');
        },
        
        getMetrics: () => {
          return get().metrics;
        }
      }))
    )
  )
);

// ============================================================================
// SELECTORS (Memoized)
// ============================================================================

// Memoized selectors to prevent infinite loops
const emptyArray: DocumentInfo[] = [];
const emptySearchResults: any[] = [];
const emptySearchHistory: any[] = [];
const defaultMetrics = {
  totalQueries: 0,
  cacheHits: 0,
  averageResponseTime: 0,
  lastQueryTime: null
};

export const useSelectedDocuments = () =>
  useDocumentStore(state => state?.selectedDocuments || emptyArray);

export const useHasDocuments = () =>
  useDocumentStore(state => (state?.selectedDocuments?.length || 0) > 0);

export const useDocumentPanelVisible = () =>
  useDocumentStore(state => state?.isDocumentPanelVisible || false);

export const useSearchResults = () =>
  useDocumentStore(state => state?.searchResults || emptySearchResults);

export const useRAGResponse = () =>
  useDocumentStore(state => state?.ragResponse || null);

export const useDocumentLoading = () =>
  useDocumentStore(state => state?.isLoading || false);

export const useDocumentError = () =>
  useDocumentStore(state => state?.error || null);

export const useDocumentMetrics = () =>
  useDocumentStore(state => state?.getMetrics ? state.getMetrics() : defaultMetrics);

export const useSearchHistory = () =>
  useDocumentStore(state => state?.searchHistory || emptySearchHistory);

// ============================================================================
// COMPUTED SELECTORS
// ============================================================================

// Memoized complex selectors
const emptyCollectionMap: Record<string, DocumentInfo[]> = {};
const emptyCollectionArray: string[] = [];

export const useDocumentsByCollection = () =>
  useDocumentStore(state => {
    const docs = state?.selectedDocuments;
    if (!docs || docs.length === 0) return emptyCollectionMap;

    return docs.reduce((acc, doc) => {
      if (!acc[doc.collectionName]) {
        acc[doc.collectionName] = [];
      }
      acc[doc.collectionName].push(doc);
      return acc;
    }, {} as Record<string, DocumentInfo[]>);
  });

export const useSelectedCollections = () =>
  useDocumentStore(state => {
    const docs = state?.selectedDocuments;
    if (!docs || docs.length === 0) return emptyCollectionArray;
    return Array.from(new Set(docs.map(doc => doc.collectionName)));
  });

export const useDocumentSummary = () =>
  useDocumentStore(state => {
    const docs = state?.selectedDocuments || emptyArray;
    return {
      totalDocuments: docs.length,
      collections: docs.length > 0 ? Array.from(new Set(docs.map(doc => doc.collectionName))) : emptyCollectionArray,
      anonymizedCount: docs.filter(doc => doc.isAnonymized).length,
      totalChunks: docs.reduce((sum, doc) => sum + doc.chunkCount, 0)
    };
  });

export default useDocumentStore;
