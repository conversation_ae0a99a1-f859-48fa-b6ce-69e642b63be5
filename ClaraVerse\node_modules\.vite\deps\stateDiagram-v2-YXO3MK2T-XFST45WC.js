import {
  StateDB,
  stateDiagram_default,
  stateRenderer_v3_unified_default,
  styles_default
} from "./chunk-XEBMPIW5.js";
import "./chunk-7NL5YDSB.js";
import "./chunk-2VBNGB3U.js";
import "./chunk-UXDJ3SXG.js";
import "./chunk-L5VB6YKJ.js";
import "./chunk-AYZICERM.js";
import "./chunk-VNMTD3NP.js";
import "./chunk-YABMZ7E6.js";
import "./chunk-HFPPC4QU.js";
import "./chunk-2H5EKQEJ.js";
import "./chunk-LQXCE4Y7.js";
import {
  __name
} from "./chunk-I7N75KF7.js";
import "./chunk-I6RL7E24.js";
import "./chunk-5RLVUMDQ.js";
import "./chunk-LTOQ36XE.js";
import "./chunk-256EKJAK.js";

// node_modules/mermaid/dist/chunks/mermaid.core/stateDiagram-v2-YXO3MK2T.mjs
var diagram = {
  parser: stateDiagram_default,
  get db() {
    return new StateDB(2);
  },
  renderer: stateRenderer_v3_unified_default,
  styles: styles_default,
  init: __name((cnf) => {
    if (!cnf.state) {
      cnf.state = {};
    }
    cnf.state.arrowMarkerAbsolute = cnf.arrowMarkerAbsolute;
  }, "init")
};
export {
  diagram
};
//# sourceMappingURL=stateDiagram-v2-YXO3MK2T-XFST45WC.js.map
