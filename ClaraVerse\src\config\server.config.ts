/**
 * 🚀 Configuration Serveur - Frontend/Backend séparés
 * Configuration pour déploiement client-serveur
 */

// Configuration par défaut (développement local)
const DEFAULT_CONFIG = {
  // Backend API
  BACKEND_URL: 'http://localhost:5001',
  
  // LM Studio (peut être sur le même serveur que le backend ou séparé)
  LM_STUDIO_URL: 'http://localhost:1234',
  
  // Timeouts
  API_TIMEOUT: 30000,
  HEALTH_CHECK_INTERVAL: 60000,
  
  // Mode
  DEVELOPMENT_MODE: true,
  DEBUG_LOGS: true
};

// Configuration production (serveur distant)
const PRODUCTION_CONFIG = {
  // Backend API - À configurer selon votre serveur
  BACKEND_URL: import.meta.env.VITE_BACKEND_URL || 'http://your-server-ip:8000',

  // LM Studio - À configurer selon votre serveur
  LM_STUDIO_URL: import.meta.env.VITE_LM_STUDIO_URL || 'http://your-server-ip:1234',
  
  // Timeouts plus longs pour réseau
  API_TIMEOUT: 60000,
  HEALTH_CHECK_INTERVAL: 120000,
  
  // Mode
  DEVELOPMENT_MODE: false,
  DEBUG_LOGS: false
};

// Détection automatique de l'environnement
const isProduction = import.meta.env.MODE === 'production';
const isServerMode = import.meta.env.VITE_SERVER_MODE === 'true';

// Configuration finale
export const SERVER_CONFIG = {
  ...(isProduction || isServerMode ? PRODUCTION_CONFIG : DEFAULT_CONFIG),
  
  // Override avec variables d'environnement
  BACKEND_URL: import.meta.env.VITE_BACKEND_URL ||
               (isProduction || isServerMode ? PRODUCTION_CONFIG.BACKEND_URL : DEFAULT_CONFIG.BACKEND_URL),

  LM_STUDIO_URL: import.meta.env.VITE_LM_STUDIO_URL ||
                 (isProduction || isServerMode ? PRODUCTION_CONFIG.LM_STUDIO_URL : DEFAULT_CONFIG.LM_STUDIO_URL),
  
  // Flags
  IS_PRODUCTION: isProduction,
  IS_SERVER_MODE: isServerMode,
  IS_LOCAL_MODE: !isProduction && !isServerMode
};

// Utilitaires
export const getBackendUrl = (endpoint: string = '') => {
  const baseUrl = SERVER_CONFIG.BACKEND_URL.replace(/\/$/, '');
  const cleanEndpoint = endpoint.replace(/^\//, '');
  return cleanEndpoint ? `${baseUrl}/${cleanEndpoint}` : baseUrl;
};

export const getLMStudioUrl = (endpoint: string = '') => {
  const baseUrl = SERVER_CONFIG.LM_STUDIO_URL.replace(/\/$/, '');
  const cleanEndpoint = endpoint.replace(/^\//, '');
  return cleanEndpoint ? `${baseUrl}/${cleanEndpoint}` : baseUrl;
};

// Validation de configuration
export const validateServerConfig = (): { valid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  if (!SERVER_CONFIG.BACKEND_URL) {
    errors.push('BACKEND_URL manquant');
  }
  
  if (!SERVER_CONFIG.LM_STUDIO_URL) {
    errors.push('LM_STUDIO_URL manquant');
  }
  
  try {
    new URL(SERVER_CONFIG.BACKEND_URL);
  } catch {
    errors.push('BACKEND_URL invalide');
  }
  
  try {
    new URL(SERVER_CONFIG.LM_STUDIO_URL);
  } catch {
    errors.push('LM_STUDIO_URL invalide');
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
};

// Test de connectivité
export const testServerConnectivity = async (): Promise<{
  backend: boolean;
  lmstudio: boolean;
  errors: string[];
}> => {
  const errors: string[] = [];
  let backend = false;
  let lmstudio = false;
  
  // Test backend
  try {
    const response = await fetch(getBackendUrl('/health'), {
      method: 'GET',
      timeout: 5000
    });
    backend = response.ok;
  } catch (error) {
    errors.push(`Backend inaccessible: ${error instanceof Error ? error.message : 'Erreur inconnue'}`);
  }
  
  // Test LM Studio
  try {
    const response = await fetch(getLMStudioUrl('/v1/models'), {
      method: 'GET',
      timeout: 5000
    });
    lmstudio = response.ok;
  } catch (error) {
    errors.push(`LM Studio inaccessible: ${error instanceof Error ? error.message : 'Erreur inconnue'}`);
  }
  
  return { backend, lmstudio, errors };
};

// Log de configuration au démarrage
if (SERVER_CONFIG.DEBUG_LOGS) {
  console.log('🚀 Configuration serveur:', {
    mode: SERVER_CONFIG.IS_PRODUCTION ? 'PRODUCTION' : 
          SERVER_CONFIG.IS_SERVER_MODE ? 'SERVER' : 'LOCAL',
    backend: SERVER_CONFIG.BACKEND_URL,
    lmstudio: SERVER_CONFIG.LM_STUDIO_URL,
    timeout: SERVER_CONFIG.API_TIMEOUT
  });
}
