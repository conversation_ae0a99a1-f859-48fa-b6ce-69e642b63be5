/**
 * 🧠 THINKING DISPLAY
 * Composant pour afficher le "thinking" de l'IA de manière déroulante
 */

import React, { useState } from 'react';
import { ChevronDown, ChevronRight, Brain, Loader2 } from 'lucide-react';

interface ThinkingDisplayProps {
  content: string;
  isActive?: boolean;
  isComplete?: boolean;
  className?: string;
}

export const ThinkingDisplay: React.FC<ThinkingDisplayProps> = ({
  content,
  isActive = false,
  isComplete = false,
  className = ''
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  // Ne pas afficher si pas de contenu
  if (!content.trim()) return null;

  // Extraire le contenu thinking (entre <thinking> et </thinking>)
  const thinkingMatch = content.match(/<thinking>(.*?)<\/thinking>/s);
  const thinkingContent = thinkingMatch ? thinkingMatch[1].trim() : '';

  // Ne pas afficher si pas de thinking
  if (!thinkingContent) return null;

  // Compter les lignes pour l'aperçu
  const lines = thinkingContent.split('\n');
  const previewLines = lines.slice(0, 2).join('\n');
  const hasMoreLines = lines.length > 2;

  return (
    <div className={`bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3 mb-3 ${className}`}>
      {/* Header avec toggle */}
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="flex items-center gap-2 w-full text-left hover:bg-blue-100 dark:hover:bg-blue-800/30 rounded p-1 transition-colors"
      >
        {/* Icône de toggle */}
        {isExpanded ? (
          <ChevronDown className="w-4 h-4 text-blue-600 dark:text-blue-400 flex-shrink-0" />
        ) : (
          <ChevronRight className="w-4 h-4 text-blue-600 dark:text-blue-400 flex-shrink-0" />
        )}

        {/* Icône thinking */}
        <div className="flex items-center gap-1">
          {isActive && !isComplete ? (
            <Loader2 className="w-4 h-4 text-blue-600 dark:text-blue-400 animate-spin" />
          ) : (
            <Brain className="w-4 h-4 text-blue-600 dark:text-blue-400" />
          )}
          <span className="text-sm font-medium text-blue-700 dark:text-blue-300">
            {isActive && !isComplete ? 'Thinking...' : 'AI Reasoning'}
          </span>
        </div>

        {/* Indicateur de contenu */}
        {!isExpanded && (
          <span className="text-xs text-blue-500 dark:text-blue-400 ml-auto">
            {lines.length} line{lines.length > 1 ? 's' : ''}
          </span>
        )}
      </button>

      {/* Aperçu quand fermé */}
      {!isExpanded && (
        <div className="mt-2 pl-6">
          <div className="text-sm text-blue-600 dark:text-blue-300 font-mono bg-blue-100 dark:bg-blue-800/30 rounded p-2 border-l-2 border-blue-300 dark:border-blue-600">
            {previewLines}
            {hasMoreLines && (
              <span className="text-blue-400 dark:text-blue-500">...</span>
            )}
          </div>
        </div>
      )}

      {/* Contenu complet quand ouvert */}
      {isExpanded && (
        <div className="mt-2 pl-6">
          <div className="text-sm text-blue-700 dark:text-blue-200 font-mono bg-blue-100 dark:bg-blue-800/30 rounded p-3 border-l-2 border-blue-300 dark:border-blue-600 max-h-60 overflow-y-auto">
            <pre className="whitespace-pre-wrap break-words">
              {thinkingContent}
            </pre>
          </div>
          
          {/* Métadonnées */}
          <div className="mt-2 flex items-center gap-4 text-xs text-blue-500 dark:text-blue-400">
            <span>{lines.length} lines</span>
            <span>{thinkingContent.length} characters</span>
            {isComplete && (
              <span className="flex items-center gap-1">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                Complete
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

/**
 * Hook pour extraire et gérer le contenu thinking
 */
export const useThinkingExtractor = (content: string) => {
  // Extraire le thinking
  const thinkingMatch = content.match(/<thinking>(.*?)<\/thinking>/s);
  const thinkingContent = thinkingMatch ? thinkingMatch[1].trim() : '';
  
  // Extraire le contenu sans thinking
  const contentWithoutThinking = content
    .replace(/<thinking>.*?<\/thinking>/s, '')
    .trim();
  
  return {
    hasThinking: !!thinkingContent,
    thinkingContent,
    contentWithoutThinking,
    isThinkingComplete: content.includes('</thinking>')
  };
};

export default ThinkingDisplay;
