{"version": 3, "sources": ["../../refractor/lang/eiffel.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = eiffel\neiffel.displayName = 'eiffel'\neiffel.aliases = []\nfunction eiffel(Prism) {\n  Prism.languages.eiffel = {\n    comment: /--.*/,\n    string: [\n      // Aligned-verbatim-strings\n      {\n        pattern: /\"([^[]*)\\[[\\s\\S]*?\\]\\1\"/,\n        greedy: true\n      }, // Non-aligned-verbatim-strings\n      {\n        pattern: /\"([^{]*)\\{[\\s\\S]*?\\}\\1\"/,\n        greedy: true\n      }, // Single-line string\n      {\n        pattern: /\"(?:%(?:(?!\\n)\\s)*\\n\\s*%|%\\S|[^%\"\\r\\n])*\"/,\n        greedy: true\n      }\n    ],\n    // normal char | special char | char code\n    char: /'(?:%.|[^%'\\r\\n])+'/,\n    keyword:\n      /\\b(?:across|agent|alias|all|and|as|assign|attached|attribute|check|class|convert|create|Current|debug|deferred|detachable|do|else|elseif|end|ensure|expanded|export|external|feature|from|frozen|if|implies|inherit|inspect|invariant|like|local|loop|not|note|obsolete|old|once|or|Precursor|redefine|rename|require|rescue|Result|retry|select|separate|some|then|undefine|until|variant|Void|when|xor)\\b/i,\n    boolean: /\\b(?:False|True)\\b/i,\n    // Convention: class-names are always all upper-case characters\n    'class-name': /\\b[A-Z][\\dA-Z_]*\\b/,\n    number: [\n      // hexa | octal | bin\n      /\\b0[xcb][\\da-f](?:_*[\\da-f])*\\b/i, // Decimal\n      /(?:\\b\\d(?:_*\\d)*)?\\.(?:(?:\\d(?:_*\\d)*)?e[+-]?)?\\d(?:_*\\d)*\\b|\\b\\d(?:_*\\d)*\\b\\.?/i\n    ],\n    punctuation: /:=|<<|>>|\\(\\||\\|\\)|->|\\.(?=\\w)|[{}[\\];(),:?]/,\n    operator: /\\\\\\\\|\\|\\.\\.\\||\\.\\.|\\/[~\\/=]?|[><]=?|[-+*^=~]/\n  }\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,WAAO,cAAc;AACrB,WAAO,UAAU,CAAC;AAClB,aAAS,OAAO,OAAO;AACrB,YAAM,UAAU,SAAS;AAAA,QACvB,SAAS;AAAA,QACT,QAAQ;AAAA;AAAA,UAEN;AAAA,YACE,SAAS;AAAA,YACT,QAAQ;AAAA,UACV;AAAA;AAAA,UACA;AAAA,YACE,SAAS;AAAA,YACT,QAAQ;AAAA,UACV;AAAA;AAAA,UACA;AAAA,YACE,SAAS;AAAA,YACT,QAAQ;AAAA,UACV;AAAA,QACF;AAAA;AAAA,QAEA,MAAM;AAAA,QACN,SACE;AAAA,QACF,SAAS;AAAA;AAAA,QAET,cAAc;AAAA,QACd,QAAQ;AAAA;AAAA,UAEN;AAAA;AAAA,UACA;AAAA,QACF;AAAA,QACA,aAAa;AAAA,QACb,UAAU;AAAA,MACZ;AAAA,IACF;AAAA;AAAA;", "names": []}