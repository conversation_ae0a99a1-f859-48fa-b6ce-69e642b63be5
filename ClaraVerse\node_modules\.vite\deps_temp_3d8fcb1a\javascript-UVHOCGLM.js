import {
  conf,
  language
} from "./chunk-XPKFL32A.js";
import "./chunk-R4PW6EOI.js";
import "./chunk-OL46QLBJ.js";

// node_modules/monaco-editor/esm/vs/basic-languages/javascript/javascript.js
var conf2 = conf;
var language2 = {
  // Set defaultToken to invalid to see what you do not tokenize yet
  defaultToken: "invalid",
  tokenPostfix: ".js",
  keywords: [
    "break",
    "case",
    "catch",
    "class",
    "continue",
    "const",
    "constructor",
    "debugger",
    "default",
    "delete",
    "do",
    "else",
    "export",
    "extends",
    "false",
    "finally",
    "for",
    "from",
    "function",
    "get",
    "if",
    "import",
    "in",
    "instanceof",
    "let",
    "new",
    "null",
    "return",
    "set",
    "static",
    "super",
    "switch",
    "symbol",
    "this",
    "throw",
    "true",
    "try",
    "typeof",
    "undefined",
    "var",
    "void",
    "while",
    "with",
    "yield",
    "async",
    "await",
    "of"
  ],
  typeKeywords: [],
  operators: language.operators,
  symbols: language.symbols,
  escapes: language.escapes,
  digits: language.digits,
  octaldigits: language.octaldigits,
  binarydigits: language.binarydigits,
  hexdigits: language.hexdigits,
  regexpctl: language.regexpctl,
  regexpesc: language.regexpesc,
  tokenizer: language.tokenizer
};
export {
  conf2 as conf,
  language2 as language
};
/*! Bundled license information:

monaco-editor/esm/vs/basic-languages/javascript/javascript.js:
  (*!-----------------------------------------------------------------------------
   * Copyright (c) Microsoft Corporation. All rights reserved.
   * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)
   * Released under the MIT license
   * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt
   *-----------------------------------------------------------------------------*)
*/
//# sourceMappingURL=javascript-UVHOCGLM.js.map
