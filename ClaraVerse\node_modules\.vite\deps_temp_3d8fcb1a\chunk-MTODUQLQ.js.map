{"version": 3, "sources": ["../../highlight.js/lib/languages/taggerscript.js"], "sourcesContent": ["/*\nLanguage: Tagger Script\nAuthor: <PERSON> <<EMAIL>>\nDescription: Syntax Highlighting for the Tagger Script as used by MusicBrainz Picard.\nWebsite: https://picard.musicbrainz.org\n */\nfunction taggerscript(hljs) {\n  const COMMENT = {\n    className: 'comment',\n    begin: /\\$noop\\(/,\n    end: /\\)/,\n    contains: [ {\n      begin: /\\(/,\n      end: /\\)/,\n      contains: [ 'self',\n        {\n          begin: /\\\\./\n        } ]\n    } ],\n    relevance: 10\n  };\n\n  const FUNCTION = {\n    className: 'keyword',\n    begin: /\\$(?!noop)[a-zA-Z][_a-zA-Z0-9]*/,\n    end: /\\(/,\n    excludeEnd: true\n  };\n\n  const VARIABLE = {\n    className: 'variable',\n    begin: /%[_a-zA-Z0-9:]*/,\n    end: '%'\n  };\n\n  const ESCAPE_SEQUENCE = {\n    className: 'symbol',\n    begin: /\\\\./\n  };\n\n  return {\n    name: 'Tagger Script',\n    contains: [\n      COMMENT,\n      FUNCTION,\n      VARIABLE,\n      ESCAPE_SEQUENCE\n    ]\n  };\n}\n\nmodule.exports = taggerscript;\n"], "mappings": ";;;;;AAAA;AAAA;AAMA,aAAS,aAAa,MAAM;AAC1B,YAAM,UAAU;AAAA,QACd,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU,CAAE;AAAA,UACV,OAAO;AAAA,UACP,KAAK;AAAA,UACL,UAAU;AAAA,YAAE;AAAA,YACV;AAAA,cACE,OAAO;AAAA,YACT;AAAA,UAAE;AAAA,QACN,CAAE;AAAA,QACF,WAAW;AAAA,MACb;AAEA,YAAM,WAAW;AAAA,QACf,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,YAAY;AAAA,MACd;AAEA,YAAM,WAAW;AAAA,QACf,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,MACP;AAEA,YAAM,kBAAkB;AAAA,QACtB,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,UAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}