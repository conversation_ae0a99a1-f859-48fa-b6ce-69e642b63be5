{"version": 3, "sources": ["../../refractor/lang/n4js.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = n4js\nn4js.displayName = 'n4js'\nn4js.aliases = ['n4jsd']\nfunction n4js(Prism) {\n  Prism.languages.n4js = Prism.languages.extend('javascript', {\n    // Keywords from N4JS language spec: https://numberfour.github.io/n4js/spec/N4JSSpec.html\n    keyword:\n      /\\b(?:Array|any|boolean|break|case|catch|class|const|constructor|continue|debugger|declare|default|delete|do|else|enum|export|extends|false|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|module|new|null|number|package|private|protected|public|return|set|static|string|super|switch|this|throw|true|try|typeof|var|void|while|with|yield)\\b/\n  })\n  Prism.languages.insertBefore('n4js', 'constant', {\n    // Annotations in N4JS spec: https://numberfour.github.io/n4js/spec/N4JSSpec.html#_annotations\n    annotation: {\n      pattern: /@+\\w+/,\n      alias: 'operator'\n    }\n  })\n  Prism.languages.n4jsd = Prism.languages.n4js\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,SAAK,cAAc;AACnB,SAAK,UAAU,CAAC,OAAO;AACvB,aAAS,KAAK,OAAO;AACnB,YAAM,UAAU,OAAO,MAAM,UAAU,OAAO,cAAc;AAAA;AAAA,QAE1D,SACE;AAAA,MACJ,CAAC;AACD,YAAM,UAAU,aAAa,QAAQ,YAAY;AAAA;AAAA,QAE/C,YAAY;AAAA,UACV,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,MACF,CAAC;AACD,YAAM,UAAU,QAAQ,MAAM,UAAU;AAAA,IAC1C;AAAA;AAAA;", "names": []}