{"version": 3, "sources": ["../../refractor/lang/jsdoc.js"], "sourcesContent": ["'use strict'\nvar refractorJavadoclike = require('./javadoclike.js')\nvar refractorTypescript = require('./typescript.js')\nmodule.exports = jsdoc\njsdoc.displayName = 'jsdoc'\njsdoc.aliases = []\nfunction jsdoc(Prism) {\n  Prism.register(refractorJavadoclike)\n  Prism.register(refractorTypescript)\n  ;(function (Prism) {\n    var javascript = Prism.languages.javascript\n    var type = /\\{(?:[^{}]|\\{(?:[^{}]|\\{[^{}]*\\})*\\})+\\}/.source\n    var parameterPrefix =\n      '(@(?:arg|argument|param|property)\\\\s+(?:' + type + '\\\\s+)?)'\n    Prism.languages.jsdoc = Prism.languages.extend('javadoclike', {\n      parameter: {\n        // @param {string} foo - foo bar\n        pattern: RegExp(\n          parameterPrefix + /(?:(?!\\s)[$\\w\\xA0-\\uFFFF.])+(?=\\s|$)/.source\n        ),\n        lookbehind: true,\n        inside: {\n          punctuation: /\\./\n        }\n      }\n    })\n    Prism.languages.insertBefore('jsdoc', 'keyword', {\n      'optional-parameter': {\n        // @param {string} [baz.foo=\"bar\"] foo bar\n        pattern: RegExp(\n          parameterPrefix +\n            /\\[(?:(?!\\s)[$\\w\\xA0-\\uFFFF.])+(?:=[^[\\]]+)?\\](?=\\s|$)/.source\n        ),\n        lookbehind: true,\n        inside: {\n          parameter: {\n            pattern: /(^\\[)[$\\w\\xA0-\\uFFFF\\.]+/,\n            lookbehind: true,\n            inside: {\n              punctuation: /\\./\n            }\n          },\n          code: {\n            pattern: /(=)[\\s\\S]*(?=\\]$)/,\n            lookbehind: true,\n            inside: javascript,\n            alias: 'language-javascript'\n          },\n          punctuation: /[=[\\]]/\n        }\n      },\n      'class-name': [\n        {\n          pattern: RegExp(\n            /(@(?:augments|class|extends|interface|memberof!?|template|this|typedef)\\s+(?:<TYPE>\\s+)?)[A-Z]\\w*(?:\\.[A-Z]\\w*)*/.source.replace(\n              /<TYPE>/g,\n              function () {\n                return type\n              }\n            )\n          ),\n          lookbehind: true,\n          inside: {\n            punctuation: /\\./\n          }\n        },\n        {\n          pattern: RegExp('(@[a-z]+\\\\s+)' + type),\n          lookbehind: true,\n          inside: {\n            string: javascript.string,\n            number: javascript.number,\n            boolean: javascript.boolean,\n            keyword: Prism.languages.typescript.keyword,\n            operator: /=>|\\.\\.\\.|[&|?:*]/,\n            punctuation: /[.,;=<>{}()[\\]]/\n          }\n        }\n      ],\n      example: {\n        pattern:\n          /(@example\\s+(?!\\s))(?:[^@\\s]|\\s+(?!\\s))+?(?=\\s*(?:\\*\\s*)?(?:@\\w|\\*\\/))/,\n        lookbehind: true,\n        inside: {\n          code: {\n            pattern: /^([\\t ]*(?:\\*\\s*)?)\\S.*$/m,\n            lookbehind: true,\n            inside: javascript,\n            alias: 'language-javascript'\n          }\n        }\n      }\n    })\n    Prism.languages.javadoclike.addSupport('javascript', Prism.languages.jsdoc)\n  })(Prism)\n}\n"], "mappings": ";;;;;;;;;;;AAAA;AAAA;AACA,QAAI,uBAAuB;AAC3B,QAAI,sBAAsB;AAC1B,WAAO,UAAU;AACjB,UAAM,cAAc;AACpB,UAAM,UAAU,CAAC;AACjB,aAAS,MAAM,OAAO;AACpB,YAAM,SAAS,oBAAoB;AACnC,YAAM,SAAS,mBAAmB;AACjC,OAAC,SAAUA,QAAO;AACjB,YAAI,aAAaA,OAAM,UAAU;AACjC,YAAI,OAAO,2CAA2C;AACtD,YAAI,kBACF,6CAA6C,OAAO;AACtD,QAAAA,OAAM,UAAU,QAAQA,OAAM,UAAU,OAAO,eAAe;AAAA,UAC5D,WAAW;AAAA;AAAA,YAET,SAAS;AAAA,cACP,kBAAkB,uCAAuC;AAAA,YAC3D;AAAA,YACA,YAAY;AAAA,YACZ,QAAQ;AAAA,cACN,aAAa;AAAA,YACf;AAAA,UACF;AAAA,QACF,CAAC;AACD,QAAAA,OAAM,UAAU,aAAa,SAAS,WAAW;AAAA,UAC/C,sBAAsB;AAAA;AAAA,YAEpB,SAAS;AAAA,cACP,kBACE,wDAAwD;AAAA,YAC5D;AAAA,YACA,YAAY;AAAA,YACZ,QAAQ;AAAA,cACN,WAAW;AAAA,gBACT,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,QAAQ;AAAA,kBACN,aAAa;AAAA,gBACf;AAAA,cACF;AAAA,cACA,MAAM;AAAA,gBACJ,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,QAAQ;AAAA,gBACR,OAAO;AAAA,cACT;AAAA,cACA,aAAa;AAAA,YACf;AAAA,UACF;AAAA,UACA,cAAc;AAAA,YACZ;AAAA,cACE,SAAS;AAAA,gBACP,mHAAmH,OAAO;AAAA,kBACxH;AAAA,kBACA,WAAY;AACV,2BAAO;AAAA,kBACT;AAAA,gBACF;AAAA,cACF;AAAA,cACA,YAAY;AAAA,cACZ,QAAQ;AAAA,gBACN,aAAa;AAAA,cACf;AAAA,YACF;AAAA,YACA;AAAA,cACE,SAAS,OAAO,kBAAkB,IAAI;AAAA,cACtC,YAAY;AAAA,cACZ,QAAQ;AAAA,gBACN,QAAQ,WAAW;AAAA,gBACnB,QAAQ,WAAW;AAAA,gBACnB,SAAS,WAAW;AAAA,gBACpB,SAASA,OAAM,UAAU,WAAW;AAAA,gBACpC,UAAU;AAAA,gBACV,aAAa;AAAA,cACf;AAAA,YACF;AAAA,UACF;AAAA,UACA,SAAS;AAAA,YACP,SACE;AAAA,YACF,YAAY;AAAA,YACZ,QAAQ;AAAA,cACN,MAAM;AAAA,gBACJ,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,QAAQ;AAAA,gBACR,OAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AAAA,QACF,CAAC;AACD,QAAAA,OAAM,UAAU,YAAY,WAAW,cAAcA,OAAM,UAAU,KAAK;AAAA,MAC5E,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}