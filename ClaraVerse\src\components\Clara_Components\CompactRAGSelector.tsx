import React, { useState, useEffect, useRef } from 'react';
import { Database, FileText, ChevronDown, Search, X, Plus } from 'lucide-react';
import { useDocumentStore } from '../../stores/documentStore';
import { DocumentInfo } from '../../types/gdpr-types';

interface CompactRAGSelectorProps {
  selectedDocumentsCount: number;
  onDocumentSelect: (document: DocumentInfo) => void;
  onDocumentRemove: (documentId: string | number) => void;
  className?: string;
}

const CompactRAGSelector: React.FC<CompactRAGSelectorProps> = ({
  selectedDocumentsCount,
  onDocumentSelect,
  onDocumentRemove,
  className = ''
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const dropdownRef = useRef<HTMLDivElement>(null);
  
  const { availableDocuments, selectedDocuments, loadAvailableDocuments } = useDocumentStore();

  // Charger les documents disponibles
  useEffect(() => {
    if (isOpen && availableDocuments.length === 0) {
      loadAvailableDocuments();
    }
  }, [isOpen, availableDocuments.length, loadAvailableDocuments]);

  // Fermer le dropdown quand on clique ailleurs
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Filtrer les documents par recherche
  const filteredDocuments = availableDocuments.filter(doc =>
    doc.filename.toLowerCase().includes(searchTerm.toLowerCase()) ||
    doc.collectionName.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Grouper par collection
  const documentsByCollection = filteredDocuments.reduce((acc, doc) => {
    if (!acc[doc.collectionName]) {
      acc[doc.collectionName] = [];
    }
    acc[doc.collectionName].push(doc);
    return acc;
  }, {} as Record<string, DocumentInfo[]>);

  const handleDocumentClick = (document: DocumentInfo) => {
    const isSelected = selectedDocuments.some(d => d.id === document.id);
    
    if (isSelected) {
      onDocumentRemove(document.id);
    } else {
      onDocumentSelect(document);
    }
    
    // Fermer le dropdown après sélection
    setIsOpen(false);
    setSearchTerm('');
  };

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* Bouton RAG */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`relative p-2 rounded-lg transition-all duration-200 ${
          selectedDocumentsCount > 0
            ? 'bg-wema-100 dark:bg-wema-900/30 text-wema-600 dark:text-wema-400 ring-1 ring-wema-300 dark:ring-wema-600'
            : isOpen
            ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400'
            : 'hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-600 dark:text-gray-400'
        }`}
        title={`RAG Documents ${selectedDocumentsCount > 0 ? `(${selectedDocumentsCount} selected)` : '- Select documents to chat with'}`}
      >
        <Database className="w-4 h-4" />
        {selectedDocumentsCount > 0 && (
          <span className="absolute -top-1 -right-1 bg-wema-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-semibold">
            {selectedDocumentsCount}
          </span>
        )}
      </button>

      {/* Dropdown compact */}
      {isOpen && (
        <div className="absolute top-full right-0 mt-2 w-80 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50 max-h-96 overflow-hidden">
          {/* Header avec recherche */}
          <div className="p-3 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center gap-2 mb-2">
              <Database className="w-4 h-4 text-wema-600 dark:text-wema-400" />
              <span className="font-medium text-sm text-gray-900 dark:text-white">
                Documents RAG
              </span>
              <span className="text-xs text-gray-500 dark:text-gray-400">
                ({availableDocuments.length} disponibles)
              </span>
            </div>
            
            {/* Barre de recherche */}
            <div className="relative">
              <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 w-3 h-3 text-gray-400" />
              <input
                type="text"
                placeholder="Rechercher un document..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-7 pr-8 py-1.5 text-xs border border-gray-200 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-wema-500 focus:border-wema-500"
              />
              {searchTerm && (
                <button
                  onClick={() => setSearchTerm('')}
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  <X className="w-3 h-3" />
                </button>
              )}
            </div>
          </div>

          {/* Liste des documents */}
          <div className="max-h-64 overflow-y-auto">
            {Object.keys(documentsByCollection).length === 0 ? (
              <div className="p-4 text-center text-gray-500 dark:text-gray-400 text-sm">
                {searchTerm ? 'Aucun document trouvé' : 'Aucun document disponible'}
              </div>
            ) : (
              Object.entries(documentsByCollection).map(([collectionName, docs]) => (
                <div key={collectionName} className="border-b border-gray-100 dark:border-gray-700 last:border-b-0">
                  {/* Collection header */}
                  <div className="px-3 py-2 bg-gray-50 dark:bg-gray-750 text-xs font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wide">
                    {collectionName} ({docs.length})
                  </div>
                  
                  {/* Documents */}
                  <div className="space-y-0">
                    {docs.map((doc) => {
                      const isSelected = selectedDocuments.some(d => d.id === doc.id);
                      return (
                        <button
                          key={doc.id}
                          onClick={() => handleDocumentClick(doc)}
                          className={`w-full flex items-center gap-2 px-3 py-2 text-left hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
                            isSelected
                              ? 'bg-wema-50 dark:bg-wema-900/20 text-wema-700 dark:text-wema-300 border-l-2 border-wema-500'
                              : 'text-gray-700 dark:text-gray-300'
                          }`}
                        >
                          <div className={`w-2 h-2 rounded-full flex-shrink-0 ${
                            isSelected ? 'bg-wema-500' : 'bg-gray-300 dark:bg-gray-600'
                          }`} />
                          <FileText className="w-3 h-3 text-gray-400 flex-shrink-0" />
                          <div className="flex-1 min-w-0">
                            <div className="text-xs font-medium truncate">
                              {doc.filename}
                            </div>
                            <div className="text-xs text-gray-500 dark:text-gray-400">
                              {doc.fileType.toUpperCase()}
                            </div>
                          </div>
                          {isSelected && (
                            <div className="flex-shrink-0 text-wema-500">
                              <Plus className="w-3 h-3 rotate-45" />
                            </div>
                          )}
                        </button>
                      );
                    })}
                  </div>
                </div>
              ))
            )}
          </div>

          {/* Footer avec documents sélectionnés */}
          {selectedDocumentsCount > 0 && (
            <div className="p-2 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-750">
              <div className="text-xs text-wema-600 dark:text-wema-400 font-medium">
                {selectedDocumentsCount} document{selectedDocumentsCount !== 1 ? 's' : ''} sélectionné{selectedDocumentsCount !== 1 ? 's' : ''}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default CompactRAGSelector;
