{"version": 3, "sources": ["../../highlight.js/lib/languages/mizar.js"], "sourcesContent": ["/*\nLanguage: Mizar\nDescription: The Mizar Language is a formal language derived from the mathematical vernacular.\nAuthor: <PERSON> <<EMAIL>>\nWebsite: http://mizar.org/language/\nCategory: scientific\n*/\n\nfunction mizar(hljs) {\n  return {\n    name: '<PERSON>zar',\n    keywords:\n      'environ vocabularies notations constructors definitions ' +\n      'registrations theorems schemes requirements begin end definition ' +\n      'registration cluster existence pred func defpred deffunc theorem ' +\n      'proof let take assume then thus hence ex for st holds consider ' +\n      'reconsider such that and in provided of as from be being by means ' +\n      'equals implies iff redefine define now not or attr is mode ' +\n      'suppose per cases set thesis contradiction scheme reserve struct ' +\n      'correctness compatibility coherence symmetry assymetry ' +\n      'reflexivity irreflexivity connectedness uniqueness commutativity ' +\n      'idempotence involutiveness projectivity',\n    contains: [\n      hljs.COMMENT('::', '$')\n    ]\n  };\n}\n\nmodule.exports = mizar;\n"], "mappings": ";;;;;AAAA;AAAA;AAQA,aAAS,MAAM,MAAM;AACnB,aAAO;AAAA,QACL,MAAM;AAAA,QACN,UACE;AAAA,QAUF,UAAU;AAAA,UACR,KAAK,QAAQ,MAAM,GAAG;AAAA,QACxB;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}