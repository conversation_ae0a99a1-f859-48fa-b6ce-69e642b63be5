{"version": 3, "sources": ["../../highlight.js/lib/languages/vbnet.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/**\n * Any of the passed expresssions may match\n *\n * Creates a huge this | this | that | that match\n * @param {(RegExp | string)[] } args\n * @returns {string}\n */\nfunction either(...args) {\n  const joined = '(' + args.map((x) => source(x)).join(\"|\") + \")\";\n  return joined;\n}\n\n/*\nLanguage: Visual Basic .NET\nDescription: Visual Basic .NET (VB.NET) is a multi-paradigm, object-oriented programming language, implemented on the .NET Framework.\nAuthors: <AUTHORS>\nWebsite: https://docs.microsoft.com/dotnet/visual-basic/getting-started\nCategory: common\n*/\n\n/** @type LanguageFn */\nfunction vbnet(hljs) {\n  /**\n   * Character Literal\n   * Either a single character (\"a\"C) or an escaped double quote (\"\"\"\"C).\n   */\n  const CHARACTER = {\n    className: 'string',\n    begin: /\"(\"\"|[^/n])\"C\\b/\n  };\n\n  const STRING = {\n    className: 'string',\n    begin: /\"/,\n    end: /\"/,\n    illegal: /\\n/,\n    contains: [\n      {\n        // double quote escape\n        begin: /\"\"/\n      }\n    ]\n  };\n\n  /** Date Literals consist of a date, a time, or both separated by whitespace, surrounded by # */\n  const MM_DD_YYYY = /\\d{1,2}\\/\\d{1,2}\\/\\d{4}/;\n  const YYYY_MM_DD = /\\d{4}-\\d{1,2}-\\d{1,2}/;\n  const TIME_12H = /(\\d|1[012])(:\\d+){0,2} *(AM|PM)/;\n  const TIME_24H = /\\d{1,2}(:\\d{1,2}){1,2}/;\n  const DATE = {\n    className: 'literal',\n    variants: [\n      {\n        // #YYYY-MM-DD# (ISO-Date) or #M/D/YYYY# (US-Date)\n        begin: concat(/# */, either(YYYY_MM_DD, MM_DD_YYYY), / *#/)\n      },\n      {\n        // #H:mm[:ss]# (24h Time)\n        begin: concat(/# */, TIME_24H, / *#/)\n      },\n      {\n        // #h[:mm[:ss]] A# (12h Time)\n        begin: concat(/# */, TIME_12H, / *#/)\n      },\n      {\n        // date plus time\n        begin: concat(\n          /# */,\n          either(YYYY_MM_DD, MM_DD_YYYY),\n          / +/,\n          either(TIME_12H, TIME_24H),\n          / *#/\n        )\n      }\n    ]\n  };\n\n  const NUMBER = {\n    className: 'number',\n    relevance: 0,\n    variants: [\n      {\n        // Float\n        begin: /\\b\\d[\\d_]*((\\.[\\d_]+(E[+-]?[\\d_]+)?)|(E[+-]?[\\d_]+))[RFD@!#]?/\n      },\n      {\n        // Integer (base 10)\n        begin: /\\b\\d[\\d_]*((U?[SIL])|[%&])?/\n      },\n      {\n        // Integer (base 16)\n        begin: /&H[\\dA-F_]+((U?[SIL])|[%&])?/\n      },\n      {\n        // Integer (base 8)\n        begin: /&O[0-7_]+((U?[SIL])|[%&])?/\n      },\n      {\n        // Integer (base 2)\n        begin: /&B[01_]+((U?[SIL])|[%&])?/\n      }\n    ]\n  };\n\n  const LABEL = {\n    className: 'label',\n    begin: /^\\w+:/\n  };\n\n  const DOC_COMMENT = hljs.COMMENT(/'''/, /$/, {\n    contains: [\n      {\n        className: 'doctag',\n        begin: /<\\/?/,\n        end: />/\n      }\n    ]\n  });\n\n  const COMMENT = hljs.COMMENT(null, /$/, {\n    variants: [\n      {\n        begin: /'/\n      },\n      {\n        // TODO: Use `beforeMatch:` for leading spaces\n        begin: /([\\t ]|^)REM(?=\\s)/\n      }\n    ]\n  });\n\n  const DIRECTIVES = {\n    className: 'meta',\n    // TODO: Use `beforeMatch:` for indentation once available\n    begin: /[\\t ]*#(const|disable|else|elseif|enable|end|externalsource|if|region)\\b/,\n    end: /$/,\n    keywords: {\n      'meta-keyword':\n        'const disable else elseif enable end externalsource if region then'\n    },\n    contains: [ COMMENT ]\n  };\n\n  return {\n    name: 'Visual Basic .NET',\n    aliases: [ 'vb' ],\n    case_insensitive: true,\n    classNameAliases: {\n      label: 'symbol'\n    },\n    keywords: {\n      keyword:\n        'addhandler alias aggregate ansi as async assembly auto binary by byref byval ' + /* a-b */\n        'call case catch class compare const continue custom declare default delegate dim distinct do ' + /* c-d */\n        'each equals else elseif end enum erase error event exit explicit finally for friend from function ' + /* e-f */\n        'get global goto group handles if implements imports in inherits interface into iterator ' + /* g-i */\n        'join key let lib loop me mid module mustinherit mustoverride mybase myclass ' + /* j-m */\n        'namespace narrowing new next notinheritable notoverridable ' + /* n */\n        'of off on operator option optional order overloads overridable overrides ' + /* o */\n        'paramarray partial preserve private property protected public ' + /* p */\n        'raiseevent readonly redim removehandler resume return ' + /* r */\n        'select set shadows shared skip static step stop structure strict sub synclock ' + /* s */\n        'take text then throw to try unicode until using when where while widening with withevents writeonly yield' /* t-y */,\n      built_in:\n        // Operators https://docs.microsoft.com/dotnet/visual-basic/language-reference/operators\n        'addressof and andalso await directcast gettype getxmlnamespace is isfalse isnot istrue like mod nameof new not or orelse trycast typeof xor ' +\n        // Type Conversion Functions https://docs.microsoft.com/dotnet/visual-basic/language-reference/functions/type-conversion-functions\n        'cbool cbyte cchar cdate cdbl cdec cint clng cobj csbyte cshort csng cstr cuint culng cushort',\n      type:\n        // Data types https://docs.microsoft.com/dotnet/visual-basic/language-reference/data-types\n        'boolean byte char date decimal double integer long object sbyte short single string uinteger ulong ushort',\n      literal: 'true false nothing'\n    },\n    illegal:\n      '//|\\\\{|\\\\}|endif|gosub|variant|wend|^\\\\$ ' /* reserved deprecated keywords */,\n    contains: [\n      CHARACTER,\n      STRING,\n      DATE,\n      NUMBER,\n      LABEL,\n      DOC_COMMENT,\n      COMMENT,\n      DIRECTIVES\n    ]\n  };\n}\n\nmodule.exports = vbnet;\n"], "mappings": ";;;;;AAAA;AAAA;AASA,aAAS,OAAO,IAAI;AAClB,UAAI,CAAC,GAAI,QAAO;AAChB,UAAI,OAAO,OAAO,SAAU,QAAO;AAEnC,aAAO,GAAG;AAAA,IACZ;AAMA,aAAS,UAAU,MAAM;AACvB,YAAM,SAAS,KAAK,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE;AACjD,aAAO;AAAA,IACT;AASA,aAAS,UAAU,MAAM;AACvB,YAAM,SAAS,MAAM,KAAK,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC,EAAE,KAAK,GAAG,IAAI;AAC5D,aAAO;AAAA,IACT;AAWA,aAAS,MAAM,MAAM;AAKnB,YAAM,YAAY;AAAA,QAChB,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AAEA,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,SAAS;AAAA,QACT,UAAU;AAAA,UACR;AAAA;AAAA,YAEE,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAGA,YAAM,aAAa;AACnB,YAAM,aAAa;AACnB,YAAM,WAAW;AACjB,YAAM,WAAW;AACjB,YAAM,OAAO;AAAA,QACX,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA;AAAA,YAEE,OAAO,OAAO,OAAO,OAAO,YAAY,UAAU,GAAG,KAAK;AAAA,UAC5D;AAAA,UACA;AAAA;AAAA,YAEE,OAAO,OAAO,OAAO,UAAU,KAAK;AAAA,UACtC;AAAA,UACA;AAAA;AAAA,YAEE,OAAO,OAAO,OAAO,UAAU,KAAK;AAAA,UACtC;AAAA,UACA;AAAA;AAAA,YAEE,OAAO;AAAA,cACL;AAAA,cACA,OAAO,YAAY,UAAU;AAAA,cAC7B;AAAA,cACA,OAAO,UAAU,QAAQ;AAAA,cACzB;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA;AAAA,YAEE,OAAO;AAAA,UACT;AAAA,UACA;AAAA;AAAA,YAEE,OAAO;AAAA,UACT;AAAA,UACA;AAAA;AAAA,YAEE,OAAO;AAAA,UACT;AAAA,UACA;AAAA;AAAA,YAEE,OAAO;AAAA,UACT;AAAA,UACA;AAAA;AAAA,YAEE,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAEA,YAAM,QAAQ;AAAA,QACZ,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AAEA,YAAM,cAAc,KAAK,QAAQ,OAAO,KAAK;AAAA,QAC3C,UAAU;AAAA,UACR;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,QACF;AAAA,MACF,CAAC;AAED,YAAM,UAAU,KAAK,QAAQ,MAAM,KAAK;AAAA,QACtC,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,UACT;AAAA,UACA;AAAA;AAAA,YAEE,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF,CAAC;AAED,YAAM,aAAa;AAAA,QACjB,WAAW;AAAA;AAAA,QAEX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,UACR,gBACE;AAAA,QACJ;AAAA,QACA,UAAU,CAAE,OAAQ;AAAA,MACtB;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAE,IAAK;AAAA,QAChB,kBAAkB;AAAA,QAClB,kBAAkB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,UAAU;AAAA,UACR,SACE;AAAA,UAWF;AAAA;AAAA,YAEE;AAAA;AAAA,UAGF;AAAA;AAAA,YAEE;AAAA;AAAA,UACF,SAAS;AAAA,QACX;AAAA,QACA,SACE;AAAA,QACF,UAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}