{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/objective-c/objective-c.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/objective-c/objective-c.ts\nvar conf = {\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".objective-c\",\n  keywords: [\n    \"#import\",\n    \"#include\",\n    \"#define\",\n    \"#else\",\n    \"#endif\",\n    \"#if\",\n    \"#ifdef\",\n    \"#ifndef\",\n    \"#ident\",\n    \"#undef\",\n    \"@class\",\n    \"@defs\",\n    \"@dynamic\",\n    \"@encode\",\n    \"@end\",\n    \"@implementation\",\n    \"@interface\",\n    \"@package\",\n    \"@private\",\n    \"@protected\",\n    \"@property\",\n    \"@protocol\",\n    \"@public\",\n    \"@selector\",\n    \"@synthesize\",\n    \"__declspec\",\n    \"assign\",\n    \"auto\",\n    \"BOOL\",\n    \"break\",\n    \"bycopy\",\n    \"byref\",\n    \"case\",\n    \"char\",\n    \"Class\",\n    \"const\",\n    \"copy\",\n    \"continue\",\n    \"default\",\n    \"do\",\n    \"double\",\n    \"else\",\n    \"enum\",\n    \"extern\",\n    \"FALSE\",\n    \"false\",\n    \"float\",\n    \"for\",\n    \"goto\",\n    \"if\",\n    \"in\",\n    \"int\",\n    \"id\",\n    \"inout\",\n    \"IMP\",\n    \"long\",\n    \"nil\",\n    \"nonatomic\",\n    \"NULL\",\n    \"oneway\",\n    \"out\",\n    \"private\",\n    \"public\",\n    \"protected\",\n    \"readwrite\",\n    \"readonly\",\n    \"register\",\n    \"return\",\n    \"SEL\",\n    \"self\",\n    \"short\",\n    \"signed\",\n    \"sizeof\",\n    \"static\",\n    \"struct\",\n    \"super\",\n    \"switch\",\n    \"typedef\",\n    \"TRUE\",\n    \"true\",\n    \"union\",\n    \"unsigned\",\n    \"volatile\",\n    \"void\",\n    \"while\"\n  ],\n  decpart: /\\d(_?\\d)*/,\n  decimal: /0|@decpart/,\n  tokenizer: {\n    root: [\n      { include: \"@comments\" },\n      { include: \"@whitespace\" },\n      { include: \"@numbers\" },\n      { include: \"@strings\" },\n      [/[,:;]/, \"delimiter\"],\n      [/[{}\\[\\]()<>]/, \"@brackets\"],\n      [\n        /[a-zA-Z@#]\\w*/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      [/[<>=\\\\+\\\\-\\\\*\\\\/\\\\^\\\\|\\\\~,]|and\\\\b|or\\\\b|not\\\\b]/, \"operator\"]\n    ],\n    whitespace: [[/\\s+/, \"white\"]],\n    comments: [\n      [\"\\\\/\\\\*\", \"comment\", \"@comment\"],\n      [\"\\\\/\\\\/+.*\", \"comment\"]\n    ],\n    comment: [\n      [\"\\\\*\\\\/\", \"comment\", \"@pop\"],\n      [\".\", \"comment\"]\n    ],\n    numbers: [\n      [/0[xX][0-9a-fA-F]*(_?[0-9a-fA-F])*/, \"number.hex\"],\n      [\n        /@decimal((\\.@decpart)?([eE][\\-+]?@decpart)?)[fF]*/,\n        {\n          cases: {\n            \"(\\\\d)*\": \"number\",\n            $0: \"number.float\"\n          }\n        }\n      ]\n    ],\n    // Recognize strings, including those broken across lines with \\ (but not without)\n    strings: [\n      [/'$/, \"string.escape\", \"@popall\"],\n      [/'/, \"string.escape\", \"@stringBody\"],\n      [/\"$/, \"string.escape\", \"@popall\"],\n      [/\"/, \"string.escape\", \"@dblStringBody\"]\n    ],\n    stringBody: [\n      [/[^\\\\']+$/, \"string\", \"@popall\"],\n      [/[^\\\\']+/, \"string\"],\n      [/\\\\./, \"string\"],\n      [/'/, \"string.escape\", \"@popall\"],\n      [/\\\\$/, \"string\"]\n    ],\n    dblStringBody: [\n      [/[^\\\\\"]+$/, \"string\", \"@popall\"],\n      [/[^\\\\\"]+/, \"string\"],\n      [/\\\\./, \"string\"],\n      [/\"/, \"string.escape\", \"@popall\"],\n      [/\\\\$/, \"string\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;AASA,IAAI,OAAO;AAAA,EACT,UAAU;AAAA,IACR,aAAa;AAAA,IACb,cAAc,CAAC,MAAM,IAAI;AAAA,EAC3B;AAAA,EACA,UAAU;AAAA,IACR,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,cAAc;AAAA,EACd,UAAU;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,SAAS;AAAA,EACT,SAAS;AAAA,EACT,WAAW;AAAA,IACT,MAAM;AAAA,MACJ,EAAE,SAAS,YAAY;AAAA,MACvB,EAAE,SAAS,cAAc;AAAA,MACzB,EAAE,SAAS,WAAW;AAAA,MACtB,EAAE,SAAS,WAAW;AAAA,MACtB,CAAC,SAAS,WAAW;AAAA,MACrB,CAAC,gBAAgB,WAAW;AAAA,MAC5B;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,aAAa;AAAA,YACb,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,oDAAoD,UAAU;AAAA,IACjE;AAAA,IACA,YAAY,CAAC,CAAC,OAAO,OAAO,CAAC;AAAA,IAC7B,UAAU;AAAA,MACR,CAAC,UAAU,WAAW,UAAU;AAAA,MAChC,CAAC,aAAa,SAAS;AAAA,IACzB;AAAA,IACA,SAAS;AAAA,MACP,CAAC,UAAU,WAAW,MAAM;AAAA,MAC5B,CAAC,KAAK,SAAS;AAAA,IACjB;AAAA,IACA,SAAS;AAAA,MACP,CAAC,qCAAqC,YAAY;AAAA,MAClD;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,UAAU;AAAA,YACV,IAAI;AAAA,UACN;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA;AAAA,IAEA,SAAS;AAAA,MACP,CAAC,MAAM,iBAAiB,SAAS;AAAA,MACjC,CAAC,KAAK,iBAAiB,aAAa;AAAA,MACpC,CAAC,MAAM,iBAAiB,SAAS;AAAA,MACjC,CAAC,KAAK,iBAAiB,gBAAgB;AAAA,IACzC;AAAA,IACA,YAAY;AAAA,MACV,CAAC,YAAY,UAAU,SAAS;AAAA,MAChC,CAAC,WAAW,QAAQ;AAAA,MACpB,CAAC,OAAO,QAAQ;AAAA,MAChB,CAAC,KAAK,iBAAiB,SAAS;AAAA,MAChC,CAAC,OAAO,QAAQ;AAAA,IAClB;AAAA,IACA,eAAe;AAAA,MACb,CAAC,YAAY,UAAU,SAAS;AAAA,MAChC,CAAC,WAAW,QAAQ;AAAA,MACpB,CAAC,OAAO,QAAQ;AAAA,MAChB,CAAC,KAAK,iBAAiB,SAAS;AAAA,MAChC,CAAC,OAAO,QAAQ;AAAA,IAClB;AAAA,EACF;AACF;", "names": []}