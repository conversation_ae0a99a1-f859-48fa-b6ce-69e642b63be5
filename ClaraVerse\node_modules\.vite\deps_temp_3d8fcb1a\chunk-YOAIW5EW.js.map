{"version": 3, "sources": ["../../highlight.js/lib/languages/makefile.js"], "sourcesContent": ["/*\nLanguage: Makefile\nAuthor: <PERSON> <<EMAIL>>\nContributors: <PERSON><PERSON> <<EMAIL>>\nWebsite: https://www.gnu.org/software/make/manual/html_node/Introduction.html\nCategory: common\n*/\n\nfunction makefile(hljs) {\n  /* Variables: simple (eg $(var)) and special (eg $@) */\n  const VARIABLE = {\n    className: 'variable',\n    variants: [\n      {\n        begin: '\\\\$\\\\(' + hljs.UNDERSCORE_IDENT_RE + '\\\\)',\n        contains: [ hljs.BACKSLASH_ESCAPE ]\n      },\n      {\n        begin: /\\$[@%<?\\^\\+\\*]/\n      }\n    ]\n  };\n  /* Quoted string with variables inside */\n  const QUOTE_STRING = {\n    className: 'string',\n    begin: /\"/,\n    end: /\"/,\n    contains: [\n      hljs.BACKSLASH_ESCAPE,\n      VARIABLE\n    ]\n  };\n  /* Function: $(func arg,...) */\n  const FUNC = {\n    className: 'variable',\n    begin: /\\$\\([\\w-]+\\s/,\n    end: /\\)/,\n    keywords: {\n      built_in:\n        'subst patsubst strip findstring filter filter-out sort ' +\n        'word wordlist firstword lastword dir notdir suffix basename ' +\n        'addsuffix addprefix join wildcard realpath abspath error warning ' +\n        'shell origin flavor foreach if or and call eval file value'\n    },\n    contains: [ VARIABLE ]\n  };\n  /* Variable assignment */\n  const ASSIGNMENT = {\n    begin: '^' + hljs.UNDERSCORE_IDENT_RE + '\\\\s*(?=[:+?]?=)'\n  };\n  /* Meta targets (.PHONY) */\n  const META = {\n    className: 'meta',\n    begin: /^\\.PHONY:/,\n    end: /$/,\n    keywords: {\n      $pattern: /[\\.\\w]+/,\n      'meta-keyword': '.PHONY'\n    }\n  };\n  /* Targets */\n  const TARGET = {\n    className: 'section',\n    begin: /^[^\\s]+:/,\n    end: /$/,\n    contains: [ VARIABLE ]\n  };\n  return {\n    name: 'Makefile',\n    aliases: [\n      'mk',\n      'mak',\n      'make',\n    ],\n    keywords: {\n      $pattern: /[\\w-]+/,\n      keyword: 'define endef undefine ifdef ifndef ifeq ifneq else endif ' +\n      'include -include sinclude override export unexport private vpath'\n    },\n    contains: [\n      hljs.HASH_COMMENT_MODE,\n      VARIABLE,\n      QUOTE_STRING,\n      FUNC,\n      ASSIGNMENT,\n      META,\n      TARGET\n    ]\n  };\n}\n\nmodule.exports = makefile;\n"], "mappings": ";;;;;AAAA;AAAA;AAQA,aAAS,SAAS,MAAM;AAEtB,YAAM,WAAW;AAAA,QACf,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,OAAO,WAAW,KAAK,sBAAsB;AAAA,YAC7C,UAAU,CAAE,KAAK,gBAAiB;AAAA,UACpC;AAAA,UACA;AAAA,YACE,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAEA,YAAM,eAAe;AAAA,QACnB,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,UACR,KAAK;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAEA,YAAM,OAAO;AAAA,QACX,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,UACR,UACE;AAAA,QAIJ;AAAA,QACA,UAAU,CAAE,QAAS;AAAA,MACvB;AAEA,YAAM,aAAa;AAAA,QACjB,OAAO,MAAM,KAAK,sBAAsB;AAAA,MAC1C;AAEA,YAAM,OAAO;AAAA,QACX,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,UACR,UAAU;AAAA,UACV,gBAAgB;AAAA,QAClB;AAAA,MACF;AAEA,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU,CAAE,QAAS;AAAA,MACvB;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,UACP;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,UAAU;AAAA,UACR,UAAU;AAAA,UACV,SAAS;AAAA,QAEX;AAAA,QACA,UAAU;AAAA,UACR,KAAK;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}