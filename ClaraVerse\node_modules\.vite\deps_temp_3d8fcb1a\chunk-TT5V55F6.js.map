{"version": 3, "sources": ["../../refractor/lang/unrealscript.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = unrealscript\nunrealscript.displayName = 'unrealscript'\nunrealscript.aliases = ['uc', 'uscript']\nfunction unrealscript(Prism) {\n  Prism.languages.unrealscript = {\n    comment: /\\/\\/.*|\\/\\*[\\s\\S]*?\\*\\//,\n    string: {\n      pattern: /([\"'])(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\1)[^\\\\\\r\\n])*\\1/,\n      greedy: true\n    },\n    category: {\n      pattern:\n        /(\\b(?:(?:autoexpand|hide|show)categories|var)\\s*\\()[^()]+(?=\\))/,\n      lookbehind: true,\n      greedy: true,\n      alias: 'property'\n    },\n    metadata: {\n      pattern: /(\\w\\s*)<\\s*\\w+\\s*=[^<>|=\\r\\n]+(?:\\|\\s*\\w+\\s*=[^<>|=\\r\\n]+)*>/,\n      lookbehind: true,\n      greedy: true,\n      inside: {\n        property: /\\b\\w+(?=\\s*=)/,\n        operator: /=/,\n        punctuation: /[<>|]/\n      }\n    },\n    macro: {\n      pattern: /`\\w+/,\n      alias: 'property'\n    },\n    'class-name': {\n      pattern:\n        /(\\b(?:class|enum|extends|interface|state(?:\\(\\))?|struct|within)\\s+)\\w+/,\n      lookbehind: true\n    },\n    keyword:\n      /\\b(?:abstract|actor|array|auto|autoexpandcategories|bool|break|byte|case|class|classgroup|client|coerce|collapsecategories|config|const|continue|default|defaultproperties|delegate|dependson|deprecated|do|dontcollapsecategories|editconst|editinlinenew|else|enum|event|exec|export|extends|final|float|for|forcescriptorder|foreach|function|goto|guid|hidecategories|hidedropdown|if|ignores|implements|inherits|input|int|interface|iterator|latent|local|material|name|native|nativereplication|noexport|nontransient|noteditinlinenew|notplaceable|operator|optional|out|pawn|perobjectconfig|perobjectlocalized|placeable|postoperator|preoperator|private|protected|reliable|replication|return|server|showcategories|simulated|singular|state|static|string|struct|structdefault|structdefaultproperties|switch|texture|transient|travel|unreliable|until|var|vector|while|within)\\b/,\n    function: /\\b[a-z_]\\w*(?=\\s*\\()/i,\n    boolean: /\\b(?:false|true)\\b/,\n    number: /\\b0x[\\da-f]+\\b|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?/i,\n    // https://docs.unrealengine.com/udk/Three/UnrealScriptExpressions.html\n    operator:\n      />>|<<|--|\\+\\+|\\*\\*|[-+*/~!=<>$@]=?|&&?|\\|\\|?|\\^\\^?|[?:%]|\\b(?:ClockwiseFrom|Cross|Dot)\\b/,\n    punctuation: /[()[\\]{};,.]/\n  }\n  Prism.languages.uc = Prism.languages.uscript = Prism.languages.unrealscript\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,iBAAa,cAAc;AAC3B,iBAAa,UAAU,CAAC,MAAM,SAAS;AACvC,aAAS,aAAa,OAAO;AAC3B,YAAM,UAAU,eAAe;AAAA,QAC7B,SAAS;AAAA,QACT,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,UAAU;AAAA,UACR,SACE;AAAA,UACF,YAAY;AAAA,UACZ,QAAQ;AAAA,UACR,OAAO;AAAA,QACT;AAAA,QACA,UAAU;AAAA,UACR,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,QAAQ;AAAA,UACR,QAAQ;AAAA,YACN,UAAU;AAAA,YACV,UAAU;AAAA,YACV,aAAa;AAAA,UACf;AAAA,QACF;AAAA,QACA,OAAO;AAAA,UACL,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,cAAc;AAAA,UACZ,SACE;AAAA,UACF,YAAY;AAAA,QACd;AAAA,QACA,SACE;AAAA,QACF,UAAU;AAAA,QACV,SAAS;AAAA,QACT,QAAQ;AAAA;AAAA,QAER,UACE;AAAA,QACF,aAAa;AAAA,MACf;AACA,YAAM,UAAU,KAAK,MAAM,UAAU,UAAU,MAAM,UAAU;AAAA,IACjE;AAAA;AAAA;", "names": []}