{"version": 3, "sources": ["../../highlight.js/lib/languages/vbscript.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/**\n * Any of the passed expresssions may match\n *\n * Creates a huge this | this | that | that match\n * @param {(RegExp | string)[] } args\n * @returns {string}\n */\nfunction either(...args) {\n  const joined = '(' + args.map((x) => source(x)).join(\"|\") + \")\";\n  return joined;\n}\n\n/*\nLanguage: VBScript\nDescription: VBScript (\"Microsoft Visual Basic Scripting Edition\") is an Active Scripting language developed by Microsoft that is modeled on Visual Basic.\nAuthor: <PERSON><PERSON> <<EMAIL>>\nContributors: <PERSON><PERSON> <<EMAIL>>\nWebsite: https://en.wikipedia.org/wiki/VBScript\nCategory: scripting\n*/\n\n/** @type LanguageFn */\nfunction vbscript(hljs) {\n  const BUILT_IN_FUNCTIONS = ('lcase month vartype instrrev ubound setlocale getobject rgb getref string ' +\n  'weekdayname rnd dateadd monthname now day minute isarray cbool round formatcurrency ' +\n  'conversions csng timevalue second year space abs clng timeserial fixs len asc ' +\n  'isempty maths dateserial atn timer isobject filter weekday datevalue ccur isdate ' +\n  'instr datediff formatdatetime replace isnull right sgn array snumeric log cdbl hex ' +\n  'chr lbound msgbox ucase getlocale cos cdate cbyte rtrim join hour oct typename trim ' +\n  'strcomp int createobject loadpicture tan formatnumber mid ' +\n  'split  cint sin datepart ltrim sqr ' +\n  'time derived eval date formatpercent exp inputbox left ascw ' +\n  'chrw regexp cstr err').split(\" \");\n  const BUILT_IN_OBJECTS = [\n    \"server\",\n    \"response\",\n    \"request\",\n    // take no arguments so can be called without ()\n    \"scriptengine\",\n    \"scriptenginebuildversion\",\n    \"scriptengineminorversion\",\n    \"scriptenginemajorversion\"\n  ];\n\n  const BUILT_IN_CALL = {\n    begin: concat(either(...BUILT_IN_FUNCTIONS), \"\\\\s*\\\\(\"),\n    // relevance 0 because this is acting as a beginKeywords really\n    relevance:0,\n    keywords: {\n      built_in: BUILT_IN_FUNCTIONS\n    }\n  };\n\n  return {\n    name: 'VBScript',\n    aliases: ['vbs'],\n    case_insensitive: true,\n    keywords: {\n      keyword:\n        'call class const dim do loop erase execute executeglobal exit for each next function ' +\n        'if then else on error option explicit new private property let get public randomize ' +\n        'redim rem select case set stop sub while wend with end to elseif is or xor and not ' +\n        'class_initialize class_terminate default preserve in me byval byref step resume goto',\n      built_in: BUILT_IN_OBJECTS,\n      literal:\n        'true false null nothing empty'\n    },\n    illegal: '//',\n    contains: [\n      BUILT_IN_CALL,\n      hljs.inherit(hljs.QUOTE_STRING_MODE, {contains: [{begin: '\"\"'}]}),\n      hljs.COMMENT(\n        /'/,\n        /$/,\n        {\n          relevance: 0\n        }\n      ),\n      hljs.C_NUMBER_MODE\n    ]\n  };\n}\n\nmodule.exports = vbscript;\n"], "mappings": ";;;;;AAAA;AAAA;AASA,aAAS,OAAO,IAAI;AAClB,UAAI,CAAC,GAAI,QAAO;AAChB,UAAI,OAAO,OAAO,SAAU,QAAO;AAEnC,aAAO,GAAG;AAAA,IACZ;AAMA,aAAS,UAAU,MAAM;AACvB,YAAM,SAAS,KAAK,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE;AACjD,aAAO;AAAA,IACT;AASA,aAAS,UAAU,MAAM;AACvB,YAAM,SAAS,MAAM,KAAK,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC,EAAE,KAAK,GAAG,IAAI;AAC5D,aAAO;AAAA,IACT;AAYA,aAAS,SAAS,MAAM;AACtB,YAAM,qBAAsB,opBASJ,MAAM,GAAG;AACjC,YAAM,mBAAmB;AAAA,QACvB;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QAEA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,YAAM,gBAAgB;AAAA,QACpB,OAAO,OAAO,OAAO,GAAG,kBAAkB,GAAG,SAAS;AAAA;AAAA,QAEtD,WAAU;AAAA,QACV,UAAU;AAAA,UACR,UAAU;AAAA,QACZ;AAAA,MACF;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAC,KAAK;AAAA,QACf,kBAAkB;AAAA,QAClB,UAAU;AAAA,UACR,SACE;AAAA,UAIF,UAAU;AAAA,UACV,SACE;AAAA,QACJ;AAAA,QACA,SAAS;AAAA,QACT,UAAU;AAAA,UACR;AAAA,UACA,KAAK,QAAQ,KAAK,mBAAmB,EAAC,UAAU,CAAC,EAAC,OAAO,KAAI,CAAC,EAAC,CAAC;AAAA,UAChE,KAAK;AAAA,YACH;AAAA,YACA;AAAA,YACA;AAAA,cACE,WAAW;AAAA,YACb;AAAA,UACF;AAAA,UACA,KAAK;AAAA,QACP;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}