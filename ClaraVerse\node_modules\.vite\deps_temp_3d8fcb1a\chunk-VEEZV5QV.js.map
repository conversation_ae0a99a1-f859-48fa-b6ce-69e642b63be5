{"version": 3, "sources": ["../../refractor/lang/lilypond.js"], "sourcesContent": ["'use strict'\nvar refractorScheme = require('./scheme.js')\nmodule.exports = lilypond\nlilypond.displayName = 'lilypond'\nlilypond.aliases = []\nfunction lilypond(Prism) {\n  Prism.register(refractorScheme)\n  ;(function (Prism) {\n    var schemeExpression =\n      /\\((?:[^();\"#\\\\]|\\\\[\\s\\S]|;.*(?!.)|\"(?:[^\"\\\\]|\\\\.)*\"|#(?:\\{(?:(?!#\\})[\\s\\S])*#\\}|[^{])|<expr>)*\\)/\n        .source // allow for up to pow(2, recursivenessLog2) many levels of recursive brace expressions\n    // For some reason, this can't be 4\n    var recursivenessLog2 = 5\n    for (var i = 0; i < recursivenessLog2; i++) {\n      schemeExpression = schemeExpression.replace(/<expr>/g, function () {\n        return schemeExpression\n      })\n    }\n    schemeExpression = schemeExpression.replace(/<expr>/g, /[^\\s\\S]/.source)\n    var lilypond = (Prism.languages.lilypond = {\n      comment: /%(?:(?!\\{).*|\\{[\\s\\S]*?%\\})/,\n      'embedded-scheme': {\n        pattern: RegExp(\n          /(^|[=\\s])#(?:\"(?:[^\"\\\\]|\\\\.)*\"|[^\\s()\"]*(?:[^\\s()]|<expr>))/.source.replace(\n            /<expr>/g,\n            function () {\n              return schemeExpression\n            }\n          ),\n          'm'\n        ),\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          scheme: {\n            pattern: /^(#)[\\s\\S]+$/,\n            lookbehind: true,\n            alias: 'language-scheme',\n            inside: {\n              'embedded-lilypond': {\n                pattern: /#\\{[\\s\\S]*?#\\}/,\n                greedy: true,\n                inside: {\n                  punctuation: /^#\\{|#\\}$/,\n                  lilypond: {\n                    pattern: /[\\s\\S]+/,\n                    alias: 'language-lilypond',\n                    inside: null // see below\n                  }\n                }\n              },\n              rest: Prism.languages.scheme\n            }\n          },\n          punctuation: /#/\n        }\n      },\n      string: {\n        pattern: /\"(?:[^\"\\\\]|\\\\.)*\"/,\n        greedy: true\n      },\n      'class-name': {\n        pattern: /(\\\\new\\s+)[\\w-]+/,\n        lookbehind: true\n      },\n      keyword: {\n        pattern: /\\\\[a-z][-\\w]*/i,\n        inside: {\n          punctuation: /^\\\\/\n        }\n      },\n      operator: /[=|]|<<|>>/,\n      punctuation: {\n        pattern:\n          /(^|[a-z\\d])(?:'+|,+|[_^]?-[_^]?(?:[-+^!>._]|(?=\\d))|[_^]\\.?|[.!])|[{}()[\\]<>^~]|\\\\[()[\\]<>\\\\!]|--|__/,\n        lookbehind: true\n      },\n      number: /\\b\\d+(?:\\/\\d+)?\\b/\n    })\n    lilypond['embedded-scheme'].inside['scheme'].inside[\n      'embedded-lilypond'\n    ].inside['lilypond'].inside = lilypond\n    Prism.languages.ly = lilypond\n  })(Prism)\n}\n"], "mappings": ";;;;;;;;AAAA;AAAA;AACA,QAAI,kBAAkB;AACtB,WAAO,UAAU;AACjB,aAAS,cAAc;AACvB,aAAS,UAAU,CAAC;AACpB,aAAS,SAAS,OAAO;AACvB,YAAM,SAAS,eAAe;AAC7B,OAAC,SAAUA,QAAO;AACjB,YAAI,mBACF,mGACG;AAEL,YAAI,oBAAoB;AACxB,iBAAS,IAAI,GAAG,IAAI,mBAAmB,KAAK;AAC1C,6BAAmB,iBAAiB,QAAQ,WAAW,WAAY;AACjE,mBAAO;AAAA,UACT,CAAC;AAAA,QACH;AACA,2BAAmB,iBAAiB,QAAQ,WAAW,UAAU,MAAM;AACvE,YAAIC,YAAYD,OAAM,UAAU,WAAW;AAAA,UACzC,SAAS;AAAA,UACT,mBAAmB;AAAA,YACjB,SAAS;AAAA,cACP,8DAA8D,OAAO;AAAA,gBACnE;AAAA,gBACA,WAAY;AACV,yBAAO;AAAA,gBACT;AAAA,cACF;AAAA,cACA;AAAA,YACF;AAAA,YACA,YAAY;AAAA,YACZ,QAAQ;AAAA,YACR,QAAQ;AAAA,cACN,QAAQ;AAAA,gBACN,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,OAAO;AAAA,gBACP,QAAQ;AAAA,kBACN,qBAAqB;AAAA,oBACnB,SAAS;AAAA,oBACT,QAAQ;AAAA,oBACR,QAAQ;AAAA,sBACN,aAAa;AAAA,sBACb,UAAU;AAAA,wBACR,SAAS;AAAA,wBACT,OAAO;AAAA,wBACP,QAAQ;AAAA;AAAA,sBACV;AAAA,oBACF;AAAA,kBACF;AAAA,kBACA,MAAMA,OAAM,UAAU;AAAA,gBACxB;AAAA,cACF;AAAA,cACA,aAAa;AAAA,YACf;AAAA,UACF;AAAA,UACA,QAAQ;AAAA,YACN,SAAS;AAAA,YACT,QAAQ;AAAA,UACV;AAAA,UACA,cAAc;AAAA,YACZ,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,UACA,SAAS;AAAA,YACP,SAAS;AAAA,YACT,QAAQ;AAAA,cACN,aAAa;AAAA,YACf;AAAA,UACF;AAAA,UACA,UAAU;AAAA,UACV,aAAa;AAAA,YACX,SACE;AAAA,YACF,YAAY;AAAA,UACd;AAAA,UACA,QAAQ;AAAA,QACV;AACA,QAAAC,UAAS,iBAAiB,EAAE,OAAO,QAAQ,EAAE,OAC3C,mBACF,EAAE,OAAO,UAAU,EAAE,SAASA;AAC9B,QAAAD,OAAM,UAAU,KAAKC;AAAA,MACvB,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism", "lilypond"]}