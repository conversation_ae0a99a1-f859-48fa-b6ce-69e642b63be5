{"version": 3, "sources": ["../../highlight.js/lib/languages/arcade.js"], "sourcesContent": ["/*\n Language: ArcGIS Arcade\n Category: scripting\n Author: <PERSON> <<EMAIL>>\n Website: https://developers.arcgis.com/arcade/\n Description: ArcGIS Arcade is an expression language used in many Esri ArcGIS products such as Pro, Online, Server, Runtime, JavaScript, and Python\n*/\n\n/** @type LanguageFn */\nfunction arcade(hljs) {\n  const IDENT_RE = '[A-Za-z_][0-9A-Za-z_]*';\n  const KEYWORDS = {\n    keyword:\n      'if for while var new function do return void else break',\n    literal:\n      'BackSlash DoubleQuote false ForwardSlash Infinity NaN NewLine null PI SingleQuote Tab TextFormatting true undefined',\n    built_in:\n      'Abs Acos Angle Attachments Area AreaGeodetic Asin Atan Atan2 Average Bearing Boolean Buffer BufferGeodetic ' +\n      'Ceil Centroid Clip Console Constrain Contains Cos Count Crosses Cut Date DateAdd ' +\n      'DateDiff Day Decode DefaultValue Dictionary Difference Disjoint Distance DistanceGeodetic Distinct ' +\n      'DomainCode DomainName Equals Exp Extent Feature FeatureSet FeatureSetByAssociation FeatureSetById FeatureSetByPortalItem ' +\n      'FeatureSetByRelationshipName FeatureSetByTitle FeatureSetByUrl Filter First Floor Geometry GroupBy Guid HasKey Hour IIf IndexOf ' +\n      'Intersection Intersects IsEmpty IsNan IsSelfIntersecting Length LengthGeodetic Log Max Mean Millisecond Min Minute Month ' +\n      'MultiPartToSinglePart Multipoint NextSequenceValue Now Number OrderBy Overlaps Point Polygon ' +\n      'Polyline Portal Pow Random Relate Reverse RingIsClockWise Round Second SetGeometry Sin Sort Sqrt Stdev Sum ' +\n      'SymmetricDifference Tan Text Timestamp Today ToLocal Top Touches ToUTC TrackCurrentTime ' +\n      'TrackGeometryWindow TrackIndex TrackStartTime TrackWindow TypeOf Union UrlEncode Variance ' +\n      'Weekday When Within Year '\n  };\n  const SYMBOL = {\n    className: 'symbol',\n    begin: '\\\\$[datastore|feature|layer|map|measure|sourcefeature|sourcelayer|targetfeature|targetlayer|value|view]+'\n  };\n  const NUMBER = {\n    className: 'number',\n    variants: [\n      {\n        begin: '\\\\b(0[bB][01]+)'\n      },\n      {\n        begin: '\\\\b(0[oO][0-7]+)'\n      },\n      {\n        begin: hljs.C_NUMBER_RE\n      }\n    ],\n    relevance: 0\n  };\n  const SUBST = {\n    className: 'subst',\n    begin: '\\\\$\\\\{',\n    end: '\\\\}',\n    keywords: KEYWORDS,\n    contains: [] // defined later\n  };\n  const TEMPLATE_STRING = {\n    className: 'string',\n    begin: '`',\n    end: '`',\n    contains: [\n      hljs.BACKSLASH_ESCAPE,\n      SUBST\n    ]\n  };\n  SUBST.contains = [\n    hljs.APOS_STRING_MODE,\n    hljs.QUOTE_STRING_MODE,\n    TEMPLATE_STRING,\n    NUMBER,\n    hljs.REGEXP_MODE\n  ];\n  const PARAMS_CONTAINS = SUBST.contains.concat([\n    hljs.C_BLOCK_COMMENT_MODE,\n    hljs.C_LINE_COMMENT_MODE\n  ]);\n\n  return {\n    name: 'ArcGIS Arcade',\n    keywords: KEYWORDS,\n    contains: [\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE,\n      TEMPLATE_STRING,\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      SYMBOL,\n      NUMBER,\n      { // object attr container\n        begin: /[{,]\\s*/,\n        relevance: 0,\n        contains: [{\n          begin: IDENT_RE + '\\\\s*:',\n          returnBegin: true,\n          relevance: 0,\n          contains: [{\n            className: 'attr',\n            begin: IDENT_RE,\n            relevance: 0\n          }]\n        }]\n      },\n      { // \"value\" container\n        begin: '(' + hljs.RE_STARTERS_RE + '|\\\\b(return)\\\\b)\\\\s*',\n        keywords: 'return',\n        contains: [\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE,\n          hljs.REGEXP_MODE,\n          {\n            className: 'function',\n            begin: '(\\\\(.*?\\\\)|' + IDENT_RE + ')\\\\s*=>',\n            returnBegin: true,\n            end: '\\\\s*=>',\n            contains: [{\n              className: 'params',\n              variants: [\n                {\n                  begin: IDENT_RE\n                },\n                {\n                  begin: /\\(\\s*\\)/\n                },\n                {\n                  begin: /\\(/,\n                  end: /\\)/,\n                  excludeBegin: true,\n                  excludeEnd: true,\n                  keywords: KEYWORDS,\n                  contains: PARAMS_CONTAINS\n                }\n              ]\n            }]\n          }\n        ],\n        relevance: 0\n      },\n      {\n        className: 'function',\n        beginKeywords: 'function',\n        end: /\\{/,\n        excludeEnd: true,\n        contains: [\n          hljs.inherit(hljs.TITLE_MODE, {\n            begin: IDENT_RE\n          }),\n          {\n            className: 'params',\n            begin: /\\(/,\n            end: /\\)/,\n            excludeBegin: true,\n            excludeEnd: true,\n            contains: PARAMS_CONTAINS\n          }\n        ],\n        illegal: /\\[|%/\n      },\n      {\n        begin: /\\$[(.]/\n      }\n    ],\n    illegal: /#(?!!)/\n  };\n}\n\nmodule.exports = arcade;\n"], "mappings": ";;;;;AAAA;AAAA;AASA,aAAS,OAAO,MAAM;AACpB,YAAM,WAAW;AACjB,YAAM,WAAW;AAAA,QACf,SACE;AAAA,QACF,SACE;AAAA,QACF,UACE;AAAA,MAWJ;AACA,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AACA,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,OAAO,KAAK;AAAA,UACd;AAAA,QACF;AAAA,QACA,WAAW;AAAA,MACb;AACA,YAAM,QAAQ;AAAA,QACZ,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,QACV,UAAU,CAAC;AAAA;AAAA,MACb;AACA,YAAM,kBAAkB;AAAA,QACtB,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,UACR,KAAK;AAAA,UACL;AAAA,QACF;AAAA,MACF;AACA,YAAM,WAAW;AAAA,QACf,KAAK;AAAA,QACL,KAAK;AAAA,QACL;AAAA,QACA;AAAA,QACA,KAAK;AAAA,MACP;AACA,YAAM,kBAAkB,MAAM,SAAS,OAAO;AAAA,QAC5C,KAAK;AAAA,QACL,KAAK;AAAA,MACP,CAAC;AAED,aAAO;AAAA,QACL,MAAM;AAAA,QACN,UAAU;AAAA,QACV,UAAU;AAAA,UACR,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA;AAAA,YACE,OAAO;AAAA,YACP,WAAW;AAAA,YACX,UAAU,CAAC;AAAA,cACT,OAAO,WAAW;AAAA,cAClB,aAAa;AAAA,cACb,WAAW;AAAA,cACX,UAAU,CAAC;AAAA,gBACT,WAAW;AAAA,gBACX,OAAO;AAAA,gBACP,WAAW;AAAA,cACb,CAAC;AAAA,YACH,CAAC;AAAA,UACH;AAAA,UACA;AAAA;AAAA,YACE,OAAO,MAAM,KAAK,iBAAiB;AAAA,YACnC,UAAU;AAAA,YACV,UAAU;AAAA,cACR,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO,gBAAgB,WAAW;AAAA,gBAClC,aAAa;AAAA,gBACb,KAAK;AAAA,gBACL,UAAU,CAAC;AAAA,kBACT,WAAW;AAAA,kBACX,UAAU;AAAA,oBACR;AAAA,sBACE,OAAO;AAAA,oBACT;AAAA,oBACA;AAAA,sBACE,OAAO;AAAA,oBACT;AAAA,oBACA;AAAA,sBACE,OAAO;AAAA,sBACP,KAAK;AAAA,sBACL,cAAc;AAAA,sBACd,YAAY;AAAA,sBACZ,UAAU;AAAA,sBACV,UAAU;AAAA,oBACZ;AAAA,kBACF;AAAA,gBACF,CAAC;AAAA,cACH;AAAA,YACF;AAAA,YACA,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,eAAe;AAAA,YACf,KAAK;AAAA,YACL,YAAY;AAAA,YACZ,UAAU;AAAA,cACR,KAAK,QAAQ,KAAK,YAAY;AAAA,gBAC5B,OAAO;AAAA,cACT,CAAC;AAAA,cACD;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,cAAc;AAAA,gBACd,YAAY;AAAA,gBACZ,UAAU;AAAA,cACZ;AAAA,YACF;AAAA,YACA,SAAS;AAAA,UACX;AAAA,UACA;AAAA,YACE,OAAO;AAAA,UACT;AAAA,QACF;AAAA,QACA,SAAS;AAAA,MACX;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}