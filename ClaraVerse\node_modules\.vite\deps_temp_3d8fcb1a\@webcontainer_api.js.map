{"version": 3, "sources": ["../../@webcontainer/api/dist/internal/constants.js", "../../@webcontainer/api/dist/internal/TypedEventTarget.js", "../../@webcontainer/api/dist/internal/tokens.js", "../../@webcontainer/api/dist/internal/iframe-url.js", "../../@webcontainer/api/dist/internal/code.js", "../../@webcontainer/api/dist/internal/reset-promise.js", "../../@webcontainer/api/dist/internal/auth-state.js", "../../@webcontainer/api/dist/preview-message-types.js", "../../@webcontainer/api/dist/vendor/index.js", "../../@webcontainer/api/dist/utils/reload-preview.js", "../../@webcontainer/api/dist/utils/is-preview-message.js", "../../@webcontainer/api/dist/utils/null-prototype.js", "../../@webcontainer/api/dist/utils/file-system.js", "../../@webcontainer/api/dist/index.js"], "sourcesContent": ["export const DEFAULT_EDITOR_ORIGIN = 'https://stackblitz.com';\nexport const SEARCH_PARAM_AUTH_CODE = 'code';\nexport const SEARCH_PARAM_ERROR = 'error';\nexport const SEARCH_PARAM_ERROR_DESCRIPTION = 'error_description';\nexport const BROADCAST_CHANNEL_NAME = '__wc_api_bc__';\nexport const STORAGE_TOKENS_NAME = '__wc_api_tokens__';\nexport const STORAGE_CODE_VERIFIER_NAME = '__wc_api_verifier__';\nexport const STORAGE_POPUP_NAME = '__wc_api_popup__';\n", "export class TypedEventTarget {\n    _bus = new EventTarget();\n    listen(listener) {\n        function wrappedListener(event) {\n            listener(event.data);\n        }\n        this._bus.addEventListener('message', wrappedListener);\n        return () => this._bus.removeEventListener('message', wrappedListener);\n    }\n    fireEvent(data) {\n        this._bus.dispatchEvent(new MessageEvent('message', { data }));\n    }\n}\n", "import { STORAGE_TOKENS_NAME } from './constants.js';\nimport { TypedEventTarget } from './TypedEventTarget.js';\nconst IGNORED_ERROR = new Error();\nIGNORED_ERROR.stack = '';\nconst accessTokenChangedListeners = new TypedEventTarget();\n/**\n * @internal\n */\nexport class Tokens {\n    origin;\n    refresh;\n    access;\n    expires;\n    _revoked = new AbortController();\n    constructor(\n    // editor origin that those tokens are bound to, mostly used for development\n    origin, \n    // token to use to get a new access token\n    refresh, \n    // token to provide to webcontainer\n    access, \n    // time in UTC when the token expires\n    expires) {\n        this.origin = origin;\n        this.refresh = refresh;\n        this.access = access;\n        this.expires = expires;\n    }\n    async activate(onFailedRefresh) {\n        if (this._revoked.signal.aborted) {\n            throw new Error('Token revoked');\n        }\n        // if the access token expired we fetch a new one\n        if (this.expires < Date.now()) {\n            if (!(await this._fetchNewAccessToken())) {\n                return false;\n            }\n        }\n        this._sync();\n        this._startRefreshTokensLoop(onFailedRefresh);\n        return true;\n    }\n    async revoke(clientId, ignoreRevokeError) {\n        this._revoked.abort();\n        try {\n            const response = await fetch(`${this.origin}/oauth/revoke`, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/x-www-form-urlencoded',\n                },\n                body: new URLSearchParams({ token: this.refresh, token_type_hint: 'refresh_token', client_id: clientId }),\n                mode: 'cors',\n            });\n            if (!response.ok) {\n                throw new Error(`Failed to logout`);\n            }\n        }\n        catch (error) {\n            if (!ignoreRevokeError) {\n                throw error;\n            }\n        }\n        clearTokensInStorage();\n    }\n    static fromStorage() {\n        const savedTokens = readTokensFromStorage();\n        if (!savedTokens) {\n            return null;\n        }\n        return new Tokens(savedTokens.origin, savedTokens.refresh, savedTokens.access, savedTokens.expires);\n    }\n    static async fromAuthCode({ editorOrigin, clientId, codeVerifier, authCode, redirectUri, }) {\n        const response = await fetch(`${editorOrigin}/oauth/token`, {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/x-www-form-urlencoded',\n            },\n            body: new URLSearchParams({\n                client_id: clientId,\n                code: authCode,\n                code_verifier: codeVerifier,\n                grant_type: 'authorization_code',\n                redirect_uri: redirectUri,\n            }),\n            mode: 'cors',\n        });\n        if (!response.ok) {\n            throw new Error(`Failed to fetch token: ${response.status}`);\n        }\n        const tokenResponse = await response.json();\n        assertTokenResponse(tokenResponse);\n        const { access_token: access, refresh_token: refresh } = tokenResponse;\n        const expires = getExpiresFromTokenResponse(tokenResponse);\n        return new Tokens(editorOrigin, refresh, access, expires);\n    }\n    async _fetchNewAccessToken() {\n        try {\n            const response = await fetch(`${this.origin}/oauth/token`, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/x-www-form-urlencoded',\n                },\n                body: new URLSearchParams({\n                    grant_type: 'refresh_token',\n                    refresh_token: this.refresh,\n                }),\n                mode: 'cors',\n                signal: this._revoked.signal,\n            });\n            if (!response.ok) {\n                throw IGNORED_ERROR;\n            }\n            const tokenResponse = await response.json();\n            assertTokenResponse(tokenResponse);\n            const { access_token: access, refresh_token: refresh } = tokenResponse;\n            const expires = getExpiresFromTokenResponse(tokenResponse);\n            this.access = access;\n            this.expires = expires;\n            this.refresh = refresh;\n            return true;\n        }\n        catch {\n            clearTokensInStorage();\n            return false;\n        }\n    }\n    _sync() {\n        persistTokensInStorage(this);\n        fireAccessTokenChanged(this.access);\n    }\n    async _startRefreshTokensLoop(onFailedRefresh) {\n        while (true) {\n            const expiresIn = this.expires - Date.now() - 1000;\n            await wait(Math.max(expiresIn, 1000));\n            if (this._revoked.signal.aborted) {\n                return;\n            }\n            if (!this._fetchNewAccessToken()) {\n                onFailedRefresh();\n                return;\n            }\n            this._sync();\n        }\n    }\n}\n/**\n * @internal\n */\nexport function clearTokensInStorage() {\n    localStorage.removeItem(STORAGE_TOKENS_NAME);\n}\n/**\n * @internal\n */\nexport function addAccessTokenChangedListener(listener) {\n    return accessTokenChangedListeners.listen(listener);\n}\nfunction readTokensFromStorage() {\n    const serializedTokens = localStorage.getItem(STORAGE_TOKENS_NAME);\n    if (!serializedTokens) {\n        return null;\n    }\n    try {\n        return JSON.parse(serializedTokens);\n    }\n    catch {\n        return null;\n    }\n}\nfunction persistTokensInStorage(tokens) {\n    localStorage.setItem(STORAGE_TOKENS_NAME, JSON.stringify(tokens));\n}\nfunction getExpiresFromTokenResponse({ created_at, expires_in }) {\n    return (created_at + expires_in) * 1000;\n}\nfunction assertTokenResponse(token) {\n    if (typeof token !== 'object' || !token) {\n        throw new Error('Invalid Token Response');\n    }\n    if (typeof token.access_token !== 'string' ||\n        typeof token.refresh_token !== 'string' ||\n        typeof token.created_at !== 'number' ||\n        typeof token.expires_in !== 'number') {\n        throw new Error('Invalid Token Response');\n    }\n}\nfunction wait(ms) {\n    return new Promise((resolve) => setTimeout(resolve, ms));\n}\nfunction fireAccessTokenChanged(accessToken) {\n    accessTokenChangedListeners.fireEvent(accessToken);\n}\n", "import { DEFAULT_EDITOR_ORIGIN } from './constants.js';\nconst params = {};\nlet editorOrigin = null;\nexport const iframeSettings = {\n    get editorOrigin() {\n        if (editorOrigin == null) {\n            editorOrigin = new URL(globalThis.WEBCONTAINER_API_IFRAME_URL ?? DEFAULT_EDITOR_ORIGIN).origin;\n        }\n        return editorOrigin;\n    },\n    set editorOrigin(newOrigin) {\n        editorOrigin = new URL(newOrigin).origin;\n    },\n    setQueryParam(key, value) {\n        params[key] = value;\n    },\n    get url() {\n        const url = new URL(this.editorOrigin);\n        url.pathname = '/headless';\n        for (const param in params) {\n            url.searchParams.set(param, params[param]);\n        }\n        url.searchParams.set('version', \"1.6.1\");\n        return url;\n    },\n};\n", "/**\n * Implementation of https://www.rfc-editor.org/rfc/rfc7636#section-4.2 that can\n * run in the browser.\n *\n * @internal\n *\n * @param input Code verifier.\n */\nexport async function S256(input) {\n    // input here is assumed to match https://www.rfc-editor.org/rfc/rfc3986#section-2.3\n    const ascii = new TextEncoder().encode(input);\n    const sha256 = new Uint8Array(await crypto.subtle.digest('SHA-256', ascii));\n    // base64url encode, based on https://developer.mozilla.org/en-US/docs/Glossary/Base64#the_unicode_problem\n    return btoa(sha256.reduce((binary, byte) => binary + String.fromCodePoint(byte), ''))\n        .replace(/\\+/g, '-')\n        .replace(/\\//g, '_')\n        .replace(/=+$/, '');\n}\n/**\n * Implementation of https://www.rfc-editor.org/rfc/rfc7636#section-4.1 with\n * a slight deviation:\n *\n *  - We use 128 characters (it's expected to be between 43 and 128)\n *  - We use 64 characters instead of 66\n *\n * So the entropy is lower given the space size is 64^128 instead of 66^128.\n * It still satisfies the entropy constraint given that 64^128 > 66^43.\n *\n * @internal\n */\nexport function newCodeVerifier() {\n    const random = new Uint8Array(96);\n    crypto.getRandomValues(random);\n    let codeVerifier = '';\n    for (let i = 0; i < 32; ++i) {\n        codeVerifier += nextFourChars(random[3 * i + 0], random[3 * i + 1], random[3 * i + 2]);\n    }\n    return codeVerifier;\n}\nfunction nextFourChars(byte1, byte2, byte3) {\n    const char1 = byte1 >> 2;\n    const char2 = ((byte1 & 3) << 4) | (byte2 >> 4);\n    const char3 = (byte2 & 15) | ((byte3 & 192) >> 2);\n    const char4 = byte3 & 63;\n    return [char1, char2, char3, char4].map(unreservedCharacters).join('');\n}\nfunction unreservedCharacters(code) {\n    let offset;\n    if (code < 26) {\n        offset = code + 65; // [A-Z]\n    }\n    else if (code < 52) {\n        offset = code - 26 + 97; // [a-z]\n    }\n    else if (code < 62) {\n        offset = code - 52 + 48; // [0-9]\n    }\n    else {\n        offset = code === 62 ? 30 /* _ */ : 45 /* - */;\n    }\n    return String.fromCharCode(offset);\n}\n", "/**\n * @internal\n */\nexport function resettablePromise() {\n    let resolve;\n    let promise;\n    function reset() {\n        promise = new Promise((_resolve) => (resolve = _resolve));\n    }\n    reset();\n    return {\n        get promise() {\n            return promise;\n        },\n        resolve(value) {\n            return resolve(value);\n        },\n        reset,\n    };\n}\n", "import { Tokens, clearTokensInStorage } from './tokens.js';\nimport { SEARCH_PARAM_AUTH_CODE, SEARCH_PARAM_ERROR, STORAGE_CODE_VERIFIER_NAME, BROADCAST_CHANNEL_NAME, STORAGE_POPUP_NAME, SEARCH_PARAM_ERROR_DESCRIPTION, } from './constants.js';\nimport { iframeSettings } from './iframe-url.js';\nimport { S256, newCodeVerifier } from './code.js';\nimport { resettablePromise } from './reset-promise.js';\nimport { TypedEventTarget } from './TypedEventTarget.js';\n/**\n * @internal\n */\nexport const authState = {\n    initialized: false,\n    bootCalled: false,\n    authComplete: resettablePromise(),\n    clientId: '',\n    oauthScope: '',\n    broadcastChannel: null,\n    get editorOrigin() {\n        return iframeSettings.editorOrigin;\n    },\n    tokens: null,\n};\nconst authFailedListeners = new TypedEventTarget();\nconst loggedOutListeners = new TypedEventTarget();\nfunction broadcastMessage(message) {\n    if (!authState.broadcastChannel) {\n        return;\n    }\n    authState.broadcastChannel.postMessage(message);\n    // check if we are in a popup mode\n    if (localStorage.getItem(STORAGE_POPUP_NAME) === 'true' && message.type !== 'auth-logout') {\n        localStorage.removeItem(STORAGE_POPUP_NAME);\n        // wait a tick to make sure the posted message has been sent\n        setTimeout(() => {\n            window.close();\n        });\n    }\n}\nexport const auth = {\n    init({ editorOrigin, clientId, scope }) {\n        if (authState.initialized) {\n            throw new Error('Init should only be called once');\n        }\n        let enterprise = true;\n        if (enterprise && authState.bootCalled) {\n            throw new Error('`auth.init` should always be called before `WebContainer.boot`');\n        }\n        authState.initialized = true;\n        authState.tokens = Tokens.fromStorage();\n        authState.clientId = clientId;\n        authState.oauthScope = scope;\n        authState.broadcastChannel = new BroadcastChannel(BROADCAST_CHANNEL_NAME);\n        // configure iframe url\n        iframeSettings.setQueryParam('client_id', clientId);\n        if (editorOrigin) {\n            iframeSettings.editorOrigin = new URL(editorOrigin).origin;\n        }\n        loggedOutListeners.listen(() => authState.authComplete.reset());\n        // if authentication or logout are done in another page, we want to reflect the state on this page as well\n        authState.broadcastChannel.addEventListener('message', onChannelMessage);\n        async function onChannelMessage(event) {\n            const typedEvent = event.data;\n            if (typedEvent.type === 'auth-complete') {\n                authState.tokens = Tokens.fromStorage();\n                // we ignore the possible error here because they can't have expired just yet\n                await authState.tokens.activate(onFailedTokenRefresh);\n                authState.authComplete.resolve();\n                return;\n            }\n            if (typedEvent.type === 'auth-failed') {\n                authFailedListeners.fireEvent(typedEvent);\n                return;\n            }\n            if (typedEvent.type === 'auth-logout') {\n                loggedOutListeners.fireEvent();\n                return;\n            }\n        }\n        if (authState.tokens) {\n            const tokens = authState.tokens;\n            if (tokens.origin === authState.editorOrigin) {\n                /**\n                 * Here we assume that the refresh token never expires which\n                 * might not be correct. If that is the case though, we will\n                 * emit a 'logged-out' event to signal that the user has been\n                 * logged out, which could also happen at a later time anyway.\n                 *\n                 * Because this flow is done entirely locally, we do not broadcast\n                 * anything to the other tabs. They should be performing a similar\n                 * check.\n                 */\n                (async () => {\n                    const success = await tokens.activate(onFailedTokenRefresh);\n                    if (!success) {\n                        // if we got new token in the meantime we discard this error\n                        if (authState.tokens !== tokens) {\n                            return;\n                        }\n                        loggedOutListeners.fireEvent();\n                        return;\n                    }\n                    authState.authComplete.resolve();\n                })();\n                return { status: 'authorized' };\n            }\n            clearTokensInStorage();\n            authState.tokens = null;\n        }\n        const locationURL = new URL(window.location.href);\n        const { searchParams } = locationURL;\n        const updateURL = () => window.history.replaceState({}, document.title, locationURL);\n        // check for errors first, aka the user declined the authorisation or stackblitz did\n        if (searchParams.has(SEARCH_PARAM_ERROR)) {\n            const error = searchParams.get(SEARCH_PARAM_ERROR);\n            const description = searchParams.get(SEARCH_PARAM_ERROR_DESCRIPTION);\n            searchParams.delete(SEARCH_PARAM_ERROR);\n            searchParams.delete(SEARCH_PARAM_ERROR_DESCRIPTION);\n            updateURL();\n            broadcastMessage({ type: 'auth-failed', error, description });\n            return { status: 'auth-failed', error, description };\n        }\n        // if there's an auth code\n        if (searchParams.has(SEARCH_PARAM_AUTH_CODE)) {\n            const authCode = searchParams.get(SEARCH_PARAM_AUTH_CODE);\n            const editorOrigin = authState.editorOrigin;\n            searchParams.delete(SEARCH_PARAM_AUTH_CODE);\n            updateURL();\n            const codeVerifier = localStorage.getItem(STORAGE_CODE_VERIFIER_NAME);\n            if (!codeVerifier) {\n                return { status: 'need-auth' };\n            }\n            localStorage.removeItem(STORAGE_CODE_VERIFIER_NAME);\n            Tokens.fromAuthCode({\n                editorOrigin,\n                clientId: authState.clientId,\n                authCode,\n                codeVerifier,\n                redirectUri: defaultRedirectUri(),\n            })\n                .then(async (tokens) => {\n                authState.tokens = tokens;\n                assertAuthTokens(authState.tokens);\n                const success = await authState.tokens.activate(onFailedTokenRefresh);\n                // if authentication failed we throw, and we'll mark auth as failed\n                if (!success) {\n                    throw new Error();\n                }\n                authState.authComplete.resolve();\n                broadcastMessage({ type: 'auth-complete' });\n            })\n                .catch((error) => {\n                // this should never happen unless the rails app is now down for some reason?\n                console.error(error);\n                // treat it as a logged out event so that the user can retry to login\n                loggedOutListeners.fireEvent();\n                broadcastMessage({ type: 'auth-logout' });\n            });\n            return { status: 'authorized' };\n        }\n        return { status: 'need-auth' };\n    },\n    async startAuthFlow({ popup } = {}) {\n        if (!authState.initialized) {\n            throw new Error('auth.init must be called first');\n        }\n        if (popup) {\n            localStorage.setItem(STORAGE_POPUP_NAME, 'true');\n            const height = 500;\n            const width = 620;\n            const left = window.screenLeft + (window.outerWidth - width) / 2;\n            const top = window.screenTop + (window.outerHeight - height) / 2;\n            window.open(await generateOAuthRequest(), '_blank', `popup,width=${width},height=${height},left=${left},top=${top}`);\n        }\n        else {\n            window.location.href = await generateOAuthRequest();\n        }\n    },\n    async logout({ ignoreRevokeError } = {}) {\n        await authState.tokens?.revoke(authState.clientId, ignoreRevokeError ?? false);\n        loggedOutListeners.fireEvent();\n        broadcastMessage({ type: 'auth-logout' });\n    },\n    loggedIn() {\n        return authState.authComplete.promise;\n    },\n    on(event, listener) {\n        switch (event) {\n            case 'auth-failed': {\n                return authFailedListeners.listen(listener);\n            }\n            case 'logged-out': {\n                return loggedOutListeners.listen(listener);\n            }\n            default: {\n                throw new Error(`Unsupported event type '${event}'.`);\n            }\n        }\n    },\n};\nfunction onFailedTokenRefresh() {\n    loggedOutListeners.fireEvent();\n    broadcastMessage({ type: 'auth-logout' });\n}\nfunction defaultRedirectUri() {\n    return window.location.href;\n}\nasync function generateOAuthRequest() {\n    const codeVerifier = newCodeVerifier();\n    localStorage.setItem(STORAGE_CODE_VERIFIER_NAME, codeVerifier);\n    const codeChallenge = await S256(codeVerifier);\n    const url = new URL('/oauth/authorize', authState.editorOrigin);\n    const { searchParams } = url;\n    searchParams.append('response_type', 'code');\n    searchParams.append('client_id', authState.clientId);\n    searchParams.append('redirect_uri', defaultRedirectUri());\n    searchParams.append('scope', authState.oauthScope);\n    searchParams.append('code_challenge', codeChallenge);\n    searchParams.append('code_challenge_method', 'S256');\n    return url.toString();\n}\n/**\n * @internal\n */\nexport function assertAuthTokens(tokens) {\n    if (!tokens) {\n        throw new Error('Oops! Tokens is not defined when it always should be.');\n    }\n}\n", "/**\n * This type is in a separate module so that localservice can import it\n * without bundling all the other webcontainer specific stuff.\n */\nexport var PreviewMessageType;\n(function (PreviewMessageType) {\n    PreviewMessageType[\"UncaughtException\"] = \"PREVIEW_UNCAUGHT_EXCEPTION\";\n    PreviewMessageType[\"UnhandledRejection\"] = \"PREVIEW_UNHANDLED_REJECTION\";\n    PreviewMessageType[\"ConsoleError\"] = \"PREVIEW_CONSOLE_ERROR\";\n})(PreviewMessageType || (PreviewMessageType = {}));\n", "var __defProp = Object.defineProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\n\n// dist/vendor/comlink.js\nvar comlink_exports = {};\n__export(comlink_exports, {\n  createEndpoint: () => createEndpoint,\n  expose: () => expose,\n  proxy: () => proxy,\n  proxyMarker: () => proxyMarker,\n  releaseProxy: () => releaseProxy,\n  transfer: () => transfer,\n  transferHandlers: () => transferHandlers,\n  windowEndpoint: () => windowEndpoint,\n  wrap: () => wrap\n});\n\n// ../../node_modules/comlink/dist/esm/comlink.mjs\nvar proxyMarker = Symbol(\"Comlink.proxy\");\nvar createEndpoint = Symbol(\"Comlink.endpoint\");\nvar releaseProxy = Symbol(\"Comlink.releaseProxy\");\nvar throwMarker = Symbol(\"Comlink.thrown\");\nvar isObject = (val) => typeof val === \"object\" && val !== null || typeof val === \"function\";\nvar proxyTransferHandler = {\n  canHandle: (val) => isObject(val) && val[proxyMarker],\n  serialize(obj) {\n    const { port1, port2 } = new MessageChannel();\n    expose(obj, port1);\n    return [port2, [port2]];\n  },\n  deserialize(port) {\n    port.start();\n    return wrap(port);\n  }\n};\nvar throwTransferHandler = {\n  canHandle: (value) => isObject(value) && throwMarker in value,\n  serialize({ value }) {\n    let serialized;\n    if (value instanceof Error) {\n      serialized = {\n        isError: true,\n        value: {\n          message: value.message,\n          name: value.name,\n          stack: value.stack\n        }\n      };\n    } else {\n      serialized = { isError: false, value };\n    }\n    return [serialized, []];\n  },\n  deserialize(serialized) {\n    if (serialized.isError) {\n      throw Object.assign(new Error(serialized.value.message), serialized.value);\n    }\n    throw serialized.value;\n  }\n};\nvar transferHandlers = /* @__PURE__ */ new Map([\n  [\"proxy\", proxyTransferHandler],\n  [\"throw\", throwTransferHandler]\n]);\nfunction expose(obj, ep = self) {\n  ep.addEventListener(\"message\", function callback(ev) {\n    if (!ev || !ev.data) {\n      return;\n    }\n    const { id, type, path } = Object.assign({ path: [] }, ev.data);\n    const argumentList = (ev.data.argumentList || []).map(fromWireValue);\n    let returnValue;\n    try {\n      const parent = path.slice(0, -1).reduce((obj2, prop) => obj2[prop], obj);\n      const rawValue = path.reduce((obj2, prop) => obj2[prop], obj);\n      switch (type) {\n        case 0:\n          {\n            returnValue = rawValue;\n          }\n          break;\n        case 1:\n          {\n            parent[path.slice(-1)[0]] = fromWireValue(ev.data.value);\n            returnValue = true;\n          }\n          break;\n        case 2:\n          {\n            returnValue = rawValue.apply(parent, argumentList);\n          }\n          break;\n        case 3:\n          {\n            const value = new rawValue(...argumentList);\n            returnValue = proxy(value);\n          }\n          break;\n        case 4:\n          {\n            const { port1, port2 } = new MessageChannel();\n            expose(obj, port2);\n            returnValue = transfer(port1, [port1]);\n          }\n          break;\n        case 5:\n          {\n            returnValue = void 0;\n          }\n          break;\n      }\n    } catch (value) {\n      returnValue = { value, [throwMarker]: 0 };\n    }\n    Promise.resolve(returnValue).catch((value) => {\n      return { value, [throwMarker]: 0 };\n    }).then((returnValue2) => {\n      const [wireValue, transferables] = toWireValue(returnValue2);\n      ep.postMessage(Object.assign(Object.assign({}, wireValue), { id }), transferables);\n      if (type === 5) {\n        ep.removeEventListener(\"message\", callback);\n        closeEndPoint(ep);\n      }\n    });\n  });\n  if (ep.start) {\n    ep.start();\n  }\n}\nfunction isMessagePort(endpoint) {\n  return endpoint.constructor.name === \"MessagePort\";\n}\nfunction closeEndPoint(endpoint) {\n  if (isMessagePort(endpoint))\n    endpoint.close();\n}\nfunction wrap(ep, target) {\n  return createProxy(ep, [], target);\n}\nfunction throwIfProxyReleased(isReleased) {\n  if (isReleased) {\n    throw new Error(\"Proxy has been released and is not useable\");\n  }\n}\nfunction createProxy(ep, path = [], target = function() {\n}) {\n  let isProxyReleased = false;\n  const proxy2 = new Proxy(target, {\n    get(_target, prop) {\n      throwIfProxyReleased(isProxyReleased);\n      if (prop === releaseProxy) {\n        return () => {\n          return requestResponseMessage(ep, {\n            type: 5,\n            path: path.map((p) => p.toString())\n          }).then(() => {\n            closeEndPoint(ep);\n            isProxyReleased = true;\n          });\n        };\n      }\n      if (prop === \"then\") {\n        if (path.length === 0) {\n          return { then: () => proxy2 };\n        }\n        const r = requestResponseMessage(ep, {\n          type: 0,\n          path: path.map((p) => p.toString())\n        }).then(fromWireValue);\n        return r.then.bind(r);\n      }\n      return createProxy(ep, [...path, prop]);\n    },\n    set(_target, prop, rawValue) {\n      throwIfProxyReleased(isProxyReleased);\n      const [value, transferables] = toWireValue(rawValue);\n      return requestResponseMessage(ep, {\n        type: 1,\n        path: [...path, prop].map((p) => p.toString()),\n        value\n      }, transferables).then(fromWireValue);\n    },\n    apply(_target, _thisArg, rawArgumentList) {\n      throwIfProxyReleased(isProxyReleased);\n      const last = path[path.length - 1];\n      if (last === createEndpoint) {\n        return requestResponseMessage(ep, {\n          type: 4\n        }).then(fromWireValue);\n      }\n      if (last === \"bind\") {\n        return createProxy(ep, path.slice(0, -1));\n      }\n      const [argumentList, transferables] = processArguments(rawArgumentList);\n      return requestResponseMessage(ep, {\n        type: 2,\n        path: path.map((p) => p.toString()),\n        argumentList\n      }, transferables).then(fromWireValue);\n    },\n    construct(_target, rawArgumentList) {\n      throwIfProxyReleased(isProxyReleased);\n      const [argumentList, transferables] = processArguments(rawArgumentList);\n      return requestResponseMessage(ep, {\n        type: 3,\n        path: path.map((p) => p.toString()),\n        argumentList\n      }, transferables).then(fromWireValue);\n    }\n  });\n  return proxy2;\n}\nfunction myFlat(arr) {\n  return Array.prototype.concat.apply([], arr);\n}\nfunction processArguments(argumentList) {\n  const processed = argumentList.map(toWireValue);\n  return [processed.map((v) => v[0]), myFlat(processed.map((v) => v[1]))];\n}\nvar transferCache = /* @__PURE__ */ new WeakMap();\nfunction transfer(obj, transfers) {\n  transferCache.set(obj, transfers);\n  return obj;\n}\nfunction proxy(obj) {\n  return Object.assign(obj, { [proxyMarker]: true });\n}\nfunction windowEndpoint(w, context = self, targetOrigin = \"*\") {\n  return {\n    postMessage: (msg, transferables) => w.postMessage(msg, targetOrigin, transferables),\n    addEventListener: context.addEventListener.bind(context),\n    removeEventListener: context.removeEventListener.bind(context)\n  };\n}\nfunction toWireValue(value) {\n  for (const [name, handler] of transferHandlers) {\n    if (handler.canHandle(value)) {\n      const [serializedValue, transferables] = handler.serialize(value);\n      return [\n        {\n          type: 3,\n          name,\n          value: serializedValue\n        },\n        transferables\n      ];\n    }\n  }\n  return [\n    {\n      type: 0,\n      value\n    },\n    transferCache.get(value) || []\n  ];\n}\nfunction fromWireValue(value) {\n  switch (value.type) {\n    case 3:\n      return transferHandlers.get(value.name).deserialize(value.value);\n    case 0:\n      return value.value;\n  }\n}\nfunction requestResponseMessage(ep, msg, transfers) {\n  return new Promise((resolve) => {\n    const id = generateUUID();\n    ep.addEventListener(\"message\", function l(ev) {\n      if (!ev.data || !ev.data.id || ev.data.id !== id) {\n        return;\n      }\n      ep.removeEventListener(\"message\", l);\n      resolve(ev.data);\n    });\n    if (ep.start) {\n      ep.start();\n    }\n    ep.postMessage(Object.assign({ id }, msg), transfers);\n  });\n}\nfunction generateUUID() {\n  return new Array(4).fill(0).map(() => Math.floor(Math.random() * Number.MAX_SAFE_INTEGER).toString(16)).join(\"-\");\n}\nexport {\n  comlink_exports as Comlink\n};\n", "/**\n * This function reloads the provided iframe.\n *\n * @param preview The iframe page to reload.\n * @param hardRefreshTimeout The timeout after which the preview is reset if it hasn't responded to the reload event.\n */\nexport function reloadPreview(preview, hardRefreshTimeout = 200) {\n    const { port1, port2 } = new MessageChannel();\n    let resolve;\n    const promise = new Promise((_resolve) => {\n        resolve = _resolve;\n    });\n    const done = () => {\n        resolve();\n        port2.close();\n    };\n    const timeout = setTimeout(() => {\n        const iframeSrc = preview.src;\n        preview.src = iframeSrc;\n        done();\n    }, hardRefreshTimeout);\n    port2.addEventListener('message', (event) => {\n        const data = event.data;\n        if (data == null || typeof data !== 'object') {\n            return;\n        }\n        if (data.type === 'LOCALSERVICE_WINDOW_RELOADED') {\n            clearTimeout(timeout);\n            done();\n        }\n    });\n    preview.contentWindow?.postMessage({\n        type: 'LOCALSERVICE_RELOAD_WINDOW',\n        callback: port1,\n    }, '*', [port1]);\n    return promise;\n}\n", "import { PreviewMessageType } from '../preview-message-types.js';\nconst PREVIEW_MESSAGE_TYPES = [\n    PreviewMessageType.ConsoleError,\n    PreviewMessageType.UncaughtException,\n    PreviewMessageType.UnhandledRejection,\n];\nexport function isPreviewMessage(data) {\n    if (data == null || typeof data !== 'object') {\n        return false;\n    }\n    if (!('type' in data) || !PREVIEW_MESSAGE_TYPES.includes(data.type)) {\n        return false;\n    }\n    return true;\n}\n", "/**\n * @internal\n */\nexport function nullPrototype(source) {\n    const prototype = Object.create(null);\n    if (!source) {\n        return prototype;\n    }\n    return Object.assign(prototype, source);\n}\n", "import { nullPrototype } from './null-prototype.js';\nconst binaryDecoder = new TextDecoder('latin1');\n/**\n * @internal\n */\nexport function toInternalFileSystemTree(tree) {\n    const newTree = { d: {} };\n    for (const name of Object.keys(tree)) {\n        const entry = tree[name];\n        if ('file' in entry) {\n            if ('symlink' in entry.file) {\n                newTree.d[name] = { f: { l: entry.file.symlink } };\n                continue;\n            }\n            const contents = entry.file.contents;\n            const stringContents = typeof contents === 'string' ? contents : binaryDecoder.decode(contents);\n            const binary = typeof contents === 'string' ? {} : { b: true };\n            newTree.d[name] = { f: { c: stringContents, ...binary } };\n            continue;\n        }\n        const newEntry = toInternalFileSystemTree(entry.directory);\n        newTree.d[name] = newEntry;\n    }\n    return newTree;\n}\n/**\n * @internal\n */\nexport function toExternalFileSystemTree(tree) {\n    const newTree = nullPrototype();\n    if ('f' in tree) {\n        throw new Error('It is not possible to export a single file in the JSON format.');\n    }\n    if ('d' in tree) {\n        for (const name of Object.keys(tree.d)) {\n            const entry = tree.d[name];\n            if ('d' in entry) {\n                newTree[name] = nullPrototype({\n                    directory: toExternalFileSystemTree(entry),\n                });\n            }\n            else if ('f' in entry) {\n                if ('c' in entry.f) {\n                    newTree[name] = nullPrototype({\n                        file: nullPrototype({\n                            contents: entry.f.b ? fromBinaryString(entry.f.c) : entry.f.c,\n                        }),\n                    });\n                }\n                else if ('l' in entry.f) {\n                    newTree[name] = nullPrototype({\n                        file: nullPrototype({\n                            symlink: entry.f.l,\n                        }),\n                    });\n                }\n            }\n        }\n    }\n    return newTree;\n}\nfunction fromBinaryString(s) {\n    const encoded = new Uint8Array(s.length);\n    for (let i = 0; i < s.length; i++) {\n        encoded[i] = s[i].charCodeAt(0);\n    }\n    return encoded;\n}\n", "/**\n * The WebContainer Public API allows you build custom applications on top of an in-browser Node.js runtime.\n *\n * Its main entrypoint is the {@link WebContainer} class.\n *\n * @packageDocumentation\n */\nimport { authState, assertAuthTokens } from './internal/auth-state.js';\nimport { PreviewMessageType } from './preview-message-types.js';\nimport { Comlink } from './vendor/index.js';\nimport { auth as authImpl } from './internal/auth-state.js';\nimport { addAccessTokenChangedListener } from './internal/tokens.js';\nimport { iframeSettings } from './internal/iframe-url.js';\nimport { isPreviewMessage } from './utils.js';\nimport { toExternalFileSystemTree, toInternalFileSystemTree } from './utils/file-system.js';\nexport const auth = authImpl;\nexport { PreviewMessageType };\nexport * from './utils.js';\nlet bootPromise = null;\nlet cachedServerPromise = null;\nlet cachedBootOptions = {};\nconst decoder = new TextDecoder();\nconst encoder = new TextEncoder();\n/**\n * The main export of this library. An instance of `WebContainer` represents a runtime\n * ready to be used.\n */\nexport class WebContainer {\n    _instance;\n    _runtimeInfo;\n    /**\n     * Gives access to the underlying file system.\n     */\n    fs;\n    /** @internal */\n    static _instance = null;\n    /** @internal */\n    static _teardownPromise = null;\n    _tornDown = false;\n    _unsubscribeFromTokenChangedListener = () => { };\n    /** @internal */\n    constructor(\n    /** @internal */\n    _instance, fs, previewScript, \n    /** @internal */\n    _runtimeInfo) {\n        this._instance = _instance;\n        this._runtimeInfo = _runtimeInfo;\n        this.fs = new FileSystemAPIClient(fs);\n        // forward the credentials to webcontainer if needed\n        if (authState.initialized) {\n            this._unsubscribeFromTokenChangedListener = addAccessTokenChangedListener((accessToken) => {\n                this._instance.setCredentials({ accessToken, editorOrigin: authState.editorOrigin });\n            });\n            (async () => {\n                await authState.authComplete.promise;\n                if (this._tornDown) {\n                    return;\n                }\n                assertAuthTokens(authState.tokens);\n                await this._instance.setCredentials({\n                    accessToken: authState.tokens.access,\n                    editorOrigin: authState.editorOrigin,\n                });\n            })().catch((error) => {\n                // print the error as this is likely a bug in webcontainer\n                console.error(error);\n            });\n        }\n    }\n    async spawn(command, optionsOrArgs, options) {\n        let args = [];\n        if (Array.isArray(optionsOrArgs)) {\n            args = optionsOrArgs;\n        }\n        else {\n            options = optionsOrArgs;\n        }\n        let output = undefined;\n        let outputStream = new ReadableStream();\n        if (options?.output !== false) {\n            const result = streamWithPush();\n            output = result.push;\n            outputStream = result.stream;\n        }\n        let stdout = undefined;\n        let stdoutStream;\n        let stderr = undefined;\n        let stderrStream;\n        const wrappedOutput = proxyListener(binaryListener(output));\n        const wrappedStdout = proxyListener(binaryListener(stdout));\n        const wrappedStderr = proxyListener(binaryListener(stderr));\n        const process = await this._instance.run({\n            command,\n            args,\n            cwd: options?.cwd,\n            env: options?.env,\n            terminal: options?.terminal,\n        }, wrappedStdout, wrappedStderr, wrappedOutput);\n        return new WebContainerProcessImpl(process, outputStream, stdoutStream, stderrStream);\n    }\n    async export(path, options) {\n        const serializeOptions = {\n            format: options?.format ?? 'json',\n            includes: options?.includes,\n            excludes: options?.excludes,\n            external: true,\n        };\n        const result = await this._instance.serialize(path, serializeOptions);\n        if (serializeOptions.format === 'json') {\n            const data = JSON.parse(decoder.decode(result));\n            return toExternalFileSystemTree(data);\n        }\n        return result;\n    }\n    on(event, listener) {\n        if (event === 'preview-message') {\n            const originalListener = listener;\n            listener = ((message) => {\n                if (isPreviewMessage(message)) {\n                    originalListener(message);\n                }\n            });\n        }\n        const { listener: wrapped, subscribe } = syncSubscription(listener);\n        return subscribe(this._instance.on(event, Comlink.proxy(wrapped)));\n    }\n    /**\n     * Mounts a tree of files into the filesystem. This can be specified as a tree object ({@link FileSystemTree})\n     * or as a binary snapshot generated by [`@webcontainer/snapshot`](https://www.npmjs.com/package/@webcontainer/snapshot).\n     *\n     * @param snapshotOrTree - A tree of files, or a binary snapshot. Note that binary payloads will be transferred.\n     * @param options.mountPoint - Specifies a nested path where the tree should be mounted.\n     */\n    mount(snapshotOrTree, options) {\n        const payload = snapshotOrTree instanceof Uint8Array\n            ? snapshotOrTree\n            : snapshotOrTree instanceof ArrayBuffer\n                ? new Uint8Array(snapshotOrTree)\n                : encoder.encode(JSON.stringify(toInternalFileSystemTree(snapshotOrTree)));\n        return this._instance.loadFiles(Comlink.transfer(payload, [payload.buffer]), {\n            mountPoints: options?.mountPoint,\n        });\n    }\n    /**\n     * Set a custom script to be injected into all previews. When this function is called, every\n     * future page reload will contain the provided script tag on all HTML responses.\n     *\n     * Note:\n     *\n     * When this function resolves, every preview reloaded _after_ will have the new script.\n     * Existing preview have to be explicitely reloaded.\n     *\n     * To reload a preview you can use `reloadPreview`.\n     *\n     * @param scriptSrc Source for the script tag.\n     * @param options Options to define which type of script this is.\n     */\n    setPreviewScript(scriptSrc, options) {\n        return this._instance.setPreviewScript(scriptSrc, options);\n    }\n    /**\n     * The default value of the `PATH` environment variable for processes started through {@link spawn}.\n     */\n    get path() {\n        return this._runtimeInfo.path;\n    }\n    /**\n     * The full path to the working directory (see {@link FileSystemAPI}).\n     */\n    get workdir() {\n        return this._runtimeInfo.cwd;\n    }\n    /**\n     * Destroys the WebContainer instance, turning it unusable, and releases its resources. After this,\n     * a new WebContainer instance can be obtained by calling {@link WebContainer.boot | `boot`}.\n     *\n     * All entities derived from this instance (e.g. processes, the file system, etc.) also become unusable\n     * after calling this method.\n     */\n    teardown() {\n        if (this._tornDown) {\n            throw new Error('WebContainer already torn down');\n        }\n        this._tornDown = true;\n        this._unsubscribeFromTokenChangedListener();\n        const teardownFn = async () => {\n            try {\n                await this.fs._teardown();\n                await this._instance.teardown();\n            }\n            finally {\n                this._instance[Comlink.releaseProxy]();\n                if (WebContainer._instance === this) {\n                    WebContainer._instance = null;\n                }\n            }\n        };\n        WebContainer._teardownPromise = teardownFn();\n    }\n    /**\n     * Boots a WebContainer. Only a single instance of WebContainer can be booted concurrently\n     * (see {@link WebContainer.teardown | `teardown`}).\n     *\n     * Booting WebContainer is an expensive operation.\n     */\n    static async boot(options = {}) {\n        await this._teardownPromise;\n        WebContainer._teardownPromise = null;\n        const { workdirName } = options;\n        if (window.crossOriginIsolated && options.coep === 'none') {\n            console.warn(`A Cross-Origin-Embedder-Policy header is required in cross origin isolated environments.\\nSet the 'coep' option to 'require-corp'.`);\n        }\n        if (workdirName?.includes('/') || workdirName === '..' || workdirName === '.') {\n            throw new Error('workdirName should be a valid folder name');\n        }\n        // signal that boot was called to auth module as calling auth.init after boot is likely incorrect\n        authState.bootCalled = true;\n        // try to \"acquire the lock\", i.e. wait for any ongoing boot request to finish\n        while (bootPromise) {\n            await bootPromise;\n        }\n        if (WebContainer._instance) {\n            throw new Error('Only a single WebContainer instance can be booted');\n        }\n        const instancePromise = unsynchronizedBoot(options);\n        // the \"lock\" is a promise for the ongoing boot that never fails\n        bootPromise = instancePromise.catch(() => { });\n        try {\n            const instance = await instancePromise;\n            WebContainer._instance = instance;\n            return instance;\n        }\n        finally {\n            // release the \"lock\"\n            bootPromise = null;\n        }\n    }\n}\n/**\n * Configure an API key to be used for this instance of WebContainer.\n *\n * @param key WebContainer API key.\n */\nexport function configureAPIKey(key) {\n    if (authState.bootCalled) {\n        throw new Error('`configureAPIKey` should always be called before `WebContainer.boot`');\n    }\n    iframeSettings.setQueryParam('client_id', key);\n}\nconst DIR_ENTRY_TYPE_FILE = 1;\nconst DIR_ENTRY_TYPE_DIR = 2;\n/**\n * @internal\n */\nclass DirEntImpl {\n    name;\n    _type;\n    constructor(name, _type) {\n        this.name = name;\n        this._type = _type;\n    }\n    isFile() {\n        return this._type === DIR_ENTRY_TYPE_FILE;\n    }\n    isDirectory() {\n        return this._type === DIR_ENTRY_TYPE_DIR;\n    }\n}\nclass FSWatcher {\n    _apiClient;\n    _path;\n    _options;\n    _listener;\n    _wrappedListener;\n    _watcher;\n    _closed = false;\n    constructor(_apiClient, _path, _options, _listener) {\n        this._apiClient = _apiClient;\n        this._path = _path;\n        this._options = _options;\n        this._listener = _listener;\n        this._apiClient._watchers.add(this);\n        this._wrappedListener = (event, filename) => {\n            if (this._listener && !this._closed) {\n                this._listener(event, filename);\n            }\n        };\n        this._apiClient._fs\n            .watch(this._path, this._options, proxyListener(this._wrappedListener))\n            .then((_watcher) => {\n            this._watcher = _watcher;\n            if (this._closed) {\n                return this._teardown();\n            }\n            return undefined;\n        })\n            .catch(console.error);\n    }\n    async close() {\n        if (!this._closed) {\n            this._closed = true;\n            this._apiClient._watchers.delete(this);\n            await this._teardown();\n        }\n    }\n    /**\n     * @internal\n     */\n    async _teardown() {\n        await this._watcher?.close().finally(() => {\n            this._watcher?.[Comlink.releaseProxy]();\n        });\n    }\n}\n/**\n * @internal\n */\nclass WebContainerProcessImpl {\n    output;\n    input;\n    exit;\n    _process;\n    stdout;\n    stderr;\n    constructor(process, output, stdout, stderr) {\n        this.output = output;\n        this._process = process;\n        this.input = new WritableStream({\n            write: (data) => {\n                // this promise is not supposed to fail anyway\n                this._getProcess()\n                    ?.write(data)\n                    .catch(() => { });\n            },\n        });\n        this.exit = this._onExit();\n        this.stdout = stdout;\n        this.stderr = stderr;\n    }\n    kill() {\n        this._process?.kill();\n    }\n    resize(dimensions) {\n        this._getProcess()?.resize(dimensions);\n    }\n    async _onExit() {\n        try {\n            return await this._process.onExit;\n        }\n        finally {\n            this._process?.[Comlink.releaseProxy]();\n            this._process = null;\n        }\n    }\n    _getProcess() {\n        if (this._process == null) {\n            console.warn('This process already exited');\n        }\n        return this._process;\n    }\n}\n/**\n * @internal\n */\nclass FileSystemAPIClient {\n    _fs;\n    _watchers = new Set([]);\n    constructor(fs) {\n        this._fs = fs;\n    }\n    rm(...args) {\n        return this._fs.rm(...args);\n    }\n    async readFile(path, encoding) {\n        return await this._fs.readFile(path, encoding);\n    }\n    async rename(oldPath, newPath) {\n        return await this._fs.rename(oldPath, newPath);\n    }\n    async writeFile(path, data, options) {\n        if (data instanceof Uint8Array) {\n            const buffer = data.buffer.slice(data.byteOffset, data.byteOffset + data.byteLength);\n            data = Comlink.transfer(new Uint8Array(buffer), [buffer]);\n        }\n        await this._fs.writeFile(path, data, options);\n    }\n    async readdir(path, options) {\n        const result = await this._fs.readdir(path, options);\n        if (isStringArray(result)) {\n            return result;\n        }\n        if (isTypedArrayCollection(result)) {\n            return result;\n        }\n        const entries = result.map((entry) => new DirEntImpl(entry.name, entry['Symbol(type)']));\n        return entries;\n    }\n    async mkdir(path, options) {\n        return await this._fs.mkdir(path, options);\n    }\n    watch(path, options, listener) {\n        if (typeof options === 'function') {\n            listener = options;\n            options = null;\n        }\n        return new FSWatcher(this, path, options, listener);\n    }\n    /**\n     * @internal\n     */\n    async _teardown() {\n        this._fs[Comlink.releaseProxy]();\n        await Promise.all([...this._watchers].map((watcher) => watcher.close()));\n    }\n}\nasync function unsynchronizedBoot(options) {\n    const { serverPromise } = serverFactory(options);\n    const server = await serverPromise;\n    const instance = await server.build({\n        host: window.location.host,\n        version: \"1.6.1\",\n        workdirName: options.workdirName,\n        forwardPreviewErrors: options.forwardPreviewErrors,\n    });\n    const [fs, previewScript, runtimeInfo] = await Promise.all([\n        instance.fs(),\n        instance.previewScript(),\n        instance.runtimeInfo(),\n    ]);\n    return new WebContainer(instance, fs, previewScript, runtimeInfo);\n}\nfunction binaryListener(listener) {\n    if (listener == null) {\n        return undefined;\n    }\n    return (data) => {\n        if (data instanceof Uint8Array) {\n            listener(decoder.decode(data));\n        }\n        else if (data == null) {\n            listener(null);\n        }\n    };\n}\nfunction proxyListener(listener) {\n    if (listener == null) {\n        return undefined;\n    }\n    return Comlink.proxy(listener);\n}\nfunction serverFactory(options) {\n    if (cachedServerPromise != null) {\n        if (options.coep !== cachedBootOptions.coep) {\n            console.warn(`Attempting to boot WebContainer with 'coep: ${options.coep}'`);\n            console.warn(`First boot had 'coep: ${cachedBootOptions.coep}', new settings will not take effect!`);\n        }\n        return { serverPromise: cachedServerPromise };\n    }\n    if (options.coep) {\n        iframeSettings.setQueryParam('coep', options.coep);\n    }\n    if (options.experimentalNode) {\n        iframeSettings.setQueryParam('experimental_node', '1');\n    }\n    const iframe = document.createElement('iframe');\n    iframe.style.display = 'none';\n    iframe.setAttribute('allow', 'cross-origin-isolated');\n    const url = iframeSettings.url;\n    iframe.src = url.toString();\n    const { origin } = url;\n    cachedBootOptions = { ...options };\n    cachedServerPromise = new Promise((resolve) => {\n        const onMessage = (event) => {\n            if (event.origin !== origin) {\n                return;\n            }\n            const { data } = event;\n            if (data.type === 'init') {\n                resolve(Comlink.wrap(event.ports[0]));\n                return;\n            }\n            if (data.type === 'warning') {\n                console[data.level].call(console, data.message);\n                return;\n            }\n        };\n        window.addEventListener('message', onMessage);\n    });\n    document.body.insertBefore(iframe, null);\n    return { serverPromise: cachedServerPromise };\n}\nfunction isStringArray(list) {\n    return typeof list[0] === 'string';\n}\nfunction isTypedArrayCollection(list) {\n    return list[0] instanceof Uint8Array;\n}\nfunction streamWithPush() {\n    let controller = null;\n    const stream = new ReadableStream({\n        start(controller_) {\n            controller = controller_;\n        },\n    });\n    const push = (item) => {\n        if (item != null) {\n            controller?.enqueue(item);\n        }\n        else {\n            controller?.close();\n            controller = null;\n        }\n    };\n    return { stream, push };\n}\nfunction syncSubscription(listener) {\n    let stopped = false;\n    let unsubscribe = () => { };\n    const wrapped = ((...args) => {\n        if (stopped) {\n            return;\n        }\n        listener(...args);\n    });\n    return {\n        subscribe(promise) {\n            promise.then((unsubscribe_) => {\n                unsubscribe = unsubscribe_;\n                if (stopped) {\n                    unsubscribe();\n                }\n            });\n            return () => {\n                stopped = true;\n                unsubscribe();\n            };\n        },\n        listener: wrapped,\n    };\n}\n"], "mappings": ";;;;;AAAO,IAAM,wBAAwB;AAC9B,IAAM,yBAAyB;AAC/B,IAAM,qBAAqB;AAC3B,IAAM,iCAAiC;AACvC,IAAM,yBAAyB;AAC/B,IAAM,sBAAsB;AAC5B,IAAM,6BAA6B;AACnC,IAAM,qBAAqB;;;ACP3B,IAAM,mBAAN,MAAuB;AAAA,EAAvB;AACH,gCAAO,IAAI,YAAY;AAAA;AAAA,EACvB,OAAO,UAAU;AACb,aAAS,gBAAgB,OAAO;AAC5B,eAAS,MAAM,IAAI;AAAA,IACvB;AACA,SAAK,KAAK,iBAAiB,WAAW,eAAe;AACrD,WAAO,MAAM,KAAK,KAAK,oBAAoB,WAAW,eAAe;AAAA,EACzE;AAAA,EACA,UAAU,MAAM;AACZ,SAAK,KAAK,cAAc,IAAI,aAAa,WAAW,EAAE,KAAK,CAAC,CAAC;AAAA,EACjE;AACJ;;;ACVA,IAAM,gBAAgB,IAAI,MAAM;AAChC,cAAc,QAAQ;AACtB,IAAM,8BAA8B,IAAI,iBAAiB;AAIlD,IAAM,SAAN,MAAM,QAAO;AAAA,EAMhB,YAEA,QAEA,SAEA,QAEA,SAAS;AAbT;AACA;AACA;AACA;AACA,oCAAW,IAAI,gBAAgB;AAU3B,SAAK,SAAS;AACd,SAAK,UAAU;AACf,SAAK,SAAS;AACd,SAAK,UAAU;AAAA,EACnB;AAAA,EACA,MAAM,SAAS,iBAAiB;AAC5B,QAAI,KAAK,SAAS,OAAO,SAAS;AAC9B,YAAM,IAAI,MAAM,eAAe;AAAA,IACnC;AAEA,QAAI,KAAK,UAAU,KAAK,IAAI,GAAG;AAC3B,UAAI,CAAE,MAAM,KAAK,qBAAqB,GAAI;AACtC,eAAO;AAAA,MACX;AAAA,IACJ;AACA,SAAK,MAAM;AACX,SAAK,wBAAwB,eAAe;AAC5C,WAAO;AAAA,EACX;AAAA,EACA,MAAM,OAAO,UAAU,mBAAmB;AACtC,SAAK,SAAS,MAAM;AACpB,QAAI;AACA,YAAM,WAAW,MAAM,MAAM,GAAG,KAAK,MAAM,iBAAiB;AAAA,QACxD,QAAQ;AAAA,QACR,SAAS;AAAA,UACL,gBAAgB;AAAA,QACpB;AAAA,QACA,MAAM,IAAI,gBAAgB,EAAE,OAAO,KAAK,SAAS,iBAAiB,iBAAiB,WAAW,SAAS,CAAC;AAAA,QACxG,MAAM;AAAA,MACV,CAAC;AACD,UAAI,CAAC,SAAS,IAAI;AACd,cAAM,IAAI,MAAM,kBAAkB;AAAA,MACtC;AAAA,IACJ,SACO,OAAO;AACV,UAAI,CAAC,mBAAmB;AACpB,cAAM;AAAA,MACV;AAAA,IACJ;AACA,yBAAqB;AAAA,EACzB;AAAA,EACA,OAAO,cAAc;AACjB,UAAM,cAAc,sBAAsB;AAC1C,QAAI,CAAC,aAAa;AACd,aAAO;AAAA,IACX;AACA,WAAO,IAAI,QAAO,YAAY,QAAQ,YAAY,SAAS,YAAY,QAAQ,YAAY,OAAO;AAAA,EACtG;AAAA,EACA,aAAa,aAAa,EAAE,cAAAA,eAAc,UAAU,cAAc,UAAU,YAAa,GAAG;AACxF,UAAM,WAAW,MAAM,MAAM,GAAGA,aAAY,gBAAgB;AAAA,MACxD,QAAQ;AAAA,MACR,SAAS;AAAA,QACL,gBAAgB;AAAA,MACpB;AAAA,MACA,MAAM,IAAI,gBAAgB;AAAA,QACtB,WAAW;AAAA,QACX,MAAM;AAAA,QACN,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,cAAc;AAAA,MAClB,CAAC;AAAA,MACD,MAAM;AAAA,IACV,CAAC;AACD,QAAI,CAAC,SAAS,IAAI;AACd,YAAM,IAAI,MAAM,0BAA0B,SAAS,MAAM,EAAE;AAAA,IAC/D;AACA,UAAM,gBAAgB,MAAM,SAAS,KAAK;AAC1C,wBAAoB,aAAa;AACjC,UAAM,EAAE,cAAc,QAAQ,eAAe,QAAQ,IAAI;AACzD,UAAM,UAAU,4BAA4B,aAAa;AACzD,WAAO,IAAI,QAAOA,eAAc,SAAS,QAAQ,OAAO;AAAA,EAC5D;AAAA,EACA,MAAM,uBAAuB;AACzB,QAAI;AACA,YAAM,WAAW,MAAM,MAAM,GAAG,KAAK,MAAM,gBAAgB;AAAA,QACvD,QAAQ;AAAA,QACR,SAAS;AAAA,UACL,gBAAgB;AAAA,QACpB;AAAA,QACA,MAAM,IAAI,gBAAgB;AAAA,UACtB,YAAY;AAAA,UACZ,eAAe,KAAK;AAAA,QACxB,CAAC;AAAA,QACD,MAAM;AAAA,QACN,QAAQ,KAAK,SAAS;AAAA,MAC1B,CAAC;AACD,UAAI,CAAC,SAAS,IAAI;AACd,cAAM;AAAA,MACV;AACA,YAAM,gBAAgB,MAAM,SAAS,KAAK;AAC1C,0BAAoB,aAAa;AACjC,YAAM,EAAE,cAAc,QAAQ,eAAe,QAAQ,IAAI;AACzD,YAAM,UAAU,4BAA4B,aAAa;AACzD,WAAK,SAAS;AACd,WAAK,UAAU;AACf,WAAK,UAAU;AACf,aAAO;AAAA,IACX,QACM;AACF,2BAAqB;AACrB,aAAO;AAAA,IACX;AAAA,EACJ;AAAA,EACA,QAAQ;AACJ,2BAAuB,IAAI;AAC3B,2BAAuB,KAAK,MAAM;AAAA,EACtC;AAAA,EACA,MAAM,wBAAwB,iBAAiB;AAC3C,WAAO,MAAM;AACT,YAAM,YAAY,KAAK,UAAU,KAAK,IAAI,IAAI;AAC9C,YAAM,KAAK,KAAK,IAAI,WAAW,GAAI,CAAC;AACpC,UAAI,KAAK,SAAS,OAAO,SAAS;AAC9B;AAAA,MACJ;AACA,UAAI,CAAC,KAAK,qBAAqB,GAAG;AAC9B,wBAAgB;AAChB;AAAA,MACJ;AACA,WAAK,MAAM;AAAA,IACf;AAAA,EACJ;AACJ;AAIO,SAAS,uBAAuB;AACnC,eAAa,WAAW,mBAAmB;AAC/C;AAIO,SAAS,8BAA8B,UAAU;AACpD,SAAO,4BAA4B,OAAO,QAAQ;AACtD;AACA,SAAS,wBAAwB;AAC7B,QAAM,mBAAmB,aAAa,QAAQ,mBAAmB;AACjE,MAAI,CAAC,kBAAkB;AACnB,WAAO;AAAA,EACX;AACA,MAAI;AACA,WAAO,KAAK,MAAM,gBAAgB;AAAA,EACtC,QACM;AACF,WAAO;AAAA,EACX;AACJ;AACA,SAAS,uBAAuB,QAAQ;AACpC,eAAa,QAAQ,qBAAqB,KAAK,UAAU,MAAM,CAAC;AACpE;AACA,SAAS,4BAA4B,EAAE,YAAY,WAAW,GAAG;AAC7D,UAAQ,aAAa,cAAc;AACvC;AACA,SAAS,oBAAoB,OAAO;AAChC,MAAI,OAAO,UAAU,YAAY,CAAC,OAAO;AACrC,UAAM,IAAI,MAAM,wBAAwB;AAAA,EAC5C;AACA,MAAI,OAAO,MAAM,iBAAiB,YAC9B,OAAO,MAAM,kBAAkB,YAC/B,OAAO,MAAM,eAAe,YAC5B,OAAO,MAAM,eAAe,UAAU;AACtC,UAAM,IAAI,MAAM,wBAAwB;AAAA,EAC5C;AACJ;AACA,SAAS,KAAK,IAAI;AACd,SAAO,IAAI,QAAQ,CAAC,YAAY,WAAW,SAAS,EAAE,CAAC;AAC3D;AACA,SAAS,uBAAuB,aAAa;AACzC,8BAA4B,UAAU,WAAW;AACrD;;;AC9LA,IAAM,SAAS,CAAC;AAChB,IAAI,eAAe;AACZ,IAAM,iBAAiB;AAAA,EAC1B,IAAI,eAAe;AACf,QAAI,gBAAgB,MAAM;AACtB,qBAAe,IAAI,IAAI,WAAW,+BAA+B,qBAAqB,EAAE;AAAA,IAC5F;AACA,WAAO;AAAA,EACX;AAAA,EACA,IAAI,aAAa,WAAW;AACxB,mBAAe,IAAI,IAAI,SAAS,EAAE;AAAA,EACtC;AAAA,EACA,cAAc,KAAK,OAAO;AACtB,WAAO,GAAG,IAAI;AAAA,EAClB;AAAA,EACA,IAAI,MAAM;AACN,UAAM,MAAM,IAAI,IAAI,KAAK,YAAY;AACrC,QAAI,WAAW;AACf,eAAW,SAAS,QAAQ;AACxB,UAAI,aAAa,IAAI,OAAO,OAAO,KAAK,CAAC;AAAA,IAC7C;AACA,QAAI,aAAa,IAAI,WAAW,OAAO;AACvC,WAAO;AAAA,EACX;AACJ;;;ACjBA,eAAsB,KAAK,OAAO;AAE9B,QAAM,QAAQ,IAAI,YAAY,EAAE,OAAO,KAAK;AAC5C,QAAM,SAAS,IAAI,WAAW,MAAM,OAAO,OAAO,OAAO,WAAW,KAAK,CAAC;AAE1E,SAAO,KAAK,OAAO,OAAO,CAAC,QAAQ,SAAS,SAAS,OAAO,cAAc,IAAI,GAAG,EAAE,CAAC,EAC/E,QAAQ,OAAO,GAAG,EAClB,QAAQ,OAAO,GAAG,EAClB,QAAQ,OAAO,EAAE;AAC1B;AAaO,SAAS,kBAAkB;AAC9B,QAAM,SAAS,IAAI,WAAW,EAAE;AAChC,SAAO,gBAAgB,MAAM;AAC7B,MAAI,eAAe;AACnB,WAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AACzB,oBAAgB,cAAc,OAAO,IAAI,IAAI,CAAC,GAAG,OAAO,IAAI,IAAI,CAAC,GAAG,OAAO,IAAI,IAAI,CAAC,CAAC;AAAA,EACzF;AACA,SAAO;AACX;AACA,SAAS,cAAc,OAAO,OAAO,OAAO;AACxC,QAAM,QAAQ,SAAS;AACvB,QAAM,SAAU,QAAQ,MAAM,IAAM,SAAS;AAC7C,QAAM,QAAS,QAAQ,MAAQ,QAAQ,QAAQ;AAC/C,QAAM,QAAQ,QAAQ;AACtB,SAAO,CAAC,OAAO,OAAO,OAAO,KAAK,EAAE,IAAI,oBAAoB,EAAE,KAAK,EAAE;AACzE;AACA,SAAS,qBAAqB,MAAM;AAChC,MAAI;AACJ,MAAI,OAAO,IAAI;AACX,aAAS,OAAO;AAAA,EACpB,WACS,OAAO,IAAI;AAChB,aAAS,OAAO,KAAK;AAAA,EACzB,WACS,OAAO,IAAI;AAChB,aAAS,OAAO,KAAK;AAAA,EACzB,OACK;AACD,aAAS,SAAS,KAAK,KAAa;AAAA,EACxC;AACA,SAAO,OAAO,aAAa,MAAM;AACrC;;;AC1DO,SAAS,oBAAoB;AAChC,MAAI;AACJ,MAAI;AACJ,WAAS,QAAQ;AACb,cAAU,IAAI,QAAQ,CAAC,aAAc,UAAU,QAAS;AAAA,EAC5D;AACA,QAAM;AACN,SAAO;AAAA,IACH,IAAI,UAAU;AACV,aAAO;AAAA,IACX;AAAA,IACA,QAAQ,OAAO;AACX,aAAO,QAAQ,KAAK;AAAA,IACxB;AAAA,IACA;AAAA,EACJ;AACJ;;;ACVO,IAAM,YAAY;AAAA,EACrB,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,cAAc,kBAAkB;AAAA,EAChC,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,kBAAkB;AAAA,EAClB,IAAI,eAAe;AACf,WAAO,eAAe;AAAA,EAC1B;AAAA,EACA,QAAQ;AACZ;AACA,IAAM,sBAAsB,IAAI,iBAAiB;AACjD,IAAM,qBAAqB,IAAI,iBAAiB;AAChD,SAAS,iBAAiB,SAAS;AAC/B,MAAI,CAAC,UAAU,kBAAkB;AAC7B;AAAA,EACJ;AACA,YAAU,iBAAiB,YAAY,OAAO;AAE9C,MAAI,aAAa,QAAQ,kBAAkB,MAAM,UAAU,QAAQ,SAAS,eAAe;AACvF,iBAAa,WAAW,kBAAkB;AAE1C,eAAW,MAAM;AACb,aAAO,MAAM;AAAA,IACjB,CAAC;AAAA,EACL;AACJ;AACO,IAAM,OAAO;AAAA,EAChB,KAAK,EAAE,cAAAC,eAAc,UAAU,MAAM,GAAG;AACpC,QAAI,UAAU,aAAa;AACvB,YAAM,IAAI,MAAM,iCAAiC;AAAA,IACrD;AACA,QAAI,aAAa;AACjB,QAAI,cAAc,UAAU,YAAY;AACpC,YAAM,IAAI,MAAM,gEAAgE;AAAA,IACpF;AACA,cAAU,cAAc;AACxB,cAAU,SAAS,OAAO,YAAY;AACtC,cAAU,WAAW;AACrB,cAAU,aAAa;AACvB,cAAU,mBAAmB,IAAI,iBAAiB,sBAAsB;AAExE,mBAAe,cAAc,aAAa,QAAQ;AAClD,QAAIA,eAAc;AACd,qBAAe,eAAe,IAAI,IAAIA,aAAY,EAAE;AAAA,IACxD;AACA,uBAAmB,OAAO,MAAM,UAAU,aAAa,MAAM,CAAC;AAE9D,cAAU,iBAAiB,iBAAiB,WAAW,gBAAgB;AACvE,mBAAe,iBAAiB,OAAO;AACnC,YAAM,aAAa,MAAM;AACzB,UAAI,WAAW,SAAS,iBAAiB;AACrC,kBAAU,SAAS,OAAO,YAAY;AAEtC,cAAM,UAAU,OAAO,SAAS,oBAAoB;AACpD,kBAAU,aAAa,QAAQ;AAC/B;AAAA,MACJ;AACA,UAAI,WAAW,SAAS,eAAe;AACnC,4BAAoB,UAAU,UAAU;AACxC;AAAA,MACJ;AACA,UAAI,WAAW,SAAS,eAAe;AACnC,2BAAmB,UAAU;AAC7B;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,UAAU,QAAQ;AAClB,YAAM,SAAS,UAAU;AACzB,UAAI,OAAO,WAAW,UAAU,cAAc;AAW1C,SAAC,YAAY;AACT,gBAAM,UAAU,MAAM,OAAO,SAAS,oBAAoB;AAC1D,cAAI,CAAC,SAAS;AAEV,gBAAI,UAAU,WAAW,QAAQ;AAC7B;AAAA,YACJ;AACA,+BAAmB,UAAU;AAC7B;AAAA,UACJ;AACA,oBAAU,aAAa,QAAQ;AAAA,QACnC,GAAG;AACH,eAAO,EAAE,QAAQ,aAAa;AAAA,MAClC;AACA,2BAAqB;AACrB,gBAAU,SAAS;AAAA,IACvB;AACA,UAAM,cAAc,IAAI,IAAI,OAAO,SAAS,IAAI;AAChD,UAAM,EAAE,aAAa,IAAI;AACzB,UAAM,YAAY,MAAM,OAAO,QAAQ,aAAa,CAAC,GAAG,SAAS,OAAO,WAAW;AAEnF,QAAI,aAAa,IAAI,kBAAkB,GAAG;AACtC,YAAM,QAAQ,aAAa,IAAI,kBAAkB;AACjD,YAAM,cAAc,aAAa,IAAI,8BAA8B;AACnE,mBAAa,OAAO,kBAAkB;AACtC,mBAAa,OAAO,8BAA8B;AAClD,gBAAU;AACV,uBAAiB,EAAE,MAAM,eAAe,OAAO,YAAY,CAAC;AAC5D,aAAO,EAAE,QAAQ,eAAe,OAAO,YAAY;AAAA,IACvD;AAEA,QAAI,aAAa,IAAI,sBAAsB,GAAG;AAC1C,YAAM,WAAW,aAAa,IAAI,sBAAsB;AACxD,YAAMA,gBAAe,UAAU;AAC/B,mBAAa,OAAO,sBAAsB;AAC1C,gBAAU;AACV,YAAM,eAAe,aAAa,QAAQ,0BAA0B;AACpE,UAAI,CAAC,cAAc;AACf,eAAO,EAAE,QAAQ,YAAY;AAAA,MACjC;AACA,mBAAa,WAAW,0BAA0B;AAClD,aAAO,aAAa;AAAA,QAChB,cAAAA;AAAA,QACA,UAAU,UAAU;AAAA,QACpB;AAAA,QACA;AAAA,QACA,aAAa,mBAAmB;AAAA,MACpC,CAAC,EACI,KAAK,OAAO,WAAW;AACxB,kBAAU,SAAS;AACnB,yBAAiB,UAAU,MAAM;AACjC,cAAM,UAAU,MAAM,UAAU,OAAO,SAAS,oBAAoB;AAEpE,YAAI,CAAC,SAAS;AACV,gBAAM,IAAI,MAAM;AAAA,QACpB;AACA,kBAAU,aAAa,QAAQ;AAC/B,yBAAiB,EAAE,MAAM,gBAAgB,CAAC;AAAA,MAC9C,CAAC,EACI,MAAM,CAAC,UAAU;AAElB,gBAAQ,MAAM,KAAK;AAEnB,2BAAmB,UAAU;AAC7B,yBAAiB,EAAE,MAAM,cAAc,CAAC;AAAA,MAC5C,CAAC;AACD,aAAO,EAAE,QAAQ,aAAa;AAAA,IAClC;AACA,WAAO,EAAE,QAAQ,YAAY;AAAA,EACjC;AAAA,EACA,MAAM,cAAc,EAAE,MAAM,IAAI,CAAC,GAAG;AAChC,QAAI,CAAC,UAAU,aAAa;AACxB,YAAM,IAAI,MAAM,gCAAgC;AAAA,IACpD;AACA,QAAI,OAAO;AACP,mBAAa,QAAQ,oBAAoB,MAAM;AAC/C,YAAM,SAAS;AACf,YAAM,QAAQ;AACd,YAAM,OAAO,OAAO,cAAc,OAAO,aAAa,SAAS;AAC/D,YAAM,MAAM,OAAO,aAAa,OAAO,cAAc,UAAU;AAC/D,aAAO,KAAK,MAAM,qBAAqB,GAAG,UAAU,eAAe,KAAK,WAAW,MAAM,SAAS,IAAI,QAAQ,GAAG,EAAE;AAAA,IACvH,OACK;AACD,aAAO,SAAS,OAAO,MAAM,qBAAqB;AAAA,IACtD;AAAA,EACJ;AAAA,EACA,MAAM,OAAO,EAAE,kBAAkB,IAAI,CAAC,GAAG;AAhL7C;AAiLQ,YAAM,eAAU,WAAV,mBAAkB,OAAO,UAAU,UAAU,qBAAqB;AACxE,uBAAmB,UAAU;AAC7B,qBAAiB,EAAE,MAAM,cAAc,CAAC;AAAA,EAC5C;AAAA,EACA,WAAW;AACP,WAAO,UAAU,aAAa;AAAA,EAClC;AAAA,EACA,GAAG,OAAO,UAAU;AAChB,YAAQ,OAAO;AAAA,MACX,KAAK,eAAe;AAChB,eAAO,oBAAoB,OAAO,QAAQ;AAAA,MAC9C;AAAA,MACA,KAAK,cAAc;AACf,eAAO,mBAAmB,OAAO,QAAQ;AAAA,MAC7C;AAAA,MACA,SAAS;AACL,cAAM,IAAI,MAAM,2BAA2B,KAAK,IAAI;AAAA,MACxD;AAAA,IACJ;AAAA,EACJ;AACJ;AACA,SAAS,uBAAuB;AAC5B,qBAAmB,UAAU;AAC7B,mBAAiB,EAAE,MAAM,cAAc,CAAC;AAC5C;AACA,SAAS,qBAAqB;AAC1B,SAAO,OAAO,SAAS;AAC3B;AACA,eAAe,uBAAuB;AAClC,QAAM,eAAe,gBAAgB;AACrC,eAAa,QAAQ,4BAA4B,YAAY;AAC7D,QAAM,gBAAgB,MAAM,KAAK,YAAY;AAC7C,QAAM,MAAM,IAAI,IAAI,oBAAoB,UAAU,YAAY;AAC9D,QAAM,EAAE,aAAa,IAAI;AACzB,eAAa,OAAO,iBAAiB,MAAM;AAC3C,eAAa,OAAO,aAAa,UAAU,QAAQ;AACnD,eAAa,OAAO,gBAAgB,mBAAmB,CAAC;AACxD,eAAa,OAAO,SAAS,UAAU,UAAU;AACjD,eAAa,OAAO,kBAAkB,aAAa;AACnD,eAAa,OAAO,yBAAyB,MAAM;AACnD,SAAO,IAAI,SAAS;AACxB;AAIO,SAAS,iBAAiB,QAAQ;AACrC,MAAI,CAAC,QAAQ;AACT,UAAM,IAAI,MAAM,uDAAuD;AAAA,EAC3E;AACJ;;;AC9NO,IAAI;AAAA,CACV,SAAUC,qBAAoB;AAC3B,EAAAA,oBAAmB,mBAAmB,IAAI;AAC1C,EAAAA,oBAAmB,oBAAoB,IAAI;AAC3C,EAAAA,oBAAmB,cAAc,IAAI;AACzC,GAAG,uBAAuB,qBAAqB,CAAC,EAAE;;;ACTlD,IAAI,YAAY,OAAO;AACvB,IAAI,WAAW,CAAC,QAAQ,QAAQ;AAC9B,WAAS,QAAQ;AACf,cAAU,QAAQ,MAAM,EAAE,KAAK,IAAI,IAAI,GAAG,YAAY,KAAK,CAAC;AAChE;AAGA,IAAI,kBAAkB,CAAC;AACvB,SAAS,iBAAiB;AAAA,EACxB,gBAAgB,MAAM;AAAA,EACtB,QAAQ,MAAM;AAAA,EACd,OAAO,MAAM;AAAA,EACb,aAAa,MAAM;AAAA,EACnB,cAAc,MAAM;AAAA,EACpB,UAAU,MAAM;AAAA,EAChB,kBAAkB,MAAM;AAAA,EACxB,gBAAgB,MAAM;AAAA,EACtB,MAAM,MAAM;AACd,CAAC;AAGD,IAAI,cAAc,OAAO,eAAe;AACxC,IAAI,iBAAiB,OAAO,kBAAkB;AAC9C,IAAI,eAAe,OAAO,sBAAsB;AAChD,IAAI,cAAc,OAAO,gBAAgB;AACzC,IAAI,WAAW,CAAC,QAAQ,OAAO,QAAQ,YAAY,QAAQ,QAAQ,OAAO,QAAQ;AAClF,IAAI,uBAAuB;AAAA,EACzB,WAAW,CAAC,QAAQ,SAAS,GAAG,KAAK,IAAI,WAAW;AAAA,EACpD,UAAU,KAAK;AACb,UAAM,EAAE,OAAO,MAAM,IAAI,IAAI,eAAe;AAC5C,WAAO,KAAK,KAAK;AACjB,WAAO,CAAC,OAAO,CAAC,KAAK,CAAC;AAAA,EACxB;AAAA,EACA,YAAY,MAAM;AAChB,SAAK,MAAM;AACX,WAAO,KAAK,IAAI;AAAA,EAClB;AACF;AACA,IAAI,uBAAuB;AAAA,EACzB,WAAW,CAAC,UAAU,SAAS,KAAK,KAAK,eAAe;AAAA,EACxD,UAAU,EAAE,MAAM,GAAG;AACnB,QAAI;AACJ,QAAI,iBAAiB,OAAO;AAC1B,mBAAa;AAAA,QACX,SAAS;AAAA,QACT,OAAO;AAAA,UACL,SAAS,MAAM;AAAA,UACf,MAAM,MAAM;AAAA,UACZ,OAAO,MAAM;AAAA,QACf;AAAA,MACF;AAAA,IACF,OAAO;AACL,mBAAa,EAAE,SAAS,OAAO,MAAM;AAAA,IACvC;AACA,WAAO,CAAC,YAAY,CAAC,CAAC;AAAA,EACxB;AAAA,EACA,YAAY,YAAY;AACtB,QAAI,WAAW,SAAS;AACtB,YAAM,OAAO,OAAO,IAAI,MAAM,WAAW,MAAM,OAAO,GAAG,WAAW,KAAK;AAAA,IAC3E;AACA,UAAM,WAAW;AAAA,EACnB;AACF;AACA,IAAI,mBAAmC,oBAAI,IAAI;AAAA,EAC7C,CAAC,SAAS,oBAAoB;AAAA,EAC9B,CAAC,SAAS,oBAAoB;AAChC,CAAC;AACD,SAAS,OAAO,KAAK,KAAK,MAAM;AAC9B,KAAG,iBAAiB,WAAW,SAAS,SAAS,IAAI;AACnD,QAAI,CAAC,MAAM,CAAC,GAAG,MAAM;AACnB;AAAA,IACF;AACA,UAAM,EAAE,IAAI,MAAM,KAAK,IAAI,OAAO,OAAO,EAAE,MAAM,CAAC,EAAE,GAAG,GAAG,IAAI;AAC9D,UAAM,gBAAgB,GAAG,KAAK,gBAAgB,CAAC,GAAG,IAAI,aAAa;AACnE,QAAI;AACJ,QAAI;AACF,YAAM,SAAS,KAAK,MAAM,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,SAAS,KAAK,IAAI,GAAG,GAAG;AACvE,YAAM,WAAW,KAAK,OAAO,CAAC,MAAM,SAAS,KAAK,IAAI,GAAG,GAAG;AAC5D,cAAQ,MAAM;AAAA,QACZ,KAAK;AACH;AACE,0BAAc;AAAA,UAChB;AACA;AAAA,QACF,KAAK;AACH;AACE,mBAAO,KAAK,MAAM,EAAE,EAAE,CAAC,CAAC,IAAI,cAAc,GAAG,KAAK,KAAK;AACvD,0BAAc;AAAA,UAChB;AACA;AAAA,QACF,KAAK;AACH;AACE,0BAAc,SAAS,MAAM,QAAQ,YAAY;AAAA,UACnD;AACA;AAAA,QACF,KAAK;AACH;AACE,kBAAM,QAAQ,IAAI,SAAS,GAAG,YAAY;AAC1C,0BAAc,MAAM,KAAK;AAAA,UAC3B;AACA;AAAA,QACF,KAAK;AACH;AACE,kBAAM,EAAE,OAAO,MAAM,IAAI,IAAI,eAAe;AAC5C,mBAAO,KAAK,KAAK;AACjB,0BAAc,SAAS,OAAO,CAAC,KAAK,CAAC;AAAA,UACvC;AACA;AAAA,QACF,KAAK;AACH;AACE,0BAAc;AAAA,UAChB;AACA;AAAA,MACJ;AAAA,IACF,SAAS,OAAO;AACd,oBAAc,EAAE,OAAO,CAAC,WAAW,GAAG,EAAE;AAAA,IAC1C;AACA,YAAQ,QAAQ,WAAW,EAAE,MAAM,CAAC,UAAU;AAC5C,aAAO,EAAE,OAAO,CAAC,WAAW,GAAG,EAAE;AAAA,IACnC,CAAC,EAAE,KAAK,CAAC,iBAAiB;AACxB,YAAM,CAAC,WAAW,aAAa,IAAI,YAAY,YAAY;AAC3D,SAAG,YAAY,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,SAAS,GAAG,EAAE,GAAG,CAAC,GAAG,aAAa;AACjF,UAAI,SAAS,GAAG;AACd,WAAG,oBAAoB,WAAW,QAAQ;AAC1C,sBAAc,EAAE;AAAA,MAClB;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,MAAI,GAAG,OAAO;AACZ,OAAG,MAAM;AAAA,EACX;AACF;AACA,SAAS,cAAc,UAAU;AAC/B,SAAO,SAAS,YAAY,SAAS;AACvC;AACA,SAAS,cAAc,UAAU;AAC/B,MAAI,cAAc,QAAQ;AACxB,aAAS,MAAM;AACnB;AACA,SAAS,KAAK,IAAI,QAAQ;AACxB,SAAO,YAAY,IAAI,CAAC,GAAG,MAAM;AACnC;AACA,SAAS,qBAAqB,YAAY;AACxC,MAAI,YAAY;AACd,UAAM,IAAI,MAAM,4CAA4C;AAAA,EAC9D;AACF;AACA,SAAS,YAAY,IAAI,OAAO,CAAC,GAAG,SAAS,WAAW;AACxD,GAAG;AACD,MAAI,kBAAkB;AACtB,QAAM,SAAS,IAAI,MAAM,QAAQ;AAAA,IAC/B,IAAI,SAAS,MAAM;AACjB,2BAAqB,eAAe;AACpC,UAAI,SAAS,cAAc;AACzB,eAAO,MAAM;AACX,iBAAO,uBAAuB,IAAI;AAAA,YAChC,MAAM;AAAA,YACN,MAAM,KAAK,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC;AAAA,UACpC,CAAC,EAAE,KAAK,MAAM;AACZ,0BAAc,EAAE;AAChB,8BAAkB;AAAA,UACpB,CAAC;AAAA,QACH;AAAA,MACF;AACA,UAAI,SAAS,QAAQ;AACnB,YAAI,KAAK,WAAW,GAAG;AACrB,iBAAO,EAAE,MAAM,MAAM,OAAO;AAAA,QAC9B;AACA,cAAM,IAAI,uBAAuB,IAAI;AAAA,UACnC,MAAM;AAAA,UACN,MAAM,KAAK,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC;AAAA,QACpC,CAAC,EAAE,KAAK,aAAa;AACrB,eAAO,EAAE,KAAK,KAAK,CAAC;AAAA,MACtB;AACA,aAAO,YAAY,IAAI,CAAC,GAAG,MAAM,IAAI,CAAC;AAAA,IACxC;AAAA,IACA,IAAI,SAAS,MAAM,UAAU;AAC3B,2BAAqB,eAAe;AACpC,YAAM,CAAC,OAAO,aAAa,IAAI,YAAY,QAAQ;AACnD,aAAO,uBAAuB,IAAI;AAAA,QAChC,MAAM;AAAA,QACN,MAAM,CAAC,GAAG,MAAM,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC;AAAA,QAC7C;AAAA,MACF,GAAG,aAAa,EAAE,KAAK,aAAa;AAAA,IACtC;AAAA,IACA,MAAM,SAAS,UAAU,iBAAiB;AACxC,2BAAqB,eAAe;AACpC,YAAM,OAAO,KAAK,KAAK,SAAS,CAAC;AACjC,UAAI,SAAS,gBAAgB;AAC3B,eAAO,uBAAuB,IAAI;AAAA,UAChC,MAAM;AAAA,QACR,CAAC,EAAE,KAAK,aAAa;AAAA,MACvB;AACA,UAAI,SAAS,QAAQ;AACnB,eAAO,YAAY,IAAI,KAAK,MAAM,GAAG,EAAE,CAAC;AAAA,MAC1C;AACA,YAAM,CAAC,cAAc,aAAa,IAAI,iBAAiB,eAAe;AACtE,aAAO,uBAAuB,IAAI;AAAA,QAChC,MAAM;AAAA,QACN,MAAM,KAAK,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC;AAAA,QAClC;AAAA,MACF,GAAG,aAAa,EAAE,KAAK,aAAa;AAAA,IACtC;AAAA,IACA,UAAU,SAAS,iBAAiB;AAClC,2BAAqB,eAAe;AACpC,YAAM,CAAC,cAAc,aAAa,IAAI,iBAAiB,eAAe;AACtE,aAAO,uBAAuB,IAAI;AAAA,QAChC,MAAM;AAAA,QACN,MAAM,KAAK,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC;AAAA,QAClC;AAAA,MACF,GAAG,aAAa,EAAE,KAAK,aAAa;AAAA,IACtC;AAAA,EACF,CAAC;AACD,SAAO;AACT;AACA,SAAS,OAAO,KAAK;AACnB,SAAO,MAAM,UAAU,OAAO,MAAM,CAAC,GAAG,GAAG;AAC7C;AACA,SAAS,iBAAiB,cAAc;AACtC,QAAM,YAAY,aAAa,IAAI,WAAW;AAC9C,SAAO,CAAC,UAAU,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG,OAAO,UAAU,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC;AACxE;AACA,IAAI,gBAAgC,oBAAI,QAAQ;AAChD,SAAS,SAAS,KAAK,WAAW;AAChC,gBAAc,IAAI,KAAK,SAAS;AAChC,SAAO;AACT;AACA,SAAS,MAAM,KAAK;AAClB,SAAO,OAAO,OAAO,KAAK,EAAE,CAAC,WAAW,GAAG,KAAK,CAAC;AACnD;AACA,SAAS,eAAe,GAAG,UAAU,MAAM,eAAe,KAAK;AAC7D,SAAO;AAAA,IACL,aAAa,CAAC,KAAK,kBAAkB,EAAE,YAAY,KAAK,cAAc,aAAa;AAAA,IACnF,kBAAkB,QAAQ,iBAAiB,KAAK,OAAO;AAAA,IACvD,qBAAqB,QAAQ,oBAAoB,KAAK,OAAO;AAAA,EAC/D;AACF;AACA,SAAS,YAAY,OAAO;AAC1B,aAAW,CAAC,MAAM,OAAO,KAAK,kBAAkB;AAC9C,QAAI,QAAQ,UAAU,KAAK,GAAG;AAC5B,YAAM,CAAC,iBAAiB,aAAa,IAAI,QAAQ,UAAU,KAAK;AAChE,aAAO;AAAA,QACL;AAAA,UACE,MAAM;AAAA,UACN;AAAA,UACA,OAAO;AAAA,QACT;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,MACE,MAAM;AAAA,MACN;AAAA,IACF;AAAA,IACA,cAAc,IAAI,KAAK,KAAK,CAAC;AAAA,EAC/B;AACF;AACA,SAAS,cAAc,OAAO;AAC5B,UAAQ,MAAM,MAAM;AAAA,IAClB,KAAK;AACH,aAAO,iBAAiB,IAAI,MAAM,IAAI,EAAE,YAAY,MAAM,KAAK;AAAA,IACjE,KAAK;AACH,aAAO,MAAM;AAAA,EACjB;AACF;AACA,SAAS,uBAAuB,IAAI,KAAK,WAAW;AAClD,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,UAAM,KAAK,aAAa;AACxB,OAAG,iBAAiB,WAAW,SAAS,EAAE,IAAI;AAC5C,UAAI,CAAC,GAAG,QAAQ,CAAC,GAAG,KAAK,MAAM,GAAG,KAAK,OAAO,IAAI;AAChD;AAAA,MACF;AACA,SAAG,oBAAoB,WAAW,CAAC;AACnC,cAAQ,GAAG,IAAI;AAAA,IACjB,CAAC;AACD,QAAI,GAAG,OAAO;AACZ,SAAG,MAAM;AAAA,IACX;AACA,OAAG,YAAY,OAAO,OAAO,EAAE,GAAG,GAAG,GAAG,GAAG,SAAS;AAAA,EACtD,CAAC;AACH;AACA,SAAS,eAAe;AACtB,SAAO,IAAI,MAAM,CAAC,EAAE,KAAK,CAAC,EAAE,IAAI,MAAM,KAAK,MAAM,KAAK,OAAO,IAAI,OAAO,gBAAgB,EAAE,SAAS,EAAE,CAAC,EAAE,KAAK,GAAG;AAClH;;;ACvRO,SAAS,cAAc,SAAS,qBAAqB,KAAK;AANjE;AAOI,QAAM,EAAE,OAAO,MAAM,IAAI,IAAI,eAAe;AAC5C,MAAI;AACJ,QAAM,UAAU,IAAI,QAAQ,CAAC,aAAa;AACtC,cAAU;AAAA,EACd,CAAC;AACD,QAAM,OAAO,MAAM;AACf,YAAQ;AACR,UAAM,MAAM;AAAA,EAChB;AACA,QAAM,UAAU,WAAW,MAAM;AAC7B,UAAM,YAAY,QAAQ;AAC1B,YAAQ,MAAM;AACd,SAAK;AAAA,EACT,GAAG,kBAAkB;AACrB,QAAM,iBAAiB,WAAW,CAAC,UAAU;AACzC,UAAM,OAAO,MAAM;AACnB,QAAI,QAAQ,QAAQ,OAAO,SAAS,UAAU;AAC1C;AAAA,IACJ;AACA,QAAI,KAAK,SAAS,gCAAgC;AAC9C,mBAAa,OAAO;AACpB,WAAK;AAAA,IACT;AAAA,EACJ,CAAC;AACD,gBAAQ,kBAAR,mBAAuB,YAAY;AAAA,IAC/B,MAAM;AAAA,IACN,UAAU;AAAA,EACd,GAAG,KAAK,CAAC,KAAK;AACd,SAAO;AACX;;;ACnCA,IAAM,wBAAwB;AAAA,EAC1B,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,mBAAmB;AACvB;AACO,SAAS,iBAAiB,MAAM;AACnC,MAAI,QAAQ,QAAQ,OAAO,SAAS,UAAU;AAC1C,WAAO;AAAA,EACX;AACA,MAAI,EAAE,UAAU,SAAS,CAAC,sBAAsB,SAAS,KAAK,IAAI,GAAG;AACjE,WAAO;AAAA,EACX;AACA,SAAO;AACX;;;ACXO,SAAS,cAAc,QAAQ;AAClC,QAAM,YAAY,uBAAO,OAAO,IAAI;AACpC,MAAI,CAAC,QAAQ;AACT,WAAO;AAAA,EACX;AACA,SAAO,OAAO,OAAO,WAAW,MAAM;AAC1C;;;ACRA,IAAM,gBAAgB,IAAI,YAAY,QAAQ;AAIvC,SAAS,yBAAyB,MAAM;AAC3C,QAAM,UAAU,EAAE,GAAG,CAAC,EAAE;AACxB,aAAW,QAAQ,OAAO,KAAK,IAAI,GAAG;AAClC,UAAM,QAAQ,KAAK,IAAI;AACvB,QAAI,UAAU,OAAO;AACjB,UAAI,aAAa,MAAM,MAAM;AACzB,gBAAQ,EAAE,IAAI,IAAI,EAAE,GAAG,EAAE,GAAG,MAAM,KAAK,QAAQ,EAAE;AACjD;AAAA,MACJ;AACA,YAAM,WAAW,MAAM,KAAK;AAC5B,YAAM,iBAAiB,OAAO,aAAa,WAAW,WAAW,cAAc,OAAO,QAAQ;AAC9F,YAAM,SAAS,OAAO,aAAa,WAAW,CAAC,IAAI,EAAE,GAAG,KAAK;AAC7D,cAAQ,EAAE,IAAI,IAAI,EAAE,GAAG,EAAE,GAAG,gBAAgB,GAAG,OAAO,EAAE;AACxD;AAAA,IACJ;AACA,UAAM,WAAW,yBAAyB,MAAM,SAAS;AACzD,YAAQ,EAAE,IAAI,IAAI;AAAA,EACtB;AACA,SAAO;AACX;AAIO,SAAS,yBAAyB,MAAM;AAC3C,QAAM,UAAU,cAAc;AAC9B,MAAI,OAAO,MAAM;AACb,UAAM,IAAI,MAAM,gEAAgE;AAAA,EACpF;AACA,MAAI,OAAO,MAAM;AACb,eAAW,QAAQ,OAAO,KAAK,KAAK,CAAC,GAAG;AACpC,YAAM,QAAQ,KAAK,EAAE,IAAI;AACzB,UAAI,OAAO,OAAO;AACd,gBAAQ,IAAI,IAAI,cAAc;AAAA,UAC1B,WAAW,yBAAyB,KAAK;AAAA,QAC7C,CAAC;AAAA,MACL,WACS,OAAO,OAAO;AACnB,YAAI,OAAO,MAAM,GAAG;AAChB,kBAAQ,IAAI,IAAI,cAAc;AAAA,YAC1B,MAAM,cAAc;AAAA,cAChB,UAAU,MAAM,EAAE,IAAI,iBAAiB,MAAM,EAAE,CAAC,IAAI,MAAM,EAAE;AAAA,YAChE,CAAC;AAAA,UACL,CAAC;AAAA,QACL,WACS,OAAO,MAAM,GAAG;AACrB,kBAAQ,IAAI,IAAI,cAAc;AAAA,YAC1B,MAAM,cAAc;AAAA,cAChB,SAAS,MAAM,EAAE;AAAA,YACrB,CAAC;AAAA,UACL,CAAC;AAAA,QACL;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,iBAAiB,GAAG;AACzB,QAAM,UAAU,IAAI,WAAW,EAAE,MAAM;AACvC,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AAC/B,YAAQ,CAAC,IAAI,EAAE,CAAC,EAAE,WAAW,CAAC;AAAA,EAClC;AACA,SAAO;AACX;;;ACpDO,IAAMC,QAAO;AAGpB,IAAI,cAAc;AAClB,IAAI,sBAAsB;AAC1B,IAAI,oBAAoB,CAAC;AACzB,IAAM,UAAU,IAAI,YAAY;AAChC,IAAM,UAAU,IAAI,YAAY;AAKzB,IAAM,gBAAN,MAAM,cAAa;AAAA;AAAA,EActB,YAEA,WAAW,IAAI,eAEf,cAAc;AAjBd;AACA;AAIA;AAAA;AAAA;AAAA;AAKA,qCAAY;AACZ,gEAAuC,MAAM;AAAA,IAAE;AAO3C,SAAK,YAAY;AACjB,SAAK,eAAe;AACpB,SAAK,KAAK,IAAI,oBAAoB,EAAE;AAEpC,QAAI,UAAU,aAAa;AACvB,WAAK,uCAAuC,8BAA8B,CAAC,gBAAgB;AACvF,aAAK,UAAU,eAAe,EAAE,aAAa,cAAc,UAAU,aAAa,CAAC;AAAA,MACvF,CAAC;AACD,OAAC,YAAY;AACT,cAAM,UAAU,aAAa;AAC7B,YAAI,KAAK,WAAW;AAChB;AAAA,QACJ;AACA,yBAAiB,UAAU,MAAM;AACjC,cAAM,KAAK,UAAU,eAAe;AAAA,UAChC,aAAa,UAAU,OAAO;AAAA,UAC9B,cAAc,UAAU;AAAA,QAC5B,CAAC;AAAA,MACL,GAAG,EAAE,MAAM,CAAC,UAAU;AAElB,gBAAQ,MAAM,KAAK;AAAA,MACvB,CAAC;AAAA,IACL;AAAA,EACJ;AAAA,EACA,MAAM,MAAM,SAAS,eAAe,SAAS;AACzC,QAAI,OAAO,CAAC;AACZ,QAAI,MAAM,QAAQ,aAAa,GAAG;AAC9B,aAAO;AAAA,IACX,OACK;AACD,gBAAU;AAAA,IACd;AACA,QAAI,SAAS;AACb,QAAI,eAAe,IAAI,eAAe;AACtC,SAAI,mCAAS,YAAW,OAAO;AAC3B,YAAM,SAAS,eAAe;AAC9B,eAAS,OAAO;AAChB,qBAAe,OAAO;AAAA,IAC1B;AACA,QAAI,SAAS;AACb,QAAI;AACJ,QAAI,SAAS;AACb,QAAI;AACJ,UAAM,gBAAgB,cAAc,eAAe,MAAM,CAAC;AAC1D,UAAM,gBAAgB,cAAc,eAAe,MAAM,CAAC;AAC1D,UAAM,gBAAgB,cAAc,eAAe,MAAM,CAAC;AAC1D,UAAM,UAAU,MAAM,KAAK,UAAU,IAAI;AAAA,MACrC;AAAA,MACA;AAAA,MACA,KAAK,mCAAS;AAAA,MACd,KAAK,mCAAS;AAAA,MACd,UAAU,mCAAS;AAAA,IACvB,GAAG,eAAe,eAAe,aAAa;AAC9C,WAAO,IAAI,wBAAwB,SAAS,cAAc,cAAc,YAAY;AAAA,EACxF;AAAA,EACA,MAAM,OAAO,MAAM,SAAS;AACxB,UAAM,mBAAmB;AAAA,MACrB,SAAQ,mCAAS,WAAU;AAAA,MAC3B,UAAU,mCAAS;AAAA,MACnB,UAAU,mCAAS;AAAA,MACnB,UAAU;AAAA,IACd;AACA,UAAM,SAAS,MAAM,KAAK,UAAU,UAAU,MAAM,gBAAgB;AACpE,QAAI,iBAAiB,WAAW,QAAQ;AACpC,YAAM,OAAO,KAAK,MAAM,QAAQ,OAAO,MAAM,CAAC;AAC9C,aAAO,yBAAyB,IAAI;AAAA,IACxC;AACA,WAAO;AAAA,EACX;AAAA,EACA,GAAG,OAAO,UAAU;AAChB,QAAI,UAAU,mBAAmB;AAC7B,YAAM,mBAAmB;AACzB,iBAAY,CAAC,YAAY;AACrB,YAAI,iBAAiB,OAAO,GAAG;AAC3B,2BAAiB,OAAO;AAAA,QAC5B;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,EAAE,UAAU,SAAS,UAAU,IAAI,iBAAiB,QAAQ;AAClE,WAAO,UAAU,KAAK,UAAU,GAAG,OAAO,gBAAQ,MAAM,OAAO,CAAC,CAAC;AAAA,EACrE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,gBAAgB,SAAS;AAC3B,UAAM,UAAU,0BAA0B,aACpC,iBACA,0BAA0B,cACtB,IAAI,WAAW,cAAc,IAC7B,QAAQ,OAAO,KAAK,UAAU,yBAAyB,cAAc,CAAC,CAAC;AACjF,WAAO,KAAK,UAAU,UAAU,gBAAQ,SAAS,SAAS,CAAC,QAAQ,MAAM,CAAC,GAAG;AAAA,MACzE,aAAa,mCAAS;AAAA,IAC1B,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,iBAAiB,WAAW,SAAS;AACjC,WAAO,KAAK,UAAU,iBAAiB,WAAW,OAAO;AAAA,EAC7D;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,OAAO;AACP,WAAO,KAAK,aAAa;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,UAAU;AACV,WAAO,KAAK,aAAa;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW;AACP,QAAI,KAAK,WAAW;AAChB,YAAM,IAAI,MAAM,gCAAgC;AAAA,IACpD;AACA,SAAK,YAAY;AACjB,SAAK,qCAAqC;AAC1C,UAAM,aAAa,YAAY;AAC3B,UAAI;AACA,cAAM,KAAK,GAAG,UAAU;AACxB,cAAM,KAAK,UAAU,SAAS;AAAA,MAClC,UACA;AACI,aAAK,UAAU,gBAAQ,YAAY,EAAE;AACrC,YAAI,cAAa,cAAc,MAAM;AACjC,wBAAa,YAAY;AAAA,QAC7B;AAAA,MACJ;AAAA,IACJ;AACA,kBAAa,mBAAmB,WAAW;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa,KAAK,UAAU,CAAC,GAAG;AAC5B,UAAM,KAAK;AACX,kBAAa,mBAAmB;AAChC,UAAM,EAAE,YAAY,IAAI;AACxB,QAAI,OAAO,uBAAuB,QAAQ,SAAS,QAAQ;AACvD,cAAQ,KAAK;AAAA,yCAAoI;AAAA,IACrJ;AACA,SAAI,2CAAa,SAAS,SAAQ,gBAAgB,QAAQ,gBAAgB,KAAK;AAC3E,YAAM,IAAI,MAAM,2CAA2C;AAAA,IAC/D;AAEA,cAAU,aAAa;AAEvB,WAAO,aAAa;AAChB,YAAM;AAAA,IACV;AACA,QAAI,cAAa,WAAW;AACxB,YAAM,IAAI,MAAM,mDAAmD;AAAA,IACvE;AACA,UAAM,kBAAkB,mBAAmB,OAAO;AAElD,kBAAc,gBAAgB,MAAM,MAAM;AAAA,IAAE,CAAC;AAC7C,QAAI;AACA,YAAM,WAAW,MAAM;AACvB,oBAAa,YAAY;AACzB,aAAO;AAAA,IACX,UACA;AAEI,oBAAc;AAAA,IAClB;AAAA,EACJ;AACJ;AAAA;AA3MI,cARS,eAQF,aAAY;AAAA;AAEnB,cAVS,eAUF,oBAAmB;AAVvB,IAAM,eAAN;AAyNA,SAAS,gBAAgB,KAAK;AACjC,MAAI,UAAU,YAAY;AACtB,UAAM,IAAI,MAAM,sEAAsE;AAAA,EAC1F;AACA,iBAAe,cAAc,aAAa,GAAG;AACjD;AACA,IAAM,sBAAsB;AAC5B,IAAM,qBAAqB;AAI3B,IAAM,aAAN,MAAiB;AAAA,EAGb,YAAY,MAAM,OAAO;AAFzB;AACA;AAEI,SAAK,OAAO;AACZ,SAAK,QAAQ;AAAA,EACjB;AAAA,EACA,SAAS;AACL,WAAO,KAAK,UAAU;AAAA,EAC1B;AAAA,EACA,cAAc;AACV,WAAO,KAAK,UAAU;AAAA,EAC1B;AACJ;AACA,IAAM,YAAN,MAAgB;AAAA,EAQZ,YAAY,YAAY,OAAO,UAAU,WAAW;AAPpD;AACA;AACA;AACA;AACA;AACA;AACA,mCAAU;AAEN,SAAK,aAAa;AAClB,SAAK,QAAQ;AACb,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,WAAW,UAAU,IAAI,IAAI;AAClC,SAAK,mBAAmB,CAAC,OAAO,aAAa;AACzC,UAAI,KAAK,aAAa,CAAC,KAAK,SAAS;AACjC,aAAK,UAAU,OAAO,QAAQ;AAAA,MAClC;AAAA,IACJ;AACA,SAAK,WAAW,IACX,MAAM,KAAK,OAAO,KAAK,UAAU,cAAc,KAAK,gBAAgB,CAAC,EACrE,KAAK,CAAC,aAAa;AACpB,WAAK,WAAW;AAChB,UAAI,KAAK,SAAS;AACd,eAAO,KAAK,UAAU;AAAA,MAC1B;AACA,aAAO;AAAA,IACX,CAAC,EACI,MAAM,QAAQ,KAAK;AAAA,EAC5B;AAAA,EACA,MAAM,QAAQ;AACV,QAAI,CAAC,KAAK,SAAS;AACf,WAAK,UAAU;AACf,WAAK,WAAW,UAAU,OAAO,IAAI;AACrC,YAAM,KAAK,UAAU;AAAA,IACzB;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM,YAAY;AArTtB;AAsTQ,YAAM,UAAK,aAAL,mBAAe,QAAQ,QAAQ,MAAM;AAtTnD,UAAAC;AAuTY,OAAAA,MAAA,KAAK,aAAL,gBAAAA,IAAgB,gBAAQ;AAAA,IAC5B;AAAA,EACJ;AACJ;AAIA,IAAM,0BAAN,MAA8B;AAAA,EAO1B,YAAY,SAAS,QAAQ,QAAQ,QAAQ;AAN7C;AACA;AACA;AACA;AACA;AACA;AAEI,SAAK,SAAS;AACd,SAAK,WAAW;AAChB,SAAK,QAAQ,IAAI,eAAe;AAAA,MAC5B,OAAO,CAAC,SAAS;AAzU7B;AA2UgB,mBAAK,YAAY,MAAjB,mBACM,MAAM,MACP,MAAM,MAAM;AAAA,QAAE;AAAA,MACvB;AAAA,IACJ,CAAC;AACD,SAAK,OAAO,KAAK,QAAQ;AACzB,SAAK,SAAS;AACd,SAAK,SAAS;AAAA,EAClB;AAAA,EACA,OAAO;AApVX;AAqVQ,eAAK,aAAL,mBAAe;AAAA,EACnB;AAAA,EACA,OAAO,YAAY;AAvVvB;AAwVQ,eAAK,YAAY,MAAjB,mBAAoB,OAAO;AAAA,EAC/B;AAAA,EACA,MAAM,UAAU;AA1VpB;AA2VQ,QAAI;AACA,aAAO,MAAM,KAAK,SAAS;AAAA,IAC/B,UACA;AACI,iBAAK,aAAL,mBAAgB,gBAAQ;AACxB,WAAK,WAAW;AAAA,IACpB;AAAA,EACJ;AAAA,EACA,cAAc;AACV,QAAI,KAAK,YAAY,MAAM;AACvB,cAAQ,KAAK,6BAA6B;AAAA,IAC9C;AACA,WAAO,KAAK;AAAA,EAChB;AACJ;AAIA,IAAM,sBAAN,MAA0B;AAAA,EAGtB,YAAY,IAAI;AAFhB;AACA,qCAAY,oBAAI,IAAI,CAAC,CAAC;AAElB,SAAK,MAAM;AAAA,EACf;AAAA,EACA,MAAM,MAAM;AACR,WAAO,KAAK,IAAI,GAAG,GAAG,IAAI;AAAA,EAC9B;AAAA,EACA,MAAM,SAAS,MAAM,UAAU;AAC3B,WAAO,MAAM,KAAK,IAAI,SAAS,MAAM,QAAQ;AAAA,EACjD;AAAA,EACA,MAAM,OAAO,SAAS,SAAS;AAC3B,WAAO,MAAM,KAAK,IAAI,OAAO,SAAS,OAAO;AAAA,EACjD;AAAA,EACA,MAAM,UAAU,MAAM,MAAM,SAAS;AACjC,QAAI,gBAAgB,YAAY;AAC5B,YAAM,SAAS,KAAK,OAAO,MAAM,KAAK,YAAY,KAAK,aAAa,KAAK,UAAU;AACnF,aAAO,gBAAQ,SAAS,IAAI,WAAW,MAAM,GAAG,CAAC,MAAM,CAAC;AAAA,IAC5D;AACA,UAAM,KAAK,IAAI,UAAU,MAAM,MAAM,OAAO;AAAA,EAChD;AAAA,EACA,MAAM,QAAQ,MAAM,SAAS;AACzB,UAAM,SAAS,MAAM,KAAK,IAAI,QAAQ,MAAM,OAAO;AACnD,QAAI,cAAc,MAAM,GAAG;AACvB,aAAO;AAAA,IACX;AACA,QAAI,uBAAuB,MAAM,GAAG;AAChC,aAAO;AAAA,IACX;AACA,UAAM,UAAU,OAAO,IAAI,CAAC,UAAU,IAAI,WAAW,MAAM,MAAM,MAAM,cAAc,CAAC,CAAC;AACvF,WAAO;AAAA,EACX;AAAA,EACA,MAAM,MAAM,MAAM,SAAS;AACvB,WAAO,MAAM,KAAK,IAAI,MAAM,MAAM,OAAO;AAAA,EAC7C;AAAA,EACA,MAAM,MAAM,SAAS,UAAU;AAC3B,QAAI,OAAO,YAAY,YAAY;AAC/B,iBAAW;AACX,gBAAU;AAAA,IACd;AACA,WAAO,IAAI,UAAU,MAAM,MAAM,SAAS,QAAQ;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM,YAAY;AACd,SAAK,IAAI,gBAAQ,YAAY,EAAE;AAC/B,UAAM,QAAQ,IAAI,CAAC,GAAG,KAAK,SAAS,EAAE,IAAI,CAAC,YAAY,QAAQ,MAAM,CAAC,CAAC;AAAA,EAC3E;AACJ;AACA,eAAe,mBAAmB,SAAS;AACvC,QAAM,EAAE,cAAc,IAAI,cAAc,OAAO;AAC/C,QAAM,SAAS,MAAM;AACrB,QAAM,WAAW,MAAM,OAAO,MAAM;AAAA,IAChC,MAAM,OAAO,SAAS;AAAA,IACtB,SAAS;AAAA,IACT,aAAa,QAAQ;AAAA,IACrB,sBAAsB,QAAQ;AAAA,EAClC,CAAC;AACD,QAAM,CAAC,IAAI,eAAe,WAAW,IAAI,MAAM,QAAQ,IAAI;AAAA,IACvD,SAAS,GAAG;AAAA,IACZ,SAAS,cAAc;AAAA,IACvB,SAAS,YAAY;AAAA,EACzB,CAAC;AACD,SAAO,IAAI,aAAa,UAAU,IAAI,eAAe,WAAW;AACpE;AACA,SAAS,eAAe,UAAU;AAC9B,MAAI,YAAY,MAAM;AAClB,WAAO;AAAA,EACX;AACA,SAAO,CAAC,SAAS;AACb,QAAI,gBAAgB,YAAY;AAC5B,eAAS,QAAQ,OAAO,IAAI,CAAC;AAAA,IACjC,WACS,QAAQ,MAAM;AACnB,eAAS,IAAI;AAAA,IACjB;AAAA,EACJ;AACJ;AACA,SAAS,cAAc,UAAU;AAC7B,MAAI,YAAY,MAAM;AAClB,WAAO;AAAA,EACX;AACA,SAAO,gBAAQ,MAAM,QAAQ;AACjC;AACA,SAAS,cAAc,SAAS;AAC5B,MAAI,uBAAuB,MAAM;AAC7B,QAAI,QAAQ,SAAS,kBAAkB,MAAM;AACzC,cAAQ,KAAK,+CAA+C,QAAQ,IAAI,GAAG;AAC3E,cAAQ,KAAK,yBAAyB,kBAAkB,IAAI,uCAAuC;AAAA,IACvG;AACA,WAAO,EAAE,eAAe,oBAAoB;AAAA,EAChD;AACA,MAAI,QAAQ,MAAM;AACd,mBAAe,cAAc,QAAQ,QAAQ,IAAI;AAAA,EACrD;AACA,MAAI,QAAQ,kBAAkB;AAC1B,mBAAe,cAAc,qBAAqB,GAAG;AAAA,EACzD;AACA,QAAM,SAAS,SAAS,cAAc,QAAQ;AAC9C,SAAO,MAAM,UAAU;AACvB,SAAO,aAAa,SAAS,uBAAuB;AACpD,QAAM,MAAM,eAAe;AAC3B,SAAO,MAAM,IAAI,SAAS;AAC1B,QAAM,EAAE,OAAO,IAAI;AACnB,sBAAoB,EAAE,GAAG,QAAQ;AACjC,wBAAsB,IAAI,QAAQ,CAAC,YAAY;AAC3C,UAAM,YAAY,CAAC,UAAU;AACzB,UAAI,MAAM,WAAW,QAAQ;AACzB;AAAA,MACJ;AACA,YAAM,EAAE,KAAK,IAAI;AACjB,UAAI,KAAK,SAAS,QAAQ;AACtB,gBAAQ,gBAAQ,KAAK,MAAM,MAAM,CAAC,CAAC,CAAC;AACpC;AAAA,MACJ;AACA,UAAI,KAAK,SAAS,WAAW;AACzB,gBAAQ,KAAK,KAAK,EAAE,KAAK,SAAS,KAAK,OAAO;AAC9C;AAAA,MACJ;AAAA,IACJ;AACA,WAAO,iBAAiB,WAAW,SAAS;AAAA,EAChD,CAAC;AACD,WAAS,KAAK,aAAa,QAAQ,IAAI;AACvC,SAAO,EAAE,eAAe,oBAAoB;AAChD;AACA,SAAS,cAAc,MAAM;AACzB,SAAO,OAAO,KAAK,CAAC,MAAM;AAC9B;AACA,SAAS,uBAAuB,MAAM;AAClC,SAAO,KAAK,CAAC,aAAa;AAC9B;AACA,SAAS,iBAAiB;AACtB,MAAI,aAAa;AACjB,QAAM,SAAS,IAAI,eAAe;AAAA,IAC9B,MAAM,aAAa;AACf,mBAAa;AAAA,IACjB;AAAA,EACJ,CAAC;AACD,QAAM,OAAO,CAAC,SAAS;AACnB,QAAI,QAAQ,MAAM;AACd,+CAAY,QAAQ;AAAA,IACxB,OACK;AACD,+CAAY;AACZ,mBAAa;AAAA,IACjB;AAAA,EACJ;AACA,SAAO,EAAE,QAAQ,KAAK;AAC1B;AACA,SAAS,iBAAiB,UAAU;AAChC,MAAI,UAAU;AACd,MAAI,cAAc,MAAM;AAAA,EAAE;AAC1B,QAAM,UAAW,IAAI,SAAS;AAC1B,QAAI,SAAS;AACT;AAAA,IACJ;AACA,aAAS,GAAG,IAAI;AAAA,EACpB;AACA,SAAO;AAAA,IACH,UAAU,SAAS;AACf,cAAQ,KAAK,CAAC,iBAAiB;AAC3B,sBAAc;AACd,YAAI,SAAS;AACT,sBAAY;AAAA,QAChB;AAAA,MACJ,CAAC;AACD,aAAO,MAAM;AACT,kBAAU;AACV,oBAAY;AAAA,MAChB;AAAA,IACJ;AAAA,IACA,UAAU;AAAA,EACd;AACJ;", "names": ["editor<PERSON><PERSON><PERSON>", "editor<PERSON><PERSON><PERSON>", "PreviewMessageType", "auth", "_a"]}