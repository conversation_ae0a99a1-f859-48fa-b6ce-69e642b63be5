#!/usr/bin/env python3
"""
Clara Backend - Version Propre (RAG Premium uniquement)
Suppression complète de ChromaDB et DocumentAI legacy
"""

import os
import sys
import json
import logging
import signal
import sqlite3
import traceback
import time
import tempfile
import shutil
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional

from fastapi import FastAPI, File, UploadFile, Form, HTTPException
from fastapi.responses import JSONResponse, Response
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

# Configuration
HOST = "127.0.0.1"
PORT = 8000
START_TIME = datetime.now().isoformat()

# Logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("clara-backend")

# Import RAG Premium Service (LightRAG + Qdrant + BGE-M3)
try:
    from rag_premium_service import PremiumRAGService
    RAG_UNIFIED_AVAILABLE = True
    logger.info("✅ RAG Premium Service available (LightRAG + Qdrant + BGE-M3)")
    
    # Instance globale du service premium
    _rag_premium_service = None
    
    async def get_rag_premium_service():
        """Obtenir l'instance du service RAG premium"""
        global _rag_premium_service
        if _rag_premium_service is None:
            _rag_premium_service = PremiumRAGService()
            await _rag_premium_service.initialize()
        return _rag_premium_service
        
except ImportError as e:
    RAG_UNIFIED_AVAILABLE = False
    logger.warning(f"❌ RAG Premium Service not available: {e}")
    
    async def get_rag_premium_service():
        raise RuntimeError("RAG Premium Service not available")

# FastAPI app
app = FastAPI(title="Clara Backend - Clean", version="2.0.0")

# CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Database setup
home_dir = os.path.expanduser("~")
data_dir = os.path.join(home_dir, ".clara")
os.makedirs(data_dir, exist_ok=True)
DATABASE = os.path.join(data_dir, "clara.db")

MAX_RETRIES = 3

from contextlib import contextmanager

@contextmanager
def get_db_connection(timeout=30.0):
    """Get database connection with retry logic"""
    conn = None
    try:
        conn = sqlite3.connect(DATABASE, timeout=timeout)
        conn.row_factory = sqlite3.Row
        conn.execute('PRAGMA journal_mode=WAL')
        conn.execute('PRAGMA synchronous=NORMAL')
        conn.execute('PRAGMA cache_size=10000')
        yield conn
    finally:
        if conn:
            conn.close()

def init_database():
    """Initialize database tables"""
    with get_db_connection() as conn:
        cursor = conn.cursor()
        
        # Collections table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS collections (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                description TEXT,
                document_count INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Documents table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS documents (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                filename TEXT NOT NULL,
                file_type TEXT,
                collection_name TEXT,
                content TEXT,
                metadata TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (collection_name) REFERENCES collections (name)
            )
        """)

        # Ajouter la colonne content si elle n'existe pas (migration)
        try:
            cursor.execute("ALTER TABLE documents ADD COLUMN content TEXT")
            logger.info("✅ Colonne 'content' ajoutée à la table documents")
        except sqlite3.OperationalError as e:
            if "duplicate column name" in str(e).lower():
                logger.info("✅ Colonne 'content' existe déjà")
            else:
                logger.warning(f"⚠️ Erreur ajout colonne content: {e}")
        
        # Test table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS test (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                value TEXT
            )
        """)
        
        # Insert test data
        cursor.execute("SELECT COUNT(*) FROM test")
        count = cursor.fetchone()[0]
        if count == 0:
            cursor.execute("INSERT INTO test (value) VALUES ('Hello from SQLite')")
        
        conn.commit()
        logger.info("Database initialized successfully")

# Initialize database
init_database()

# Pydantic models
class CollectionCreate(BaseModel):
    name: str
    description: Optional[str] = None

# ============================================================================
# ENDPOINTS PRINCIPAUX
# ============================================================================

@app.get("/")
def read_root():
    """Root endpoint for basic health check"""
    return {
        "status": "ok", 
        "service": "Clara Backend Clean", 
        "version": "2.0.0",
        "port": PORT,
        "uptime": str(datetime.now() - datetime.fromisoformat(START_TIME)),
        "start_time": START_TIME,
        "rag_available": RAG_UNIFIED_AVAILABLE
    }

@app.get("/health")
def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "port": PORT,
        "uptime": str(datetime.now() - datetime.fromisoformat(START_TIME)),
        "rag_available": RAG_UNIFIED_AVAILABLE
    }

# 🔧 CORRECTION: Ajouter l'endpoint /files manquant pour éviter les erreurs 404
@app.get("/files")
def list_files():
    """Endpoint temporaire pour éviter les erreurs 404 - retourne une liste vide"""
    return []

# ============================================================================
# COLLECTIONS
# ============================================================================

@app.post("/collections")
async def create_collection(collection: CollectionCreate):
    """Create a new collection"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            
            # Check if collection exists
            cursor.execute("SELECT name FROM collections WHERE name = ?", (collection.name,))
            existing = cursor.fetchone()
            
            if existing:
                return JSONResponse(
                    status_code=409,
                    content={"detail": f"Collection '{collection.name}' already exists"}
                )
            
            # Create the collection
            cursor.execute(
                "INSERT INTO collections (name, description) VALUES (?, ?)",
                (collection.name, collection.description or "")
            )
            conn.commit()
            
            return {"message": f"Collection '{collection.name}' created successfully"}
            
    except Exception as e:
        logger.error(f"Error creating collection: {e}")
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")

@app.get("/collections")
def list_collections():
    """List all available document collections"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT name, description, document_count, created_at FROM collections")
            collections = [dict(row) for row in cursor.fetchall()]
            return {"collections": collections}
    except Exception as e:
        logger.error(f"Error listing collections: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/collections/{collection_name}")
async def delete_collection(collection_name: str):
    """Delete a collection and all its documents"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            
            # 🚀 SUPPRIMER DU RAG PREMIUM
            if RAG_UNIFIED_AVAILABLE:
                try:
                    # Get all documents in this collection
                    cursor.execute("SELECT filename FROM documents WHERE collection_name = ?", (collection_name,))
                    filenames = [row['filename'] for row in cursor.fetchall()]
                    
                    rag_service = await get_rag_premium_service()
                    for filename in filenames:
                        await rag_service.delete_document(filename)
                    
                    logger.info(f"🗑️ Collection {collection_name} supprimée du RAG Premium")
                except Exception as e:
                    logger.warning(f"Échec suppression RAG Premium: {e}")
            
            # Delete from SQLite
            cursor.execute("DELETE FROM documents WHERE collection_name = ?", (collection_name,))
            cursor.execute("DELETE FROM collections WHERE name = ?", (collection_name,))
            conn.commit()
            
        return {"message": f"Collection {collection_name} deleted successfully"}
        
    except Exception as e:
        logger.error(f"Error deleting collection: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# ============================================================================
# DOCUMENTS
# ============================================================================

@app.post("/documents/upload")
async def upload_document(
    file: UploadFile = File(...),
    collection_name: str = Form("default_collection"),
    metadata: str = Form("{}"),
    use_ocr: bool = Form(True),
    ocr_language: str = Form("fra"),
    preserve_layout: bool = Form(True)
):
    """Upload a document file (PDF, CSV, or plain text) and add it to RAG Premium"""
    # Check if collection exists, create if not
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM collections WHERE name = ?", (collection_name,))
            if not cursor.fetchone():
                cursor.execute(
                    "INSERT INTO collections (name, description) VALUES (?, ?)",
                    (collection_name, f"Auto-created for {file.filename}")
                )
                conn.commit()
    except Exception as e:
        logger.error(f"Error checking/creating collection: {e}")
        raise HTTPException(status_code=500, detail=str(e))

    # Create a temporary directory to save the uploaded file
    with tempfile.TemporaryDirectory() as temp_dir:
        file_path = Path(temp_dir) / file.filename

        # Save uploaded file
        try:
            with open(file_path, "wb") as f:
                shutil.copyfileobj(file.file, f)
        except Exception as e:
            logger.error(f"Error saving file: {e}")
            raise HTTPException(status_code=500, detail=f"Could not save file: {str(e)}")

        # Process the file based on extension
        file_extension = file.filename.lower().split('.')[-1]
        documents = []
        file_type = file_extension

        try:
            if file_extension == 'pdf':
                if use_ocr:
                    # Utiliser OCR Premium pour PDF
                    from ocr_service import ocr_processor

                    logger.info(f"Traitement OCR du PDF: {file.filename}")
                    ocr_result = await ocr_processor.process_document(
                        str(file_path),
                        language=ocr_language,
                        preserve_layout=preserve_layout
                    )

                    if ocr_result.success:
                        # Créer document avec texte OCR
                        from langchain_core.documents import Document
                        documents = [Document(
                            page_content=ocr_result.full_text,
                            metadata={
                                "source_file": file.filename,
                                "file_type": file_extension,
                                "processed_with_ocr": True,
                                "ocr_language": ocr_language,
                                "total_pages": ocr_result.total_pages,
                                "processing_time": ocr_result.processing_time,
                                "ocr_confidence": ocr_result.confidence,
                                "text_blocks_count": len(ocr_result.text_blocks)
                            }
                        )]
                        logger.info(f"OCR réussi: {len(ocr_result.full_text)} caractères extraits")
                    else:
                        logger.warning(f"OCR échoué: {ocr_result.error_message}")
                        # Fallback vers extraction simple
                        from langchain_community.document_loaders import PyPDFLoader
                        loader = PyPDFLoader(str(file_path))
                        documents = loader.load()
                        for doc in documents:
                            doc.metadata["ocr_attempted"] = True
                            doc.metadata["ocr_failed"] = True
                            doc.metadata["ocr_error"] = ocr_result.error_message
                else:
                    # Extraction PDF simple
                    from langchain_community.document_loaders import PyPDFLoader
                    loader = PyPDFLoader(str(file_path))
                    documents = loader.load()
            elif file_extension == 'csv':
                from langchain_community.document_loaders import CSVLoader
                loader = CSVLoader(file_path=str(file_path))
                documents = loader.load()
            elif file_extension in ['txt', 'md', 'html']:
                from langchain_community.document_loaders import TextLoader
                # Forcer l'encodage UTF-8 pour éviter les problèmes d'encodage
                loader = TextLoader(str(file_path), encoding='utf-8')
                documents = loader.load()
            else:
                raise HTTPException(status_code=400, detail=f"Unsupported file type: {file_extension}")

            # Parse metadata if provided
            try:
                meta_dict = json.loads(metadata)

                # Add file metadata to each document
                for doc in documents:
                    doc.metadata.update(meta_dict)
                    doc.metadata["source_file"] = file.filename
                    doc.metadata["file_type"] = file_extension
            except json.JSONDecodeError:
                logger.warning(f"Invalid metadata JSON: {metadata}")

            # 🚀 INTÉGRATION RAG PREMIUM
            if RAG_UNIFIED_AVAILABLE:
                try:
                    rag_service = await get_rag_premium_service()

                    # Ajouter au système RAG Premium
                    for doc in documents:
                        await rag_service.add_document(
                            content=doc.page_content,
                            metadata={
                                **doc.metadata,
                                "collection_name": collection_name,
                                "document_id": None  # Sera mis à jour après insertion DB
                            }
                        )
                    logger.info(f"✅ Document ajouté au RAG Premium: {file.filename}")
                except Exception as e:
                    logger.warning(f"⚠️ Échec ajout RAG Premium: {e}")

            # Combiner tout le contenu des documents
            full_content = "\n\n".join([doc.page_content for doc in documents])

            # Update database
            with get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    "INSERT INTO documents (filename, file_type, collection_name, content, metadata) VALUES (?, ?, ?, ?, ?)",
                    (file.filename, file_type, collection_name, full_content, metadata)
                )
                document_id = cursor.lastrowid

                # Update document count in collection
                cursor.execute(
                    "UPDATE collections SET document_count = document_count + 1 WHERE name = ?",
                    (collection_name,)
                )
                conn.commit()

            # Préparer la réponse avec informations OCR
            response_data = {
                "status": "success",
                "filename": file.filename,
                "collection": collection_name,
                "document_count": len(documents),
                "document_id": document_id,
                "processed_with_ocr": False,
                "text_length": len(full_content)
            }

            # Ajouter informations OCR si utilisé
            if documents and documents[0].metadata.get("processed_with_ocr"):
                response_data.update({
                    "processed_with_ocr": True,
                    "ocr_language": documents[0].metadata.get("ocr_language"),
                    "total_pages": documents[0].metadata.get("total_pages"),
                    "processing_time": documents[0].metadata.get("processing_time"),
                    "ocr_confidence": documents[0].metadata.get("ocr_confidence"),
                    "text_length": len(full_content)
                })

            return response_data

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error processing document: {str(e)}")
            logger.error(traceback.format_exc())
            raise HTTPException(status_code=500, detail=f"Error processing document: {str(e)}")

@app.get("/documents")
async def list_documents(collection_name: Optional[str] = None):
    """List all documents, optionally filtered by collection"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()

            query = """
                SELECT id, filename, file_type, collection_name, content, metadata, created_at
                FROM documents
            """

            params = []
            if collection_name:
                query += " WHERE collection_name = ?"
                params.append(collection_name)

            query += " ORDER BY created_at DESC"

            cursor.execute(query, params)
            documents = []

            for row in cursor.fetchall():
                doc_dict = dict(row)
                # Parse metadata JSON
                try:
                    doc_dict['metadata'] = json.loads(doc_dict['metadata']) if doc_dict['metadata'] else {}
                except:
                    doc_dict['metadata'] = {}
                documents.append(doc_dict)

            return {"documents": documents}
    except Exception as e:
        logger.error(f"Error listing documents: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error listing documents: {str(e)}")

@app.get("/documents/{document_id}")
async def get_document(document_id: int):
    """Get a document by ID from SQLite"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT id, filename, file_type, collection_name, content,
                       created_at, metadata
                FROM documents
                WHERE id = ?
            """, (document_id,))

            row = cursor.fetchone()
            if not row:
                raise HTTPException(status_code=404, detail=f"Document with ID {document_id} not found")

            # Convert row to dict
            document = {
                "id": row[0],
                "filename": row[1],
                "file_type": row[2],
                "collection_name": row[3],
                "content": row[4],
                "created_at": row[5],
                "metadata": json.loads(row[6]) if row[6] else {}
            }

            return document

    except Exception as e:
        logger.error(f"Error getting document {document_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting document: {str(e)}")

@app.delete("/documents/{document_id}")
async def delete_document(document_id: int):
    """Delete a document from SQLite and RAG Premium"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT collection_name, filename FROM documents WHERE id = ?", (document_id,))
            document = cursor.fetchone()

            if not document:
                raise HTTPException(status_code=404, detail=f"Document with ID {document_id} not found")

            collection_name = document['collection_name']
            filename = document['filename']

            # 🚀 SUPPRIMER DU RAG PREMIUM
            if RAG_UNIFIED_AVAILABLE:
                try:
                    rag_service = await get_rag_premium_service()
                    await rag_service.delete_document(filename)
                    logger.info(f"🗑️ Document {filename} supprimé du RAG Premium")
                except Exception as e:
                    logger.warning(f"Échec suppression RAG Premium: {e}")

            # Delete from SQLite
            cursor.execute("DELETE FROM documents WHERE id = ?", (document_id,))
            cursor.execute(
                "UPDATE collections SET document_count = document_count - 1 WHERE name = ? AND document_count > 0",
                (collection_name,)
            )
            conn.commit()

        return {
            "status": "success",
            "message": f"Document {document_id} ({filename}) deleted successfully"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting document: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error deleting document: {str(e)}")

# ============================================================================
# RAG PREMIUM ENDPOINTS
# ============================================================================

@app.post("/rag/search")
async def premium_rag_search(request: dict):
    """Recherche RAG Premium avec LightRAG + Qdrant + BGE-M3"""
    if not RAG_UNIFIED_AVAILABLE:
        raise HTTPException(status_code=503, detail="RAG Premium Service not available")

    try:
        rag_service = await get_rag_premium_service()

        # Extraire les paramètres de la requête
        query = request.get("query", "")
        selected_documents = request.get("selectedDocuments", [])
        use_cache = request.get("use_cache", True)
        use_lightrag = request.get("use_lightrag", True)
        use_vector_store = request.get("use_vector_store", True)
        limit = request.get("limit", 5)
        fast_mode = request.get("fast_mode", False)  # 🚀 Mode rapide

        # 🚀 OPTIMISATION: Log pour debugging performance
        start_time = time.time()
        logger.info(f"🔍 RAG Search: query='{query[:50]}...', fast_mode={fast_mode}, limit={limit}")

        # Effectuer la recherche premium optimisée
        result = await rag_service.search(
            query=query,
            use_cache=use_cache,
            use_lightrag=use_lightrag and not fast_mode,  # 🚀 Désactiver LightRAG en mode rapide
            use_vector_store=use_vector_store,
            limit=limit,
            fast_mode=fast_mode
        )

        # 🚀 Log performance
        processing_time = time.time() - start_time
        result["processing_time"] = round(processing_time, 3)
        logger.info(f"🚀 RAG Search completed in {processing_time:.3f}s")

        return result

    except Exception as e:
        logger.error(f"Premium RAG search failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/rag/metrics")
async def rag_metrics():
    """Obtenir les métriques de performance du RAG Premium"""
    if not RAG_UNIFIED_AVAILABLE:
        raise HTTPException(status_code=503, detail="RAG Premium Service not available")

    try:
        rag_service = await get_rag_premium_service()
        metrics = await rag_service.get_metrics()
        return metrics

    except Exception as e:
        logger.error(f"Failed to get RAG metrics: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/rag/delete_document")
async def rag_delete_document(request: dict):
    """Supprimer un document du RAG Premium (appelé lors suppression conversation)"""
    if not RAG_UNIFIED_AVAILABLE:
        raise HTTPException(status_code=503, detail="RAG Premium Service not available")

    try:
        filename = request.get("filename")
        session_id = request.get("session_id", "unknown")

        if not filename:
            raise HTTPException(status_code=400, detail="Filename is required")

        rag_service = await get_rag_premium_service()
        success = await rag_service.delete_document(filename)

        if success:
            logger.info(f"🗑️ Document {filename} supprimé du RAG Premium (session: {session_id})")
            return {
                "status": "success",
                "message": f"Document {filename} removed from RAG Premium",
                "session_id": session_id
            }
        else:
            logger.warning(f"⚠️ Échec suppression RAG Premium: {filename}")
            return {
                "status": "partial_success",
                "message": f"Document {filename} may not have been in RAG Premium",
                "session_id": session_id
            }

    except Exception as e:
        logger.error(f"Error deleting document from RAG: {e}")
        raise HTTPException(status_code=500, detail=f"Error deleting document from RAG: {str(e)}")

@app.post("/rag/clear_all")
async def rag_clear_all(request: dict):
    """Supprimer TOUS les documents du RAG Premium (appelé lors clear all conversations)"""
    if not RAG_UNIFIED_AVAILABLE:
        raise HTTPException(status_code=503, detail="RAG Premium Service not available")

    try:
        confirm = request.get("confirm", False)
        clear_all_documents = request.get("clear_all_documents", False)

        if not confirm or not clear_all_documents:
            raise HTTPException(status_code=400, detail="Confirmation required for clearing all RAG data")

        # 🗑️ SUPPRESSION COMPLÈTE RAG PREMIUM
        try:
            # Supprimer toute la collection Qdrant
            rag_service = await get_rag_premium_service()
            if rag_service.qdrant_client:
                collection_name = rag_service.config["qdrant"]["collection_name"]
                rag_service.qdrant_client.delete_collection(collection_name)
                logger.info(f"🗑️ Collection Qdrant {collection_name} supprimée complètement")

                # Recréer la collection vide
                await rag_service._create_collection()
                logger.info(f"✅ Collection Qdrant {collection_name} recréée vide")

            # TODO: Supprimer LightRAG storage si nécessaire
            # shutil.rmtree("./lightrag_storage", ignore_errors=True)

            logger.info("🗑️ RAG Premium complètement nettoyé")
            return {
                "status": "success",
                "message": "All RAG Premium data cleared successfully",
                "cleared_collections": [collection_name] if 'collection_name' in locals() else []
            }

        except Exception as e:
            logger.error(f"Error clearing RAG Premium: {e}")
            return {
                "status": "partial_success",
                "message": f"RAG clearing encountered errors: {str(e)}"
            }

    except Exception as e:
        logger.error(f"Error clearing all RAG data: {e}")
        raise HTTPException(status_code=500, detail=f"Error clearing all RAG data: {str(e)}")

@app.get("/rag/health")
async def rag_health():
    """Vérification de santé du système RAG Premium"""
    if not RAG_UNIFIED_AVAILABLE:
        return {"status": "unavailable", "message": "RAG Premium Service not available"}

    try:
        rag_service = await get_rag_premium_service()
        health = await rag_service.health_check()
        return health

    except Exception as e:
        logger.error(f"RAG health check failed: {e}")
        return {"status": "unhealthy", "error": str(e)}

# ============================================================================
# SHUTDOWN
# ============================================================================

def handle_exit(signum, frame):
    logger.info(f"Received signal {signum}, shutting down gracefully")
    sys.exit(0)

signal.signal(signal.SIGINT, handle_exit)
signal.signal(signal.SIGTERM, handle_exit)

if __name__ == "__main__":
    import uvicorn
    logger.info(f"Starting Clara Backend Clean on {HOST}:{PORT}")

    uvicorn.run(
        "main:app",
        host=HOST,
        port=PORT,
        log_level="info",
        reload=False
    )
