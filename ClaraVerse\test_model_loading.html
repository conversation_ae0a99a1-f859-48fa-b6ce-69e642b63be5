<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Model Loading</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        button { padding: 8px 16px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>🔍 Test Model Loading - ClaraVerse</h1>
    
    <div class="test-section info">
        <h3>📋 Tests de diagnostic</h3>
        <button onclick="testLMStudioConnection()">Test LM Studio Connection</button>
        <button onclick="testOllamaConnection()">Test Ollama Connection</button>
        <button onclick="testProviderLoading()">Test Provider Loading</button>
        <button onclick="testModelLoading()">Test Model Loading</button>
        <button onclick="runAllTests()">🚀 Run All Tests</button>
    </div>

    <div id="results"></div>

    <script>
        const results = document.getElementById('results');

        function addResult(title, content, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-section ${type}`;
            div.innerHTML = `<h3>${title}</h3><pre>${content}</pre>`;
            results.appendChild(div);
        }

        async function testLMStudioConnection() {
            try {
                addResult('🔄 Testing LM Studio Connection...', 'Connecting to http://localhost:1234/v1/models');
                
                const response = await fetch('http://localhost:1234/v1/models');
                const data = await response.json();
                
                addResult('✅ LM Studio Connection Success', JSON.stringify(data, null, 2), 'success');
            } catch (error) {
                addResult('❌ LM Studio Connection Failed', error.message, 'error');
            }
        }

        async function testOllamaConnection() {
            try {
                addResult('🔄 Testing Ollama Connection...', 'Connecting to http://localhost:11434/api/tags');
                
                const response = await fetch('http://localhost:11434/api/tags');
                const data = await response.json();
                
                addResult('✅ Ollama Connection Success', JSON.stringify(data, null, 2), 'success');
            } catch (error) {
                addResult('❌ Ollama Connection Failed', error.message, 'error');
            }
        }

        async function testProviderLoading() {
            try {
                addResult('🔄 Testing Provider Loading...', 'Loading providers from localStorage and defaults');
                
                // Test localStorage providers
                const storedProviders = localStorage.getItem('clara-providers');
                addResult('📦 Stored Providers', storedProviders || 'None found');
                
                // Test if claraApiService is available
                if (window.claraApiService) {
                    const providers = await window.claraApiService.getProviders();
                    addResult('✅ Provider Loading Success', JSON.stringify(providers, null, 2), 'success');
                } else {
                    addResult('⚠️ Clara API Service Not Available', 'claraApiService not found on window object', 'error');
                }
            } catch (error) {
                addResult('❌ Provider Loading Failed', error.message, 'error');
            }
        }

        async function testModelLoading() {
            try {
                addResult('🔄 Testing Model Loading...', 'Loading models from all providers');

                // Test direct API calls to providers
                const lmStudioModels = await testDirectLMStudioModels();
                const ollamaModels = await testDirectOllamaModels();

                addResult('📊 Direct API Results',
                    `LM Studio Models: ${lmStudioModels.length}\nOllama Models: ${ollamaModels.length}`, 'info');

                if (window.claraApiService) {
                    const models = await window.claraApiService.getModels();
                    addResult('✅ Model Loading Success', `Found ${models.length} models:\n${JSON.stringify(models, null, 2)}`, 'success');
                } else {
                    addResult('⚠️ Clara API Service Not Available', 'claraApiService not found on window object', 'error');
                }
            } catch (error) {
                addResult('❌ Model Loading Failed', error.message, 'error');
            }
        }

        async function testDirectLMStudioModels() {
            try {
                const response = await fetch('http://localhost:1234/v1/models');
                const data = await response.json();
                return data.data || [];
            } catch (error) {
                return [];
            }
        }

        async function testDirectOllamaModels() {
            try {
                const response = await fetch('http://localhost:11434/api/tags');
                const data = await response.json();
                return data.models || [];
            } catch (error) {
                return [];
            }
        }

        async function runAllTests() {
            results.innerHTML = '';
            addResult('🚀 Starting All Tests...', 'Running comprehensive diagnostics');
            
            await testLMStudioConnection();
            await testOllamaConnection();
            await testProviderLoading();
            await testModelLoading();
            
            addResult('✅ All Tests Complete', 'Check results above for any issues');
        }

        // Auto-run tests on page load
        window.addEventListener('load', () => {
            setTimeout(runAllTests, 1000);
        });
    </script>
</body>
</html>
