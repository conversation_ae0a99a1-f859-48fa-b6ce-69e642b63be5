# Configuration délégation d'inférence WeMa IA
# Copiez ce fichier vers .env pour activer la délégation

# 🧠 SERVEUR D'INFÉRENCE CENTRAL
INFERENCE_SERVER_IP=*************
INFERENCE_SERVER_PORT=1235

# 🏠 FALLBACK LOCAL DÉSACTIVÉ (pas de LM Studio sur PC utilisateur)
INFERENCE_FALLBACK_LOCAL=false

# 📊 CONFIGURATION RAG LOCAL (reste sur le PC)
RAG_ENABLED=true
RAG_FAST_MODE=false

# 🔍 CONFIGURATION OCR LOCAL (reste sur le PC)
OCR_ENABLED=true
OCR_LANGUAGE=fra
OCR_PRESERVE_LAYOUT=true

# 💾 BASE DE DONNÉES LOCALE (reste sur le PC)
DATABASE_PATH=./data/clara.db

# 🔧 CONFIGURATION BACKEND LOCAL
CLARA_HOST=127.0.0.1
CLARA_PORT=8000

# 📝 LOGS
LOG_LEVEL=INFO
DEBUG_INFERENCE=false
