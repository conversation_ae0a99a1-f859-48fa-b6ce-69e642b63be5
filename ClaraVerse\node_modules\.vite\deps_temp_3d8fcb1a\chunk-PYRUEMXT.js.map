{"version": 3, "sources": ["../../refractor/lang/c.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = c\nc.displayName = 'c'\nc.aliases = []\nfunction c(Prism) {\n  Prism.languages.c = Prism.languages.extend('clike', {\n    comment: {\n      pattern:\n        /\\/\\/(?:[^\\r\\n\\\\]|\\\\(?:\\r\\n?|\\n|(?![\\r\\n])))*|\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n      greedy: true\n    },\n    string: {\n      // https://en.cppreference.com/w/c/language/string_literal\n      pattern: /\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\"\\\\\\r\\n])*\"/,\n      greedy: true\n    },\n    'class-name': {\n      pattern:\n        /(\\b(?:enum|struct)\\s+(?:__attribute__\\s*\\(\\([\\s\\S]*?\\)\\)\\s*)?)\\w+|\\b[a-z]\\w*_t\\b/,\n      lookbehind: true\n    },\n    keyword:\n      /\\b(?:_Alignas|_Alignof|_Atomic|_Bool|_Complex|_Generic|_Imaginary|_Noreturn|_Static_assert|_Thread_local|__attribute__|asm|auto|break|case|char|const|continue|default|do|double|else|enum|extern|float|for|goto|if|inline|int|long|register|return|short|signed|sizeof|static|struct|switch|typedef|typeof|union|unsigned|void|volatile|while)\\b/,\n    function: /\\b[a-z_]\\w*(?=\\s*\\()/i,\n    number:\n      /(?:\\b0x(?:[\\da-f]+(?:\\.[\\da-f]*)?|\\.[\\da-f]+)(?:p[+-]?\\d+)?|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?)[ful]{0,4}/i,\n    operator: />>=?|<<=?|->|([-+&|:])\\1|[?:~]|[-+*/%&|^!=<>]=?/\n  })\n  Prism.languages.insertBefore('c', 'string', {\n    char: {\n      // https://en.cppreference.com/w/c/language/character_constant\n      pattern: /'(?:\\\\(?:\\r\\n|[\\s\\S])|[^'\\\\\\r\\n]){0,32}'/,\n      greedy: true\n    }\n  })\n  Prism.languages.insertBefore('c', 'string', {\n    macro: {\n      // allow for multiline macro definitions\n      // spaces after the # character compile fine with gcc\n      pattern:\n        /(^[\\t ]*)#\\s*[a-z](?:[^\\r\\n\\\\/]|\\/(?!\\*)|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/|\\\\(?:\\r\\n|[\\s\\S]))*/im,\n      lookbehind: true,\n      greedy: true,\n      alias: 'property',\n      inside: {\n        string: [\n          {\n            // highlight the path of the include statement as a string\n            pattern: /^(#\\s*include\\s*)<[^>]+>/,\n            lookbehind: true\n          },\n          Prism.languages.c['string']\n        ],\n        char: Prism.languages.c['char'],\n        comment: Prism.languages.c['comment'],\n        'macro-name': [\n          {\n            pattern: /(^#\\s*define\\s+)\\w+\\b(?!\\()/i,\n            lookbehind: true\n          },\n          {\n            pattern: /(^#\\s*define\\s+)\\w+\\b(?=\\()/i,\n            lookbehind: true,\n            alias: 'function'\n          }\n        ],\n        // highlight macro directives as keywords\n        directive: {\n          pattern: /^(#\\s*)[a-z]+/,\n          lookbehind: true,\n          alias: 'keyword'\n        },\n        'directive-hash': /^#/,\n        punctuation: /##|\\\\(?=[\\r\\n])/,\n        expression: {\n          pattern: /\\S[\\s\\S]*/,\n          inside: Prism.languages.c\n        }\n      }\n    }\n  })\n  Prism.languages.insertBefore('c', 'function', {\n    // highlight predefined macros as constants\n    constant:\n      /\\b(?:EOF|NULL|SEEK_CUR|SEEK_END|SEEK_SET|__DATE__|__FILE__|__LINE__|__TIMESTAMP__|__TIME__|__func__|stderr|stdin|stdout)\\b/\n  })\n  delete Prism.languages.c['boolean']\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,MAAE,cAAc;AAChB,MAAE,UAAU,CAAC;AACb,aAAS,EAAE,OAAO;AAChB,YAAM,UAAU,IAAI,MAAM,UAAU,OAAO,SAAS;AAAA,QAClD,SAAS;AAAA,UACP,SACE;AAAA,UACF,QAAQ;AAAA,QACV;AAAA,QACA,QAAQ;AAAA;AAAA,UAEN,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,cAAc;AAAA,UACZ,SACE;AAAA,UACF,YAAY;AAAA,QACd;AAAA,QACA,SACE;AAAA,QACF,UAAU;AAAA,QACV,QACE;AAAA,QACF,UAAU;AAAA,MACZ,CAAC;AACD,YAAM,UAAU,aAAa,KAAK,UAAU;AAAA,QAC1C,MAAM;AAAA;AAAA,UAEJ,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,MACF,CAAC;AACD,YAAM,UAAU,aAAa,KAAK,UAAU;AAAA,QAC1C,OAAO;AAAA;AAAA;AAAA,UAGL,SACE;AAAA,UACF,YAAY;AAAA,UACZ,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,QAAQ;AAAA,YACN,QAAQ;AAAA,cACN;AAAA;AAAA,gBAEE,SAAS;AAAA,gBACT,YAAY;AAAA,cACd;AAAA,cACA,MAAM,UAAU,EAAE,QAAQ;AAAA,YAC5B;AAAA,YACA,MAAM,MAAM,UAAU,EAAE,MAAM;AAAA,YAC9B,SAAS,MAAM,UAAU,EAAE,SAAS;AAAA,YACpC,cAAc;AAAA,cACZ;AAAA,gBACE,SAAS;AAAA,gBACT,YAAY;AAAA,cACd;AAAA,cACA;AAAA,gBACE,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,OAAO;AAAA,cACT;AAAA,YACF;AAAA;AAAA,YAEA,WAAW;AAAA,cACT,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,OAAO;AAAA,YACT;AAAA,YACA,kBAAkB;AAAA,YAClB,aAAa;AAAA,YACb,YAAY;AAAA,cACV,SAAS;AAAA,cACT,QAAQ,MAAM,UAAU;AAAA,YAC1B;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AACD,YAAM,UAAU,aAAa,KAAK,YAAY;AAAA;AAAA,QAE5C,UACE;AAAA,MACJ,CAAC;AACD,aAAO,MAAM,UAAU,EAAE,SAAS;AAAA,IACpC;AAAA;AAAA;", "names": []}