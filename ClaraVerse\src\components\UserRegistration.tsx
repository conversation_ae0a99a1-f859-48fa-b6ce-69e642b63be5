/**
 * 🔐 Enregistrement utilisateur WeMa IA
 * Premier démarrage : saisie nom/prénom/pôle
 */

import React, { useState, useEffect } from 'react';
import { User, Building, Save, Loader } from 'lucide-react';

interface UserInfo {
  firstName: string;
  lastName: string;
  pole: string;
  pcName: string;
  ipAddress: string;
  registeredAt: string;
}

interface UserRegistrationProps {
  onRegistrationComplete: (userInfo: UserInfo) => void;
}

const UserRegistration: React.FC<UserRegistrationProps> = ({ onRegistrationComplete }) => {
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [pole, setPole] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const poles = [
    { id: 'patrimoine', name: 'Gestion Patrimoine' },
    { id: 'mandats', name: '<PERSON>da<PERSON> A' },
    { id: 'rh', name: '<PERSON><PERSON><PERSON>ces Humaines' },
    { id: 'direction', name: 'Direction' },
    { id: 'comptabilite', name: 'Comptabilité' },
    { id: 'autre', name: 'Autre' }
  ];

  // Obtenir les informations système
  const getSystemInfo = async (): Promise<{ pcName: string; ipAddress: string }> => {
    try {
      // Nom du PC (approximatif via navigateur)
      const pcName = navigator.userAgent.includes('Windows') 
        ? `PC-${Math.random().toString(36).substr(2, 6).toUpperCase()}`
        : 'Unknown-PC';

      // IP locale (approximative)
      let ipAddress = 'Unknown';
      try {
        const response = await fetch('/api/system/ip');
        if (response.ok) {
          const data = await response.json();
          ipAddress = data.ip || 'Unknown';
        }
      } catch {
        // Fallback si l'API n'est pas disponible
        ipAddress = 'Local-Network';
      }

      return { pcName, ipAddress };
    } catch (error) {
      console.error('Erreur récupération infos système:', error);
      return { pcName: 'Unknown-PC', ipAddress: 'Unknown' };
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!firstName.trim() || !lastName.trim() || !pole) {
      setError('Tous les champs sont requis');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // Obtenir les infos système
      const systemInfo = await getSystemInfo();
      
      const userInfo: UserInfo = {
        firstName: firstName.trim(),
        lastName: lastName.trim(),
        pole,
        pcName: systemInfo.pcName,
        ipAddress: systemInfo.ipAddress,
        registeredAt: new Date().toISOString()
      };

      // Sauvegarder localement
      localStorage.setItem('wema_user_info', JSON.stringify(userInfo));
      
      // Enregistrer sur le serveur admin
      try {
        await fetch('/api/admin/register-user', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(userInfo)
        });
      } catch (serverError) {
        console.warn('Impossible d\'enregistrer sur le serveur admin:', serverError);
        // Continue quand même, l'enregistrement local suffit
      }

      // Notifier le parent
      onRegistrationComplete(userInfo);
      
    } catch (error) {
      console.error('Erreur enregistrement:', error);
      setError('Erreur lors de l\'enregistrement. Veuillez réessayer.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl shadow-xl p-8 w-full max-w-md">
        {/* Logo et titre */}
        <div className="text-center mb-8">
          <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
            <User className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-2xl font-bold text-gray-900">Bienvenue dans WeMa IA</h1>
          <p className="text-gray-600 mt-2">Veuillez vous identifier pour commencer</p>
        </div>

        {/* Formulaire */}
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Prénom */}
          <div>
            <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-2">
              Prénom *
            </label>
            <input
              type="text"
              id="firstName"
              value={firstName}
              onChange={(e) => setFirstName(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Votre prénom"
              required
            />
          </div>

          {/* Nom */}
          <div>
            <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-2">
              Nom *
            </label>
            <input
              type="text"
              id="lastName"
              value={lastName}
              onChange={(e) => setLastName(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Votre nom"
              required
            />
          </div>

          {/* Pôle */}
          <div>
            <label htmlFor="pole" className="block text-sm font-medium text-gray-700 mb-2">
              Pôle / Service *
            </label>
            <select
              id="pole"
              value={pole}
              onChange={(e) => setPole(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            >
              <option value="">Sélectionnez votre pôle</option>
              {poles.map(p => (
                <option key={p.id} value={p.id}>{p.name}</option>
              ))}
            </select>
          </div>

          {/* Message d'erreur */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
              <p className="text-red-600 text-sm">{error}</p>
            </div>
          )}

          {/* Bouton de validation */}
          <button
            type="submit"
            disabled={loading}
            className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
          >
            {loading ? (
              <>
                <Loader className="w-5 h-5 mr-2 animate-spin" />
                Enregistrement...
              </>
            ) : (
              <>
                <Save className="w-5 h-5 mr-2" />
                Commencer
              </>
            )}
          </button>
        </form>

        {/* Note */}
        <div className="mt-6 text-center">
          <p className="text-xs text-gray-500">
            Ces informations sont utilisées pour l'administration et le support technique
          </p>
        </div>
      </div>
    </div>
  );
};

export default UserRegistration;
