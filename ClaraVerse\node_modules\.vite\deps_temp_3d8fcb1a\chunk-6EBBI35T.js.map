{"version": 3, "sources": ["../../refractor/lang/qml.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = qml\nqml.displayName = 'qml'\nqml.aliases = []\nfunction qml(Prism) {\n  ;(function (Prism) {\n    var jsString = /\"(?:\\\\.|[^\\\\\"\\r\\n])*\"|'(?:\\\\.|[^\\\\'\\r\\n])*'/.source\n    var jsComment = /\\/\\/.*(?!.)|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\//.source\n    var jsExpr =\n      /(?:[^\\\\()[\\]{}\"'/]|<string>|\\/(?![*/])|<comment>|\\(<expr>*\\)|\\[<expr>*\\]|\\{<expr>*\\}|\\\\[\\s\\S])/.source\n        .replace(/<string>/g, function () {\n          return jsString\n        })\n        .replace(/<comment>/g, function () {\n          return jsComment\n        }) // the pattern will blow up, so only a few iterations\n    for (var i = 0; i < 2; i++) {\n      jsExpr = jsExpr.replace(/<expr>/g, function () {\n        return jsExpr\n      })\n    }\n    jsExpr = jsExpr.replace(/<expr>/g, '[^\\\\s\\\\S]')\n    Prism.languages.qml = {\n      comment: {\n        pattern: /\\/\\/.*|\\/\\*[\\s\\S]*?\\*\\//,\n        greedy: true\n      },\n      'javascript-function': {\n        pattern: RegExp(\n          /((?:^|;)[ \\t]*)function\\s+(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*\\s*\\(<js>*\\)\\s*\\{<js>*\\}/.source.replace(\n            /<js>/g,\n            function () {\n              return jsExpr\n            }\n          ),\n          'm'\n        ),\n        lookbehind: true,\n        greedy: true,\n        alias: 'language-javascript',\n        inside: Prism.languages.javascript\n      },\n      'class-name': {\n        pattern: /((?:^|[:;])[ \\t]*)(?!\\d)\\w+(?=[ \\t]*\\{|[ \\t]+on\\b)/m,\n        lookbehind: true\n      },\n      property: [\n        {\n          pattern: /((?:^|[;{])[ \\t]*)(?!\\d)\\w+(?:\\.\\w+)*(?=[ \\t]*:)/m,\n          lookbehind: true\n        },\n        {\n          pattern:\n            /((?:^|[;{])[ \\t]*)property[ \\t]+(?!\\d)\\w+(?:\\.\\w+)*[ \\t]+(?!\\d)\\w+(?:\\.\\w+)*(?=[ \\t]*:)/m,\n          lookbehind: true,\n          inside: {\n            keyword: /^property/,\n            property: /\\w+(?:\\.\\w+)*/\n          }\n        }\n      ],\n      'javascript-expression': {\n        pattern: RegExp(\n          /(:[ \\t]*)(?![\\s;}[])(?:(?!$|[;}])<js>)+/.source.replace(\n            /<js>/g,\n            function () {\n              return jsExpr\n            }\n          ),\n          'm'\n        ),\n        lookbehind: true,\n        greedy: true,\n        alias: 'language-javascript',\n        inside: Prism.languages.javascript\n      },\n      string: {\n        pattern: /\"(?:\\\\.|[^\\\\\"\\r\\n])*\"/,\n        greedy: true\n      },\n      keyword: /\\b(?:as|import|on)\\b/,\n      punctuation: /[{}[\\]:;,]/\n    }\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,QAAI,cAAc;AAClB,QAAI,UAAU,CAAC;AACf,aAAS,IAAI,OAAO;AAClB;AAAC,OAAC,SAAUA,QAAO;AACjB,YAAI,WAAW,8CAA8C;AAC7D,YAAI,YAAY,yCAAyC;AACzD,YAAI,SACF,iGAAiG,OAC9F,QAAQ,aAAa,WAAY;AAChC,iBAAO;AAAA,QACT,CAAC,EACA,QAAQ,cAAc,WAAY;AACjC,iBAAO;AAAA,QACT,CAAC;AACL,iBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,mBAAS,OAAO,QAAQ,WAAW,WAAY;AAC7C,mBAAO;AAAA,UACT,CAAC;AAAA,QACH;AACA,iBAAS,OAAO,QAAQ,WAAW,WAAW;AAC9C,QAAAA,OAAM,UAAU,MAAM;AAAA,UACpB,SAAS;AAAA,YACP,SAAS;AAAA,YACT,QAAQ;AAAA,UACV;AAAA,UACA,uBAAuB;AAAA,YACrB,SAAS;AAAA,cACP,2GAA2G,OAAO;AAAA,gBAChH;AAAA,gBACA,WAAY;AACV,yBAAO;AAAA,gBACT;AAAA,cACF;AAAA,cACA;AAAA,YACF;AAAA,YACA,YAAY;AAAA,YACZ,QAAQ;AAAA,YACR,OAAO;AAAA,YACP,QAAQA,OAAM,UAAU;AAAA,UAC1B;AAAA,UACA,cAAc;AAAA,YACZ,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,UACA,UAAU;AAAA,YACR;AAAA,cACE,SAAS;AAAA,cACT,YAAY;AAAA,YACd;AAAA,YACA;AAAA,cACE,SACE;AAAA,cACF,YAAY;AAAA,cACZ,QAAQ;AAAA,gBACN,SAAS;AAAA,gBACT,UAAU;AAAA,cACZ;AAAA,YACF;AAAA,UACF;AAAA,UACA,yBAAyB;AAAA,YACvB,SAAS;AAAA,cACP,0CAA0C,OAAO;AAAA,gBAC/C;AAAA,gBACA,WAAY;AACV,yBAAO;AAAA,gBACT;AAAA,cACF;AAAA,cACA;AAAA,YACF;AAAA,YACA,YAAY;AAAA,YACZ,QAAQ;AAAA,YACR,OAAO;AAAA,YACP,QAAQA,OAAM,UAAU;AAAA,UAC1B;AAAA,UACA,QAAQ;AAAA,YACN,SAAS;AAAA,YACT,QAAQ;AAAA,UACV;AAAA,UACA,SAAS;AAAA,UACT,aAAa;AAAA,QACf;AAAA,MACF,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}