{"version": 3, "sources": ["../../refractor/lang/wasm.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = wasm\nwasm.displayName = 'wasm'\nwasm.aliases = []\nfunction wasm(Prism) {\n  Prism.languages.wasm = {\n    comment: [\n      /\\(;[\\s\\S]*?;\\)/,\n      {\n        pattern: /;;.*/,\n        greedy: true\n      }\n    ],\n    string: {\n      pattern: /\"(?:\\\\[\\s\\S]|[^\"\\\\])*\"/,\n      greedy: true\n    },\n    keyword: [\n      {\n        pattern: /\\b(?:align|offset)=/,\n        inside: {\n          operator: /=/\n        }\n      },\n      {\n        pattern:\n          /\\b(?:(?:f32|f64|i32|i64)(?:\\.(?:abs|add|and|ceil|clz|const|convert_[su]\\/i(?:32|64)|copysign|ctz|demote\\/f64|div(?:_[su])?|eqz?|extend_[su]\\/i32|floor|ge(?:_[su])?|gt(?:_[su])?|le(?:_[su])?|load(?:(?:8|16|32)_[su])?|lt(?:_[su])?|max|min|mul|neg?|nearest|or|popcnt|promote\\/f32|reinterpret\\/[fi](?:32|64)|rem_[su]|rot[lr]|shl|shr_[su]|sqrt|store(?:8|16|32)?|sub|trunc(?:_[su]\\/f(?:32|64))?|wrap\\/i64|xor))?|memory\\.(?:grow|size))\\b/,\n        inside: {\n          punctuation: /\\./\n        }\n      },\n      /\\b(?:anyfunc|block|br(?:_if|_table)?|call(?:_indirect)?|data|drop|elem|else|end|export|func|get_(?:global|local)|global|if|import|local|loop|memory|module|mut|nop|offset|param|result|return|select|set_(?:global|local)|start|table|tee_local|then|type|unreachable)\\b/\n    ],\n    variable: /\\$[\\w!#$%&'*+\\-./:<=>?@\\\\^`|~]+/,\n    number:\n      /[+-]?\\b(?:\\d(?:_?\\d)*(?:\\.\\d(?:_?\\d)*)?(?:[eE][+-]?\\d(?:_?\\d)*)?|0x[\\da-fA-F](?:_?[\\da-fA-F])*(?:\\.[\\da-fA-F](?:_?[\\da-fA-D])*)?(?:[pP][+-]?\\d(?:_?\\d)*)?)\\b|\\binf\\b|\\bnan(?::0x[\\da-fA-F](?:_?[\\da-fA-D])*)?\\b/,\n    punctuation: /[()]/\n  }\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,SAAK,cAAc;AACnB,SAAK,UAAU,CAAC;AAChB,aAAS,KAAK,OAAO;AACnB,YAAM,UAAU,OAAO;AAAA,QACrB,SAAS;AAAA,UACP;AAAA,UACA;AAAA,YACE,SAAS;AAAA,YACT,QAAQ;AAAA,UACV;AAAA,QACF;AAAA,QACA,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,SAAS;AAAA,UACP;AAAA,YACE,SAAS;AAAA,YACT,QAAQ;AAAA,cACN,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,UACA;AAAA,YACE,SACE;AAAA,YACF,QAAQ;AAAA,cACN,aAAa;AAAA,YACf;AAAA,UACF;AAAA,UACA;AAAA,QACF;AAAA,QACA,UAAU;AAAA,QACV,QACE;AAAA,QACF,aAAa;AAAA,MACf;AAAA,IACF;AAAA;AAAA;", "names": []}