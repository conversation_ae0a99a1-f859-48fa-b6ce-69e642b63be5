{"version": 3, "sources": ["../../refractor/lang/systemd.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = systemd\nsystemd.displayName = 'systemd'\nsystemd.aliases = []\nfunction systemd(Prism) {\n  // https://www.freedesktop.org/software/systemd/man/systemd.syntax.html\n  ;(function (Prism) {\n    var comment = {\n      pattern: /^[;#].*/m,\n      greedy: true\n    }\n    var quotesSource = /\"(?:[^\\r\\n\"\\\\]|\\\\(?:[^\\r]|\\r\\n?))*\"(?!\\S)/.source\n    Prism.languages.systemd = {\n      comment: comment,\n      section: {\n        pattern: /^\\[[^\\n\\r\\[\\]]*\\](?=[ \\t]*$)/m,\n        greedy: true,\n        inside: {\n          punctuation: /^\\[|\\]$/,\n          'section-name': {\n            pattern: /[\\s\\S]+/,\n            alias: 'selector'\n          }\n        }\n      },\n      key: {\n        pattern: /^[^\\s=]+(?=[ \\t]*=)/m,\n        greedy: true,\n        alias: 'attr-name'\n      },\n      value: {\n        // This pattern is quite complex because of two properties:\n        //  1) Quotes (strings) must be preceded by a space. Since we can't use lookbehinds, we have to \"resolve\"\n        //     the lookbehind. You will see this in the main loop where spaces are handled separately.\n        //  2) Line continuations.\n        //     After line continuations, empty lines and comments are ignored so we have to consume them.\n        pattern: RegExp(\n          /(=[ \\t]*(?!\\s))/.source + // the value either starts with quotes or not\n            '(?:' +\n            quotesSource +\n            '|(?=[^\"\\r\\n]))' + // main loop\n            '(?:' +\n            (/[^\\s\\\\]/.source + // handle spaces separately because of quotes\n              '|' +\n              '[ \\t]+(?:(?![ \\t\"])|' +\n              quotesSource +\n              ')' + // line continuation\n              '|' +\n              /\\\\[\\r\\n]+(?:[#;].*[\\r\\n]+)*(?![#;])/.source) +\n            ')*'\n        ),\n        lookbehind: true,\n        greedy: true,\n        alias: 'attr-value',\n        inside: {\n          comment: comment,\n          quoted: {\n            pattern: RegExp(/(^|\\s)/.source + quotesSource),\n            lookbehind: true,\n            greedy: true\n          },\n          punctuation: /\\\\$/m,\n          boolean: {\n            pattern: /^(?:false|no|off|on|true|yes)$/,\n            greedy: true\n          }\n        }\n      },\n      punctuation: /=/\n    }\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,YAAQ,cAAc;AACtB,YAAQ,UAAU,CAAC;AACnB,aAAS,QAAQ,OAAO;AAEtB;AAAC,OAAC,SAAUA,QAAO;AACjB,YAAI,UAAU;AAAA,UACZ,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AACA,YAAI,eAAe,4CAA4C;AAC/D,QAAAA,OAAM,UAAU,UAAU;AAAA,UACxB;AAAA,UACA,SAAS;AAAA,YACP,SAAS;AAAA,YACT,QAAQ;AAAA,YACR,QAAQ;AAAA,cACN,aAAa;AAAA,cACb,gBAAgB;AAAA,gBACd,SAAS;AAAA,gBACT,OAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AAAA,UACA,KAAK;AAAA,YACH,SAAS;AAAA,YACT,QAAQ;AAAA,YACR,OAAO;AAAA,UACT;AAAA,UACA,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAML,SAAS;AAAA,cACP,kBAAkB;AAAA,cAChB,QACA,eACA,uBAEC,UAAU;AAAA,cACT,wBAEA,eACA,OAEA,sCAAsC,UACxC;AAAA,YACJ;AAAA,YACA,YAAY;AAAA,YACZ,QAAQ;AAAA,YACR,OAAO;AAAA,YACP,QAAQ;AAAA,cACN;AAAA,cACA,QAAQ;AAAA,gBACN,SAAS,OAAO,SAAS,SAAS,YAAY;AAAA,gBAC9C,YAAY;AAAA,gBACZ,QAAQ;AAAA,cACV;AAAA,cACA,aAAa;AAAA,cACb,SAAS;AAAA,gBACP,SAAS;AAAA,gBACT,QAAQ;AAAA,cACV;AAAA,YACF;AAAA,UACF;AAAA,UACA,aAAa;AAAA,QACf;AAAA,MACF,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}