/**
 * 💬 MESSAGE HANDLER
 * Gère l'envoi et la réception des messages de manière isolée
 */

import { useState, useCallback } from 'react';
import { claraApiService } from '../../../services/claraApiService';
import { claraDB } from '../../../db/claraDatabase';
import { createOptimizedContentHandler } from '../../../utils/streamOptimizer';
import { claraTTSService } from '../../../services/claraTTSService';
import { addCompletionNotification, addErrorNotification } from '../../../services/notificationService';
import { useDocumentStore } from '../../../stores/documentStore';
import { ragSearch } from '../../assistantLibrary/ragSearch';
import type { 
  ClaraMessage, 
  ClaraFileAttachment, 
  ClaraSessionConfig,
  ClaraChatSession 
} from '../../../types/clara_assistant_types';

export interface MessageHandlerState {
  isLoading: boolean;
  latestAIResponse: string;
  autoTTSTrigger: { text: string; timestamp: number } | null;
}

export interface MessageHandlerActions {
  sendMessage: (
    content: string, 
    attachments?: ClaraFileAttachment[],
    session?: ClaraChatSession,
    config?: ClaraSessionConfig
  ) => Promise<ClaraMessage>;
  stopGeneration: () => void;
  copyMessage: (content: string) => Promise<void>;
  retryMessage: (messageId: string) => void;
  editMessage: (messageId: string, newContent: string) => void;
}

// Générer un ID unique
const generateId = () => `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

export const useMessageHandler = (
  onMessageAdded: (message: ClaraMessage) => void,
  onMessageUpdated: (messageId: string, updates: Partial<ClaraMessage>) => void
): MessageHandlerState & MessageHandlerActions => {
  
  const [isLoading, setIsLoading] = useState(false);
  const [latestAIResponse, setLatestAIResponse] = useState<string>('');
  const [autoTTSTrigger, setAutoTTSTrigger] = useState<{ text: string; timestamp: number } | null>(null);

  // Document store pour RAG
  const { selectedDocuments, ragMode } = useDocumentStore();

  // Envoyer un message
  const sendMessage = useCallback(async (
    content: string,
    attachments?: ClaraFileAttachment[],
    session?: ClaraChatSession,
    config?: ClaraSessionConfig
  ): Promise<ClaraMessage> => {
    if (!content.trim() && (!attachments || attachments.length === 0)) {
      throw new Error('Message content or attachments required');
    }

    if (!session || !config) {
      throw new Error('Session and config required');
    }

    setIsLoading(true);

    try {
      // Créer le message utilisateur
      const userMessage: ClaraMessage = {
        id: generateId(),
        role: 'user',
        content,
        timestamp: new Date(),
        attachments,
        sessionId: session.id
      };

      // Ajouter le message utilisateur
      onMessageAdded(userMessage);
      await claraDB.saveChatMessage(userMessage);

      // Créer le message assistant (streaming)
      const streamingMessageId = generateId();
      const assistantMessage: ClaraMessage = {
        id: streamingMessageId,
        role: 'assistant',
        content: '',
        timestamp: new Date(),
        sessionId: session.id,
        isStreaming: true
      };

      onMessageAdded(assistantMessage);

      // Préparer le contexte de conversation
      const conversationHistory = await claraDB.getChatMessages(session.id);
      const recentHistory = conversationHistory
        .slice(-config.contextWindow)
        .filter(msg => msg.id !== streamingMessageId); // Exclure le message streaming

      // Déterminer si on utilise RAG
      const useRAG = config.aiConfig.features.enableRAG && 
                    selectedDocuments.length > 0 && 
                    ragMode !== 'disabled';

      let enhancedContent = content;
      let ragContext = '';

      // Recherche RAG si activée
      if (useRAG) {
        try {
          console.log('🔍 Performing RAG search...');
          const ragResults = await ragSearch(content, selectedDocuments);
          
          if (ragResults && ragResults.length > 0) {
            ragContext = ragResults.map(result => 
              `Document: ${result.source}\nContent: ${result.content}`
            ).join('\n\n');
            
            enhancedContent = `Context from documents:\n${ragContext}\n\nUser question: ${content}`;
            console.log('✅ RAG context added to message');
          }
        } catch (ragError) {
          console.warn('⚠️ RAG search failed:', ragError);
          // Continuer sans RAG en cas d'erreur
        }
      }

      // Préparer le système prompt
      let systemPrompt = config.aiConfig.systemPrompt || '';
      if (ragContext) {
        systemPrompt += '\n\nYou have access to relevant document context. Use this information to provide accurate and contextual responses.';
      }

      // Gestionnaire de streaming optimisé
      const optimizedStreamingHandler = createOptimizedContentHandler((accumulatedContent: string) => {
        onMessageUpdated(streamingMessageId, { 
          content: accumulatedContent,
          isStreaming: true 
        });
      });

      // Envoyer le message à l'API
      const aiMessage = await claraApiService.sendChatMessage(
        enhancedContent,
        config.aiConfig,
        attachments,
        systemPrompt,
        recentHistory,
        optimizedStreamingHandler
      );

      // Finaliser le message assistant
      const finalMessage: ClaraMessage = {
        ...assistantMessage,
        content: aiMessage.content,
        model: aiMessage.model,
        usage: aiMessage.usage,
        isStreaming: false,
        ragContext: ragContext || undefined
      };

      onMessageUpdated(streamingMessageId, finalMessage);
      await claraDB.saveChatMessage(finalMessage);

      // Mettre à jour la session
      await claraDB.updateChatSession(session.id, {
        title: session.messageCount === 0 ? content.slice(0, 50) + '...' : session.title,
        messageCount: session.messageCount + 2, // User + Assistant
        updatedAt: new Date()
      });

      // TTS automatique si activé
      if (aiMessage.content) {
        setLatestAIResponse(aiMessage.content);
        setAutoTTSTrigger({ text: aiMessage.content, timestamp: Date.now() });
      }

      // Notification de succès
      addCompletionNotification(
        'Message sent successfully',
        `Response generated in ${aiMessage.usage?.total_tokens || 'unknown'} tokens`
      );

      console.log('✅ Message sent successfully');
      return finalMessage;

    } catch (error) {
      console.error('❌ Failed to send message:', error);
      
      // Notification d'erreur
      addErrorNotification(
        'Failed to send message',
        error instanceof Error ? error.message : 'Unknown error'
      );

      // Marquer le message comme erreur
      onMessageUpdated(streamingMessageId, {
        content: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        isStreaming: false,
        error: true
      });

      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [onMessageAdded, onMessageUpdated, selectedDocuments, ragMode]);

  // Arrêter la génération
  const stopGeneration = useCallback(() => {
    console.log('🛑 Stopping generation...');
    setIsLoading(false);
    // TODO: Implémenter l'arrêt réel du streaming
  }, []);

  // Copier un message
  const copyMessage = useCallback(async (content: string) => {
    try {
      await navigator.clipboard.writeText(content);
      addCompletionNotification('Message copied', 'Message copied to clipboard');
    } catch (error) {
      console.error('❌ Failed to copy message:', error);
      addErrorNotification('Copy failed', 'Failed to copy message to clipboard');
    }
  }, []);

  // Réessayer un message
  const retryMessage = useCallback((messageId: string) => {
    console.log('🔄 Retrying message:', messageId);
    // TODO: Implémenter la logique de retry
  }, []);

  // Éditer un message
  const editMessage = useCallback((messageId: string, newContent: string) => {
    console.log('✏️ Editing message:', messageId, newContent);
    // TODO: Implémenter la logique d'édition
  }, []);

  return {
    // État
    isLoading,
    latestAIResponse,
    autoTTSTrigger,
    
    // Actions
    sendMessage,
    stopGeneration,
    copyMessage,
    retryMessage,
    editMessage
  };
};
