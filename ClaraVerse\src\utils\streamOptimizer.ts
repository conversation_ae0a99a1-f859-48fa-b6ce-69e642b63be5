/**
 * 🚀 OPTIMISEUR DE STREAMING
 * Optimise les performances du streaming en réduisant les mises à jour UI
 */

export class StreamOptimizer {
  private contentBuffer: string = '';
  private lastFlushTime: number = 0;
  private flushTimer: NodeJS.Timeout | null = null;
  private onFlush: (content: string) => void;
  
  // Configuration optimisée
  private readonly FLUSH_INTERVAL = 100; // Flush max toutes les 100ms
  private readonly MIN_CHUNK_SIZE = 20; // Taille minimum avant flush
  private readonly MAX_BUFFER_SIZE = 500; // Taille max du buffer avant flush forcé
  
  constructor(onFlush: (content: string) => void) {
    this.onFlush = onFlush;
  }
  
  /**
   * Ajoute du contenu au buffer avec flush intelligent
   */
  addContent(chunk: string): void {
    if (!chunk) return;
    
    this.contentBuffer += chunk;
    
    const now = Date.now();
    const timeSinceLastFlush = now - this.lastFlushTime;
    const shouldFlushByTime = timeSinceLastFlush >= this.FLUSH_INTERVAL;
    const shouldFlushBySize = this.contentBuffer.length >= this.MAX_BUFFER_SIZE;
    const hasMinimumContent = this.contentBuffer.length >= this.MIN_CHUNK_SIZE;
    
    // Flush immédiat si buffer trop gros
    if (shouldFlushBySize) {
      this.flushNow();
      return;
    }
    
    // Flush si assez de temps s'est écoulé ET qu'on a du contenu minimum
    if (shouldFlushByTime && hasMinimumContent) {
      this.flushNow();
      return;
    }
    
    // Sinon, programmer un flush différé
    this.scheduleFlush();
  }
  
  /**
   * Programme un flush différé
   */
  private scheduleFlush(): void {
    if (this.flushTimer) {
      clearTimeout(this.flushTimer);
    }
    
    this.flushTimer = setTimeout(() => {
      if (this.contentBuffer) {
        this.flushNow();
      }
    }, this.FLUSH_INTERVAL);
  }
  
  /**
   * Flush immédiat du buffer
   */
  private flushNow(): void {
    if (this.contentBuffer) {
      this.onFlush(this.contentBuffer);
      this.contentBuffer = '';
      this.lastFlushTime = Date.now();
    }
    
    if (this.flushTimer) {
      clearTimeout(this.flushTimer);
      this.flushTimer = null;
    }
  }
  
  /**
   * Flush final (pour la fin du stream)
   */
  flushFinal(): void {
    this.flushNow();
  }
  
  /**
   * Nettoie les timers
   */
  cleanup(): void {
    if (this.flushTimer) {
      clearTimeout(this.flushTimer);
      this.flushTimer = null;
    }
    this.contentBuffer = '';
  }
}

/**
 * 🚀 HOOK REACT OPTIMISÉ POUR LE STREAMING
 */
export function useOptimizedStreaming(
  onContentUpdate: (content: string) => void
): (chunk: string) => void {
  const optimizerRef = useRef<StreamOptimizer | null>(null);
  
  // Créer l'optimiseur une seule fois
  if (!optimizerRef.current) {
    optimizerRef.current = new StreamOptimizer(onContentUpdate);
  }
  
  // Nettoyer à la destruction
  useEffect(() => {
    return () => {
      optimizerRef.current?.cleanup();
    };
  }, []);
  
  // Retourner la fonction optimisée
  return useCallback((chunk: string) => {
    optimizerRef.current?.addContent(chunk);
  }, []);
}

/**
 * 🚀 WRAPPER POUR onContentChunk OPTIMISÉ
 */
export function createOptimizedContentHandler(
  originalHandler: (content: string) => void
): (content: string) => void {
  const optimizer = new StreamOptimizer(originalHandler);
  
  return (chunk: string) => {
    optimizer.addContent(chunk);
  };
}

// Imports React nécessaires
import { useRef, useEffect, useCallback } from 'react';
