{"version": 3, "sources": ["../../highlight.js/lib/languages/coq.js"], "sourcesContent": ["/*\nLanguage: Coq\nAuthor: <PERSON> <<EMAIL>>\nCategory: functional\nWebsite: https://coq.inria.fr\n*/\n\n/** @type LanguageFn */\nfunction coq(hljs) {\n  return {\n    name: 'Coq',\n    keywords: {\n      keyword:\n        '_|0 as at cofix else end exists exists2 fix for forall fun if IF in let ' +\n        'match mod Prop return Set then Type using where with ' +\n        'Abort About Add Admit Admitted All Arguments Assumptions Axiom Back BackTo ' +\n        'Backtrack Bind Blacklist Canonical Cd Check Class Classes Close Coercion ' +\n        'Coercions CoFixpoint CoInductive Collection Combined Compute Conjecture ' +\n        'Conjectures Constant constr Constraint Constructors Context Corollary ' +\n        'CreateHintDb Cut Declare Defined Definition Delimit Dependencies Dependent ' +\n        'Derive Drop eauto End Equality Eval Example Existential Existentials ' +\n        'Existing Export exporting Extern Extract Extraction Fact Field Fields File ' +\n        'Fixpoint Focus for From Function Functional Generalizable Global Goal Grab ' +\n        'Grammar Graph Guarded Heap Hint HintDb Hints Hypotheses Hypothesis ident ' +\n        'Identity If Immediate Implicit Import Include Inductive Infix Info Initial ' +\n        'Inline Inspect Instance Instances Intro Intros Inversion Inversion_clear ' +\n        'Language Left Lemma Let Libraries Library Load LoadPath Local Locate Ltac ML ' +\n        'Mode Module Modules Monomorphic Morphism Next NoInline Notation Obligation ' +\n        'Obligations Opaque Open Optimize Options Parameter Parameters Parametric ' +\n        'Path Paths pattern Polymorphic Preterm Print Printing Program Projections ' +\n        'Proof Proposition Pwd Qed Quit Rec Record Recursive Redirect Relation Remark ' +\n        'Remove Require Reserved Reset Resolve Restart Rewrite Right Ring Rings Save ' +\n        'Scheme Scope Scopes Script Search SearchAbout SearchHead SearchPattern ' +\n        'SearchRewrite Section Separate Set Setoid Show Solve Sorted Step Strategies ' +\n        'Strategy Structure SubClass Table Tables Tactic Term Test Theorem Time ' +\n        'Timeout Transparent Type Typeclasses Types Undelimit Undo Unfocus Unfocused ' +\n        'Unfold Universe Universes Unset Unshelve using Variable Variables Variant ' +\n        'Verbose Visibility where with',\n      built_in:\n        'abstract absurd admit after apply as assert assumption at auto autorewrite ' +\n        'autounfold before bottom btauto by case case_eq cbn cbv change ' +\n        'classical_left classical_right clear clearbody cofix compare compute ' +\n        'congruence constr_eq constructor contradict contradiction cut cutrewrite ' +\n        'cycle decide decompose dependent destruct destruction dintuition ' +\n        'discriminate discrR do double dtauto eapply eassumption eauto ecase ' +\n        'econstructor edestruct ediscriminate eelim eexact eexists einduction ' +\n        'einjection eleft elim elimtype enough equality erewrite eright ' +\n        'esimplify_eq esplit evar exact exactly_once exfalso exists f_equal fail ' +\n        'field field_simplify field_simplify_eq first firstorder fix fold fourier ' +\n        'functional generalize generalizing gfail give_up has_evar hnf idtac in ' +\n        'induction injection instantiate intro intro_pattern intros intuition ' +\n        'inversion inversion_clear is_evar is_var lapply lazy left lia lra move ' +\n        'native_compute nia nsatz omega once pattern pose progress proof psatz quote ' +\n        'record red refine reflexivity remember rename repeat replace revert ' +\n        'revgoals rewrite rewrite_strat right ring ring_simplify rtauto set ' +\n        'setoid_reflexivity setoid_replace setoid_rewrite setoid_symmetry ' +\n        'setoid_transitivity shelve shelve_unifiable simpl simple simplify_eq solve ' +\n        'specialize split split_Rabs split_Rmult stepl stepr subst sum swap ' +\n        'symmetry tactic tauto time timeout top transitivity trivial try tryif ' +\n        'unfold unify until using vm_compute with'\n    },\n    contains: [\n      hljs.QUOTE_STRING_MODE,\n      hljs.COMMENT('\\\\(\\\\*', '\\\\*\\\\)'),\n      hljs.C_NUMBER_MODE,\n      {\n        className: 'type',\n        excludeBegin: true,\n        begin: '\\\\|\\\\s*',\n        end: '\\\\w+'\n      },\n      { // relevance booster\n        begin: /[-=]>/\n      }\n    ]\n  };\n}\n\nmodule.exports = coq;\n"], "mappings": ";;;;;AAAA;AAAA;AAQA,aAAS,IAAI,MAAM;AACjB,aAAO;AAAA,QACL,MAAM;AAAA,QACN,UAAU;AAAA,UACR,SACE;AAAA,UAyBF,UACE;AAAA,QAqBJ;AAAA,QACA,UAAU;AAAA,UACR,KAAK;AAAA,UACL,KAAK,QAAQ,UAAU,QAAQ;AAAA,UAC/B,KAAK;AAAA,UACL;AAAA,YACE,WAAW;AAAA,YACX,cAAc;AAAA,YACd,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA;AAAA,YACE,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}