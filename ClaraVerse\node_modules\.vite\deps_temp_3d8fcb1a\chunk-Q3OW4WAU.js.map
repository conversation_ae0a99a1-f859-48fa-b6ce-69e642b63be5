{"version": 3, "sources": ["../../refractor/lang/fsharp.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = fsharp\nfsharp.displayName = 'fsharp'\nfsharp.aliases = []\nfunction fsharp(Prism) {\n  Prism.languages.fsharp = Prism.languages.extend('clike', {\n    comment: [\n      {\n        pattern: /(^|[^\\\\])\\(\\*(?!\\))[\\s\\S]*?\\*\\)/,\n        lookbehind: true,\n        greedy: true\n      },\n      {\n        pattern: /(^|[^\\\\:])\\/\\/.*/,\n        lookbehind: true,\n        greedy: true\n      }\n    ],\n    string: {\n      pattern: /(?:\"\"\"[\\s\\S]*?\"\"\"|@\"(?:\"\"|[^\"])*\"|\"(?:\\\\[\\s\\S]|[^\\\\\"])*\")B?/,\n      greedy: true\n    },\n    'class-name': {\n      pattern:\n        /(\\b(?:exception|inherit|interface|new|of|type)\\s+|\\w\\s*:\\s*|\\s:\\??>\\s*)[.\\w]+\\b(?:\\s*(?:->|\\*)\\s*[.\\w]+\\b)*(?!\\s*[:.])/,\n      lookbehind: true,\n      inside: {\n        operator: /->|\\*/,\n        punctuation: /\\./\n      }\n    },\n    keyword:\n      /\\b(?:let|return|use|yield)(?:!\\B|\\b)|\\b(?:abstract|and|as|asr|assert|atomic|base|begin|break|checked|class|component|const|constraint|constructor|continue|default|delegate|do|done|downcast|downto|eager|elif|else|end|event|exception|extern|external|false|finally|fixed|for|fun|function|functor|global|if|in|include|inherit|inline|interface|internal|land|lazy|lor|lsl|lsr|lxor|match|member|method|mixin|mod|module|mutable|namespace|new|not|null|object|of|open|or|override|parallel|private|process|protected|public|pure|rec|sealed|select|sig|static|struct|tailcall|then|to|trait|true|try|type|upcast|val|virtual|void|volatile|when|while|with)\\b/,\n    number: [\n      /\\b0x[\\da-fA-F]+(?:LF|lf|un)?\\b/,\n      /\\b0b[01]+(?:uy|y)?\\b/,\n      /(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:[fm]|e[+-]?\\d+)?\\b/i,\n      /\\b\\d+(?:[IlLsy]|UL|u[lsy]?)?\\b/\n    ],\n    operator:\n      /([<>~&^])\\1\\1|([*.:<>&])\\2|<-|->|[!=:]=|<?\\|{1,3}>?|\\??(?:<=|>=|<>|[-+*/%=<>])\\??|[!?^&]|~[+~-]|:>|:\\?>?/\n  })\n  Prism.languages.insertBefore('fsharp', 'keyword', {\n    preprocessor: {\n      pattern: /(^[\\t ]*)#.*/m,\n      lookbehind: true,\n      alias: 'property',\n      inside: {\n        directive: {\n          pattern: /(^#)\\b(?:else|endif|if|light|line|nowarn)\\b/,\n          lookbehind: true,\n          alias: 'keyword'\n        }\n      }\n    }\n  })\n  Prism.languages.insertBefore('fsharp', 'punctuation', {\n    'computation-expression': {\n      pattern: /\\b[_a-z]\\w*(?=\\s*\\{)/i,\n      alias: 'keyword'\n    }\n  })\n  Prism.languages.insertBefore('fsharp', 'string', {\n    annotation: {\n      pattern: /\\[<.+?>\\]/,\n      greedy: true,\n      inside: {\n        punctuation: /^\\[<|>\\]$/,\n        'class-name': {\n          pattern: /^\\w+$|(^|;\\s*)[A-Z]\\w*(?=\\()/,\n          lookbehind: true\n        },\n        'annotation-content': {\n          pattern: /[\\s\\S]+/,\n          inside: Prism.languages.fsharp\n        }\n      }\n    },\n    char: {\n      pattern:\n        /'(?:[^\\\\']|\\\\(?:.|\\d{3}|x[a-fA-F\\d]{2}|u[a-fA-F\\d]{4}|U[a-fA-F\\d]{8}))'B?/,\n      greedy: true\n    }\n  })\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,WAAO,cAAc;AACrB,WAAO,UAAU,CAAC;AAClB,aAAS,OAAO,OAAO;AACrB,YAAM,UAAU,SAAS,MAAM,UAAU,OAAO,SAAS;AAAA,QACvD,SAAS;AAAA,UACP;AAAA,YACE,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,QAAQ;AAAA,UACV;AAAA,UACA;AAAA,YACE,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,QAAQ;AAAA,UACV;AAAA,QACF;AAAA,QACA,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,cAAc;AAAA,UACZ,SACE;AAAA,UACF,YAAY;AAAA,UACZ,QAAQ;AAAA,YACN,UAAU;AAAA,YACV,aAAa;AAAA,UACf;AAAA,QACF;AAAA,QACA,SACE;AAAA,QACF,QAAQ;AAAA,UACN;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,UACE;AAAA,MACJ,CAAC;AACD,YAAM,UAAU,aAAa,UAAU,WAAW;AAAA,QAChD,cAAc;AAAA,UACZ,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,OAAO;AAAA,UACP,QAAQ;AAAA,YACN,WAAW;AAAA,cACT,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,OAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AACD,YAAM,UAAU,aAAa,UAAU,eAAe;AAAA,QACpD,0BAA0B;AAAA,UACxB,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,MACF,CAAC;AACD,YAAM,UAAU,aAAa,UAAU,UAAU;AAAA,QAC/C,YAAY;AAAA,UACV,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,QAAQ;AAAA,YACN,aAAa;AAAA,YACb,cAAc;AAAA,cACZ,SAAS;AAAA,cACT,YAAY;AAAA,YACd;AAAA,YACA,sBAAsB;AAAA,cACpB,SAAS;AAAA,cACT,QAAQ,MAAM,UAAU;AAAA,YAC1B;AAAA,UACF;AAAA,QACF;AAAA,QACA,MAAM;AAAA,UACJ,SACE;AAAA,UACF,QAAQ;AAAA,QACV;AAAA,MACF,CAAC;AAAA,IACH;AAAA;AAAA;", "names": []}