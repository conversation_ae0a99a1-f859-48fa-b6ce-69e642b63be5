{"version": 3, "sources": ["../../refractor/lang/jolie.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = jolie\njolie.displayName = 'jolie'\njolie.aliases = []\nfunction jolie(Prism) {\n  Prism.languages.jolie = Prism.languages.extend('clike', {\n    string: {\n      pattern: /(^|[^\\\\])\"(?:\\\\[\\s\\S]|[^\"\\\\])*\"/,\n      lookbehind: true,\n      greedy: true\n    },\n    'class-name': {\n      pattern:\n        /((?:\\b(?:as|courier|embed|in|inputPort|outputPort|service)\\b|@)[ \\t]*)\\w+/,\n      lookbehind: true\n    },\n    keyword:\n      /\\b(?:as|cH|comp|concurrent|constants|courier|cset|csets|default|define|else|embed|embedded|execution|exit|extender|for|foreach|forward|from|global|if|import|in|include|init|inputPort|install|instanceof|interface|is_defined|linkIn|linkOut|main|new|nullProcess|outputPort|over|private|provide|public|scope|sequential|service|single|spawn|synchronized|this|throw|throws|type|undef|until|while|with)\\b/,\n    function: /\\b[a-z_]\\w*(?=[ \\t]*[@(])/i,\n    number: /(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?l?/i,\n    operator: /-[-=>]?|\\+[+=]?|<[<=]?|[>=*!]=?|&&|\\|\\||[?\\/%^@|]/,\n    punctuation: /[()[\\]{},;.:]/,\n    builtin:\n      /\\b(?:Byte|any|bool|char|double|enum|float|int|length|long|ranges|regex|string|undefined|void)\\b/\n  })\n  Prism.languages.insertBefore('jolie', 'keyword', {\n    aggregates: {\n      pattern:\n        /(\\bAggregates\\s*:\\s*)(?:\\w+(?:\\s+with\\s+\\w+)?\\s*,\\s*)*\\w+(?:\\s+with\\s+\\w+)?/,\n      lookbehind: true,\n      inside: {\n        keyword: /\\bwith\\b/,\n        'class-name': /\\w+/,\n        punctuation: /,/\n      }\n    },\n    redirects: {\n      pattern:\n        /(\\bRedirects\\s*:\\s*)(?:\\w+\\s*=>\\s*\\w+\\s*,\\s*)*(?:\\w+\\s*=>\\s*\\w+)/,\n      lookbehind: true,\n      inside: {\n        punctuation: /,/,\n        'class-name': /\\w+/,\n        operator: /=>/\n      }\n    },\n    property: {\n      pattern:\n        /\\b(?:Aggregates|[Ii]nterfaces|Java|Javascript|Jolie|[Ll]ocation|OneWay|[Pp]rotocol|Redirects|RequestResponse)\\b(?=[ \\t]*:)/\n    }\n  })\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,UAAM,cAAc;AACpB,UAAM,UAAU,CAAC;AACjB,aAAS,MAAM,OAAO;AACpB,YAAM,UAAU,QAAQ,MAAM,UAAU,OAAO,SAAS;AAAA,QACtD,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,QAAQ;AAAA,QACV;AAAA,QACA,cAAc;AAAA,UACZ,SACE;AAAA,UACF,YAAY;AAAA,QACd;AAAA,QACA,SACE;AAAA,QACF,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,aAAa;AAAA,QACb,SACE;AAAA,MACJ,CAAC;AACD,YAAM,UAAU,aAAa,SAAS,WAAW;AAAA,QAC/C,YAAY;AAAA,UACV,SACE;AAAA,UACF,YAAY;AAAA,UACZ,QAAQ;AAAA,YACN,SAAS;AAAA,YACT,cAAc;AAAA,YACd,aAAa;AAAA,UACf;AAAA,QACF;AAAA,QACA,WAAW;AAAA,UACT,SACE;AAAA,UACF,YAAY;AAAA,UACZ,QAAQ;AAAA,YACN,aAAa;AAAA,YACb,cAAc;AAAA,YACd,UAAU;AAAA,UACZ;AAAA,QACF;AAAA,QACA,UAAU;AAAA,UACR,SACE;AAAA,QACJ;AAAA,MACF,CAAC;AAAA,IACH;AAAA;AAAA;", "names": []}