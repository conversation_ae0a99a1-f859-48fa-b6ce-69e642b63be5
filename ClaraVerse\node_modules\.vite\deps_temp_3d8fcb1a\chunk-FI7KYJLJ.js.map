{"version": 3, "sources": ["../../highlight.js/lib/languages/pony.js"], "sourcesContent": ["/*\nLanguage: Pony\nAuthor: <PERSON> <<EMAIL>>\nDescription: Pony is an open-source, object-oriented, actor-model,\n             capabilities-secure, high performance programming language.\nWebsite: https://www.ponylang.io\n*/\n\nfunction pony(hljs) {\n  const KEYWORDS = {\n    keyword:\n      'actor addressof and as be break class compile_error compile_intrinsic ' +\n      'consume continue delegate digestof do else elseif embed end error ' +\n      'for fun if ifdef in interface is isnt lambda let match new not object ' +\n      'or primitive recover repeat return struct then trait try type until ' +\n      'use var where while with xor',\n    meta:\n      'iso val tag trn box ref',\n    literal:\n      'this false true'\n  };\n\n  const TRIPLE_QUOTE_STRING_MODE = {\n    className: 'string',\n    begin: '\"\"\"',\n    end: '\"\"\"',\n    relevance: 10\n  };\n\n  const QUOTE_STRING_MODE = {\n    className: 'string',\n    begin: '\"',\n    end: '\"',\n    contains: [ hljs.BACKSLASH_ESCAPE ]\n  };\n\n  const SINGLE_QUOTE_CHAR_MODE = {\n    className: 'string',\n    begin: '\\'',\n    end: '\\'',\n    contains: [ hljs.BACKSLASH_ESCAPE ],\n    relevance: 0\n  };\n\n  const TYPE_NAME = {\n    className: 'type',\n    begin: '\\\\b_?[A-Z][\\\\w]*',\n    relevance: 0\n  };\n\n  const PRIMED_NAME = {\n    begin: hljs.IDENT_RE + '\\'',\n    relevance: 0\n  };\n\n  const NUMBER_MODE = {\n    className: 'number',\n    begin: '(-?)(\\\\b0[xX][a-fA-F0-9]+|\\\\b0[bB][01]+|(\\\\b\\\\d+(_\\\\d+)?(\\\\.\\\\d*)?|\\\\.\\\\d+)([eE][-+]?\\\\d+)?)',\n    relevance: 0\n  };\n\n  /**\n   * The `FUNCTION` and `CLASS` modes were intentionally removed to simplify\n   * highlighting and fix cases like\n   * ```\n   * interface Iterator[A: A]\n   *   fun has_next(): Bool\n   *   fun next(): A?\n   * ```\n   * where it is valid to have a function head without a body\n   */\n\n  return {\n    name: 'Pony',\n    keywords: KEYWORDS,\n    contains: [\n      TYPE_NAME,\n      TRIPLE_QUOTE_STRING_MODE,\n      QUOTE_STRING_MODE,\n      SINGLE_QUOTE_CHAR_MODE,\n      PRIMED_NAME,\n      NUMBER_MODE,\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE\n    ]\n  };\n}\n\nmodule.exports = pony;\n"], "mappings": ";;;;;AAAA;AAAA;AAQA,aAAS,KAAK,MAAM;AAClB,YAAM,WAAW;AAAA,QACf,SACE;AAAA,QAKF,MACE;AAAA,QACF,SACE;AAAA,MACJ;AAEA,YAAM,2BAA2B;AAAA,QAC/B,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,WAAW;AAAA,MACb;AAEA,YAAM,oBAAoB;AAAA,QACxB,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU,CAAE,KAAK,gBAAiB;AAAA,MACpC;AAEA,YAAM,yBAAyB;AAAA,QAC7B,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU,CAAE,KAAK,gBAAiB;AAAA,QAClC,WAAW;AAAA,MACb;AAEA,YAAM,YAAY;AAAA,QAChB,WAAW;AAAA,QACX,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAEA,YAAM,cAAc;AAAA,QAClB,OAAO,KAAK,WAAW;AAAA,QACvB,WAAW;AAAA,MACb;AAEA,YAAM,cAAc;AAAA,QAClB,WAAW;AAAA,QACX,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAaA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,UAAU;AAAA,QACV,UAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,QACP;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}