{"version": 3, "sources": ["../../use-sync-external-store/cjs/use-sync-external-store-shim.development.js", "../../use-sync-external-store/shim/index.js", "../../use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.development.js", "../../use-sync-external-store/shim/with-selector.js", "../../@reactflow/core/dist/esm/index.mjs", "../../classcat/index.js", "../../@reactflow/core/node_modules/zustand/esm/traditional.mjs", "../../@reactflow/core/node_modules/zustand/esm/vanilla.mjs", "../../@reactflow/core/node_modules/zustand/esm/shallow.mjs", "../../@reactflow/minimap/dist/esm/index.mjs", "../../@reactflow/minimap/node_modules/zustand/esm/shallow.mjs", "../../@reactflow/controls/dist/esm/index.mjs", "../../@reactflow/controls/node_modules/zustand/esm/shallow.mjs", "../../@reactflow/background/dist/esm/index.mjs", "../../@reactflow/background/node_modules/zustand/esm/shallow.mjs", "../../@reactflow/node-toolbar/dist/esm/index.mjs", "../../@reactflow/node-toolbar/node_modules/zustand/esm/shallow.mjs", "../../@reactflow/node-resizer/dist/esm/index.mjs"], "sourcesContent": ["/**\n * @license React\n * use-sync-external-store-shim.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function is(x, y) {\n      return (x === y && (0 !== x || 1 / x === 1 / y)) || (x !== x && y !== y);\n    }\n    function useSyncExternalStore$2(subscribe, getSnapshot) {\n      didWarnOld18Alpha ||\n        void 0 === React.startTransition ||\n        ((didWarnOld18Alpha = !0),\n        console.error(\n          \"You are using an outdated, pre-release alpha of React 18 that does not support useSyncExternalStore. The use-sync-external-store shim will not work correctly. Upgrade to a newer pre-release.\"\n        ));\n      var value = getSnapshot();\n      if (!didWarnUncachedGetSnapshot) {\n        var cachedValue = getSnapshot();\n        objectIs(value, cachedValue) ||\n          (console.error(\n            \"The result of getSnapshot should be cached to avoid an infinite loop\"\n          ),\n          (didWarnUncachedGetSnapshot = !0));\n      }\n      cachedValue = useState({\n        inst: { value: value, getSnapshot: getSnapshot }\n      });\n      var inst = cachedValue[0].inst,\n        forceUpdate = cachedValue[1];\n      useLayoutEffect(\n        function () {\n          inst.value = value;\n          inst.getSnapshot = getSnapshot;\n          checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n        },\n        [subscribe, value, getSnapshot]\n      );\n      useEffect(\n        function () {\n          checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n          return subscribe(function () {\n            checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n          });\n        },\n        [subscribe]\n      );\n      useDebugValue(value);\n      return value;\n    }\n    function checkIfSnapshotChanged(inst) {\n      var latestGetSnapshot = inst.getSnapshot;\n      inst = inst.value;\n      try {\n        var nextValue = latestGetSnapshot();\n        return !objectIs(inst, nextValue);\n      } catch (error) {\n        return !0;\n      }\n    }\n    function useSyncExternalStore$1(subscribe, getSnapshot) {\n      return getSnapshot();\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    var React = require(\"react\"),\n      objectIs = \"function\" === typeof Object.is ? Object.is : is,\n      useState = React.useState,\n      useEffect = React.useEffect,\n      useLayoutEffect = React.useLayoutEffect,\n      useDebugValue = React.useDebugValue,\n      didWarnOld18Alpha = !1,\n      didWarnUncachedGetSnapshot = !1,\n      shim =\n        \"undefined\" === typeof window ||\n        \"undefined\" === typeof window.document ||\n        \"undefined\" === typeof window.document.createElement\n          ? useSyncExternalStore$1\n          : useSyncExternalStore$2;\n    exports.useSyncExternalStore =\n      void 0 !== React.useSyncExternalStore ? React.useSyncExternalStore : shim;\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n  })();\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim.production.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim.development.js');\n}\n", "/**\n * @license React\n * use-sync-external-store-shim/with-selector.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function is(x, y) {\n      return (x === y && (0 !== x || 1 / x === 1 / y)) || (x !== x && y !== y);\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    var React = require(\"react\"),\n      shim = require(\"use-sync-external-store/shim\"),\n      objectIs = \"function\" === typeof Object.is ? Object.is : is,\n      useSyncExternalStore = shim.useSyncExternalStore,\n      useRef = React.useRef,\n      useEffect = React.useEffect,\n      useMemo = React.useMemo,\n      useDebugValue = React.useDebugValue;\n    exports.useSyncExternalStoreWithSelector = function (\n      subscribe,\n      getSnapshot,\n      getServerSnapshot,\n      selector,\n      isEqual\n    ) {\n      var instRef = useRef(null);\n      if (null === instRef.current) {\n        var inst = { hasValue: !1, value: null };\n        instRef.current = inst;\n      } else inst = instRef.current;\n      instRef = useMemo(\n        function () {\n          function memoizedSelector(nextSnapshot) {\n            if (!hasMemo) {\n              hasMemo = !0;\n              memoizedSnapshot = nextSnapshot;\n              nextSnapshot = selector(nextSnapshot);\n              if (void 0 !== isEqual && inst.hasValue) {\n                var currentSelection = inst.value;\n                if (isEqual(currentSelection, nextSnapshot))\n                  return (memoizedSelection = currentSelection);\n              }\n              return (memoizedSelection = nextSnapshot);\n            }\n            currentSelection = memoizedSelection;\n            if (objectIs(memoizedSnapshot, nextSnapshot))\n              return currentSelection;\n            var nextSelection = selector(nextSnapshot);\n            if (void 0 !== isEqual && isEqual(currentSelection, nextSelection))\n              return (memoizedSnapshot = nextSnapshot), currentSelection;\n            memoizedSnapshot = nextSnapshot;\n            return (memoizedSelection = nextSelection);\n          }\n          var hasMemo = !1,\n            memoizedSnapshot,\n            memoizedSelection,\n            maybeGetServerSnapshot =\n              void 0 === getServerSnapshot ? null : getServerSnapshot;\n          return [\n            function () {\n              return memoizedSelector(getSnapshot());\n            },\n            null === maybeGetServerSnapshot\n              ? void 0\n              : function () {\n                  return memoizedSelector(maybeGetServerSnapshot());\n                }\n          ];\n        },\n        [getSnapshot, getServerSnapshot, selector, isEqual]\n      );\n      var value = useSyncExternalStore(subscribe, instRef[0], instRef[1]);\n      useEffect(\n        function () {\n          inst.hasValue = !0;\n          inst.value = value;\n        },\n        [value]\n      );\n      useDebugValue(value);\n      return value;\n    };\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n  })();\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim/with-selector.production.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim/with-selector.development.js');\n}\n", "import React, { createContext, useContext, useMemo, memo, useRef, useState, useEffect, forwardRef, useCallback } from 'react';\nimport cc from 'classcat';\nimport { useStoreWithEqualityFn, createWithEqualityFn } from 'zustand/traditional';\nimport { shallow } from 'zustand/shallow';\nimport { zoomIdentity, zoom } from 'd3-zoom';\nimport { select, pointer } from 'd3-selection';\nimport { drag } from 'd3-drag';\nimport { createPortal } from 'react-dom';\n\nconst StoreContext = createContext(null);\nconst Provider$1 = StoreContext.Provider;\n\nconst errorMessages = {\n    error001: () => '[React Flow]: Seems like you have not used zustand provider as an ancestor. Help: https://reactflow.dev/error#001',\n    error002: () => \"It looks like you've created a new nodeTypes or edgeTypes object. If this wasn't on purpose please define the nodeTypes/edgeTypes outside of the component or memoize them.\",\n    error003: (nodeType) => `Node type \"${nodeType}\" not found. Using fallback type \"default\".`,\n    error004: () => 'The React Flow parent container needs a width and a height to render the graph.',\n    error005: () => 'Only child nodes can use a parent extent.',\n    error006: () => \"Can't create edge. An edge needs a source and a target.\",\n    error007: (id) => `The old edge with id=${id} does not exist.`,\n    error009: (type) => `Marker type \"${type}\" doesn't exist.`,\n    error008: (sourceHandle, edge) => `Couldn't create edge for ${!sourceHandle ? 'source' : 'target'} handle id: \"${!sourceHandle ? edge.sourceHandle : edge.targetHandle}\", edge id: ${edge.id}.`,\n    error010: () => 'Handle: No node id found. Make sure to only use a Handle inside a custom Node.',\n    error011: (edgeType) => `Edge type \"${edgeType}\" not found. Using fallback type \"default\".`,\n    error012: (id) => `Node with id \"${id}\" does not exist, it may have been removed. This can happen when a node is deleted before the \"onNodeClick\" handler is called.`,\n};\n\nconst zustandErrorMessage = errorMessages['error001']();\nfunction useStore(selector, equalityFn) {\n    const store = useContext(StoreContext);\n    if (store === null) {\n        throw new Error(zustandErrorMessage);\n    }\n    return useStoreWithEqualityFn(store, selector, equalityFn);\n}\nconst useStoreApi = () => {\n    const store = useContext(StoreContext);\n    if (store === null) {\n        throw new Error(zustandErrorMessage);\n    }\n    return useMemo(() => ({\n        getState: store.getState,\n        setState: store.setState,\n        subscribe: store.subscribe,\n        destroy: store.destroy,\n    }), [store]);\n};\n\nconst selector$g = (s) => (s.userSelectionActive ? 'none' : 'all');\nfunction Panel({ position, children, className, style, ...rest }) {\n    const pointerEvents = useStore(selector$g);\n    const positionClasses = `${position}`.split('-');\n    return (React.createElement(\"div\", { className: cc(['react-flow__panel', className, ...positionClasses]), style: { ...style, pointerEvents }, ...rest }, children));\n}\n\nfunction Attribution({ proOptions, position = 'bottom-right' }) {\n    if (proOptions?.hideAttribution) {\n        return null;\n    }\n    return (React.createElement(Panel, { position: position, className: \"react-flow__attribution\", \"data-message\": \"Please only hide this attribution when you are subscribed to React Flow Pro: https://reactflow.dev/pro\" },\n        React.createElement(\"a\", { href: \"https://reactflow.dev\", target: \"_blank\", rel: \"noopener noreferrer\", \"aria-label\": \"React Flow attribution\" }, \"React Flow\")));\n}\n\nconst EdgeText = ({ x, y, label, labelStyle = {}, labelShowBg = true, labelBgStyle = {}, labelBgPadding = [2, 4], labelBgBorderRadius = 2, children, className, ...rest }) => {\n    const edgeRef = useRef(null);\n    const [edgeTextBbox, setEdgeTextBbox] = useState({ x: 0, y: 0, width: 0, height: 0 });\n    const edgeTextClasses = cc(['react-flow__edge-textwrapper', className]);\n    useEffect(() => {\n        if (edgeRef.current) {\n            const textBbox = edgeRef.current.getBBox();\n            setEdgeTextBbox({\n                x: textBbox.x,\n                y: textBbox.y,\n                width: textBbox.width,\n                height: textBbox.height,\n            });\n        }\n    }, [label]);\n    if (typeof label === 'undefined' || !label) {\n        return null;\n    }\n    return (React.createElement(\"g\", { transform: `translate(${x - edgeTextBbox.width / 2} ${y - edgeTextBbox.height / 2})`, className: edgeTextClasses, visibility: edgeTextBbox.width ? 'visible' : 'hidden', ...rest },\n        labelShowBg && (React.createElement(\"rect\", { width: edgeTextBbox.width + 2 * labelBgPadding[0], x: -labelBgPadding[0], y: -labelBgPadding[1], height: edgeTextBbox.height + 2 * labelBgPadding[1], className: \"react-flow__edge-textbg\", style: labelBgStyle, rx: labelBgBorderRadius, ry: labelBgBorderRadius })),\n        React.createElement(\"text\", { className: \"react-flow__edge-text\", y: edgeTextBbox.height / 2, dy: \"0.3em\", ref: edgeRef, style: labelStyle }, label),\n        children));\n};\nvar EdgeText$1 = memo(EdgeText);\n\nconst getDimensions = (node) => ({\n    width: node.offsetWidth,\n    height: node.offsetHeight,\n});\nconst clamp = (val, min = 0, max = 1) => Math.min(Math.max(val, min), max);\nconst clampPosition = (position = { x: 0, y: 0 }, extent) => ({\n    x: clamp(position.x, extent[0][0], extent[1][0]),\n    y: clamp(position.y, extent[0][1], extent[1][1]),\n});\n// returns a number between 0 and 1 that represents the velocity of the movement\n// when the mouse is close to the edge of the canvas\nconst calcAutoPanVelocity = (value, min, max) => {\n    if (value < min) {\n        return clamp(Math.abs(value - min), 1, 50) / 50;\n    }\n    else if (value > max) {\n        return -clamp(Math.abs(value - max), 1, 50) / 50;\n    }\n    return 0;\n};\nconst calcAutoPan = (pos, bounds) => {\n    const xMovement = calcAutoPanVelocity(pos.x, 35, bounds.width - 35) * 20;\n    const yMovement = calcAutoPanVelocity(pos.y, 35, bounds.height - 35) * 20;\n    return [xMovement, yMovement];\n};\nconst getHostForElement = (element) => element.getRootNode?.() || window?.document;\nconst getBoundsOfBoxes = (box1, box2) => ({\n    x: Math.min(box1.x, box2.x),\n    y: Math.min(box1.y, box2.y),\n    x2: Math.max(box1.x2, box2.x2),\n    y2: Math.max(box1.y2, box2.y2),\n});\nconst rectToBox = ({ x, y, width, height }) => ({\n    x,\n    y,\n    x2: x + width,\n    y2: y + height,\n});\nconst boxToRect = ({ x, y, x2, y2 }) => ({\n    x,\n    y,\n    width: x2 - x,\n    height: y2 - y,\n});\nconst nodeToRect = (node) => ({\n    ...(node.positionAbsolute || { x: 0, y: 0 }),\n    width: node.width || 0,\n    height: node.height || 0,\n});\nconst getBoundsOfRects = (rect1, rect2) => boxToRect(getBoundsOfBoxes(rectToBox(rect1), rectToBox(rect2)));\nconst getOverlappingArea = (rectA, rectB) => {\n    const xOverlap = Math.max(0, Math.min(rectA.x + rectA.width, rectB.x + rectB.width) - Math.max(rectA.x, rectB.x));\n    const yOverlap = Math.max(0, Math.min(rectA.y + rectA.height, rectB.y + rectB.height) - Math.max(rectA.y, rectB.y));\n    return Math.ceil(xOverlap * yOverlap);\n};\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nconst isRectObject = (obj) => isNumeric(obj.width) && isNumeric(obj.height) && isNumeric(obj.x) && isNumeric(obj.y);\n/* eslint-disable-next-line @typescript-eslint/no-explicit-any */\nconst isNumeric = (n) => !isNaN(n) && isFinite(n);\nconst internalsSymbol = Symbol.for('internals');\n// used for a11y key board controls for nodes and edges\nconst elementSelectionKeys = ['Enter', ' ', 'Escape'];\nconst devWarn = (id, message) => {\n    if (process.env.NODE_ENV === 'development') {\n        console.warn(`[React Flow]: ${message} Help: https://reactflow.dev/error#${id}`);\n    }\n};\nconst isReactKeyboardEvent = (event) => 'nativeEvent' in event;\nfunction isInputDOMNode(event) {\n    const kbEvent = isReactKeyboardEvent(event) ? event.nativeEvent : event;\n    // using composed path for handling shadow dom\n    const target = (kbEvent.composedPath?.()?.[0] || event.target);\n    const isInput = ['INPUT', 'SELECT', 'TEXTAREA'].includes(target?.nodeName) || target?.hasAttribute('contenteditable');\n    // when an input field is focused we don't want to trigger deletion or movement of nodes\n    return isInput || !!target?.closest('.nokey');\n}\nconst isMouseEvent = (event) => 'clientX' in event;\nconst getEventPosition = (event, bounds) => {\n    const isMouseTriggered = isMouseEvent(event);\n    const evtX = isMouseTriggered ? event.clientX : event.touches?.[0].clientX;\n    const evtY = isMouseTriggered ? event.clientY : event.touches?.[0].clientY;\n    return {\n        x: evtX - (bounds?.left ?? 0),\n        y: evtY - (bounds?.top ?? 0),\n    };\n};\nconst isMacOs = () => typeof navigator !== 'undefined' && navigator?.userAgent?.indexOf('Mac') >= 0;\n\nconst BaseEdge = ({ id, path, labelX, labelY, label, labelStyle, labelShowBg, labelBgStyle, labelBgPadding, labelBgBorderRadius, style, markerEnd, markerStart, interactionWidth = 20, }) => {\n    return (React.createElement(React.Fragment, null,\n        React.createElement(\"path\", { id: id, style: style, d: path, fill: \"none\", className: \"react-flow__edge-path\", markerEnd: markerEnd, markerStart: markerStart }),\n        interactionWidth && (React.createElement(\"path\", { d: path, fill: \"none\", strokeOpacity: 0, strokeWidth: interactionWidth, className: \"react-flow__edge-interaction\" })),\n        label && isNumeric(labelX) && isNumeric(labelY) ? (React.createElement(EdgeText$1, { x: labelX, y: labelY, label: label, labelStyle: labelStyle, labelShowBg: labelShowBg, labelBgStyle: labelBgStyle, labelBgPadding: labelBgPadding, labelBgBorderRadius: labelBgBorderRadius })) : null));\n};\nBaseEdge.displayName = 'BaseEdge';\n\nconst getMarkerEnd = (markerType, markerEndId) => {\n    if (typeof markerEndId !== 'undefined' && markerEndId) {\n        return `url(#${markerEndId})`;\n    }\n    return typeof markerType !== 'undefined' ? `url(#react-flow__${markerType})` : 'none';\n};\nfunction getMouseHandler$1(id, getState, handler) {\n    return handler === undefined\n        ? handler\n        : (event) => {\n            const edge = getState().edges.find((e) => e.id === id);\n            if (edge) {\n                handler(event, { ...edge });\n            }\n        };\n}\n// this is used for straight edges and simple smoothstep edges (LTR, RTL, BTT, TTB)\nfunction getEdgeCenter({ sourceX, sourceY, targetX, targetY, }) {\n    const xOffset = Math.abs(targetX - sourceX) / 2;\n    const centerX = targetX < sourceX ? targetX + xOffset : targetX - xOffset;\n    const yOffset = Math.abs(targetY - sourceY) / 2;\n    const centerY = targetY < sourceY ? targetY + yOffset : targetY - yOffset;\n    return [centerX, centerY, xOffset, yOffset];\n}\nfunction getBezierEdgeCenter({ sourceX, sourceY, targetX, targetY, sourceControlX, sourceControlY, targetControlX, targetControlY, }) {\n    // cubic bezier t=0.5 mid point, not the actual mid point, but easy to calculate\n    // https://stackoverflow.com/questions/67516101/how-to-find-distance-mid-point-of-bezier-curve\n    const centerX = sourceX * 0.125 + sourceControlX * 0.375 + targetControlX * 0.375 + targetX * 0.125;\n    const centerY = sourceY * 0.125 + sourceControlY * 0.375 + targetControlY * 0.375 + targetY * 0.125;\n    const offsetX = Math.abs(centerX - sourceX);\n    const offsetY = Math.abs(centerY - sourceY);\n    return [centerX, centerY, offsetX, offsetY];\n}\n\nvar ConnectionMode;\n(function (ConnectionMode) {\n    ConnectionMode[\"Strict\"] = \"strict\";\n    ConnectionMode[\"Loose\"] = \"loose\";\n})(ConnectionMode || (ConnectionMode = {}));\nvar PanOnScrollMode;\n(function (PanOnScrollMode) {\n    PanOnScrollMode[\"Free\"] = \"free\";\n    PanOnScrollMode[\"Vertical\"] = \"vertical\";\n    PanOnScrollMode[\"Horizontal\"] = \"horizontal\";\n})(PanOnScrollMode || (PanOnScrollMode = {}));\nvar SelectionMode;\n(function (SelectionMode) {\n    SelectionMode[\"Partial\"] = \"partial\";\n    SelectionMode[\"Full\"] = \"full\";\n})(SelectionMode || (SelectionMode = {}));\n\nvar ConnectionLineType;\n(function (ConnectionLineType) {\n    ConnectionLineType[\"Bezier\"] = \"default\";\n    ConnectionLineType[\"Straight\"] = \"straight\";\n    ConnectionLineType[\"Step\"] = \"step\";\n    ConnectionLineType[\"SmoothStep\"] = \"smoothstep\";\n    ConnectionLineType[\"SimpleBezier\"] = \"simplebezier\";\n})(ConnectionLineType || (ConnectionLineType = {}));\nvar MarkerType;\n(function (MarkerType) {\n    MarkerType[\"Arrow\"] = \"arrow\";\n    MarkerType[\"ArrowClosed\"] = \"arrowclosed\";\n})(MarkerType || (MarkerType = {}));\n\nvar Position;\n(function (Position) {\n    Position[\"Left\"] = \"left\";\n    Position[\"Top\"] = \"top\";\n    Position[\"Right\"] = \"right\";\n    Position[\"Bottom\"] = \"bottom\";\n})(Position || (Position = {}));\n\nfunction getControl({ pos, x1, y1, x2, y2 }) {\n    if (pos === Position.Left || pos === Position.Right) {\n        return [0.5 * (x1 + x2), y1];\n    }\n    return [x1, 0.5 * (y1 + y2)];\n}\nfunction getSimpleBezierPath({ sourceX, sourceY, sourcePosition = Position.Bottom, targetX, targetY, targetPosition = Position.Top, }) {\n    const [sourceControlX, sourceControlY] = getControl({\n        pos: sourcePosition,\n        x1: sourceX,\n        y1: sourceY,\n        x2: targetX,\n        y2: targetY,\n    });\n    const [targetControlX, targetControlY] = getControl({\n        pos: targetPosition,\n        x1: targetX,\n        y1: targetY,\n        x2: sourceX,\n        y2: sourceY,\n    });\n    const [labelX, labelY, offsetX, offsetY] = getBezierEdgeCenter({\n        sourceX,\n        sourceY,\n        targetX,\n        targetY,\n        sourceControlX,\n        sourceControlY,\n        targetControlX,\n        targetControlY,\n    });\n    return [\n        `M${sourceX},${sourceY} C${sourceControlX},${sourceControlY} ${targetControlX},${targetControlY} ${targetX},${targetY}`,\n        labelX,\n        labelY,\n        offsetX,\n        offsetY,\n    ];\n}\nconst SimpleBezierEdge = memo(({ sourceX, sourceY, targetX, targetY, sourcePosition = Position.Bottom, targetPosition = Position.Top, label, labelStyle, labelShowBg, labelBgStyle, labelBgPadding, labelBgBorderRadius, style, markerEnd, markerStart, interactionWidth, }) => {\n    const [path, labelX, labelY] = getSimpleBezierPath({\n        sourceX,\n        sourceY,\n        sourcePosition,\n        targetX,\n        targetY,\n        targetPosition,\n    });\n    return (React.createElement(BaseEdge, { path: path, labelX: labelX, labelY: labelY, label: label, labelStyle: labelStyle, labelShowBg: labelShowBg, labelBgStyle: labelBgStyle, labelBgPadding: labelBgPadding, labelBgBorderRadius: labelBgBorderRadius, style: style, markerEnd: markerEnd, markerStart: markerStart, interactionWidth: interactionWidth }));\n});\nSimpleBezierEdge.displayName = 'SimpleBezierEdge';\n\nconst handleDirections = {\n    [Position.Left]: { x: -1, y: 0 },\n    [Position.Right]: { x: 1, y: 0 },\n    [Position.Top]: { x: 0, y: -1 },\n    [Position.Bottom]: { x: 0, y: 1 },\n};\nconst getDirection = ({ source, sourcePosition = Position.Bottom, target, }) => {\n    if (sourcePosition === Position.Left || sourcePosition === Position.Right) {\n        return source.x < target.x ? { x: 1, y: 0 } : { x: -1, y: 0 };\n    }\n    return source.y < target.y ? { x: 0, y: 1 } : { x: 0, y: -1 };\n};\nconst distance = (a, b) => Math.sqrt(Math.pow(b.x - a.x, 2) + Math.pow(b.y - a.y, 2));\n// ith this function we try to mimic a orthogonal edge routing behaviour\n// It's not as good as a real orthogonal edge routing but it's faster and good enough as a default for step and smooth step edges\nfunction getPoints({ source, sourcePosition = Position.Bottom, target, targetPosition = Position.Top, center, offset, }) {\n    const sourceDir = handleDirections[sourcePosition];\n    const targetDir = handleDirections[targetPosition];\n    const sourceGapped = { x: source.x + sourceDir.x * offset, y: source.y + sourceDir.y * offset };\n    const targetGapped = { x: target.x + targetDir.x * offset, y: target.y + targetDir.y * offset };\n    const dir = getDirection({\n        source: sourceGapped,\n        sourcePosition,\n        target: targetGapped,\n    });\n    const dirAccessor = dir.x !== 0 ? 'x' : 'y';\n    const currDir = dir[dirAccessor];\n    let points = [];\n    let centerX, centerY;\n    const sourceGapOffset = { x: 0, y: 0 };\n    const targetGapOffset = { x: 0, y: 0 };\n    const [defaultCenterX, defaultCenterY, defaultOffsetX, defaultOffsetY] = getEdgeCenter({\n        sourceX: source.x,\n        sourceY: source.y,\n        targetX: target.x,\n        targetY: target.y,\n    });\n    // opposite handle positions, default case\n    if (sourceDir[dirAccessor] * targetDir[dirAccessor] === -1) {\n        centerX = center.x ?? defaultCenterX;\n        centerY = center.y ?? defaultCenterY;\n        //    --->\n        //    |\n        // >---\n        const verticalSplit = [\n            { x: centerX, y: sourceGapped.y },\n            { x: centerX, y: targetGapped.y },\n        ];\n        //    |\n        //  ---\n        //  |\n        const horizontalSplit = [\n            { x: sourceGapped.x, y: centerY },\n            { x: targetGapped.x, y: centerY },\n        ];\n        if (sourceDir[dirAccessor] === currDir) {\n            points = dirAccessor === 'x' ? verticalSplit : horizontalSplit;\n        }\n        else {\n            points = dirAccessor === 'x' ? horizontalSplit : verticalSplit;\n        }\n    }\n    else {\n        // sourceTarget means we take x from source and y from target, targetSource is the opposite\n        const sourceTarget = [{ x: sourceGapped.x, y: targetGapped.y }];\n        const targetSource = [{ x: targetGapped.x, y: sourceGapped.y }];\n        // this handles edges with same handle positions\n        if (dirAccessor === 'x') {\n            points = sourceDir.x === currDir ? targetSource : sourceTarget;\n        }\n        else {\n            points = sourceDir.y === currDir ? sourceTarget : targetSource;\n        }\n        if (sourcePosition === targetPosition) {\n            const diff = Math.abs(source[dirAccessor] - target[dirAccessor]);\n            // if an edge goes from right to right for example (sourcePosition === targetPosition) and the distance between source.x and target.x is less than the offset, the added point and the gapped source/target will overlap. This leads to a weird edge path. To avoid this we add a gapOffset to the source/target\n            if (diff <= offset) {\n                const gapOffset = Math.min(offset - 1, offset - diff);\n                if (sourceDir[dirAccessor] === currDir) {\n                    sourceGapOffset[dirAccessor] = (sourceGapped[dirAccessor] > source[dirAccessor] ? -1 : 1) * gapOffset;\n                }\n                else {\n                    targetGapOffset[dirAccessor] = (targetGapped[dirAccessor] > target[dirAccessor] ? -1 : 1) * gapOffset;\n                }\n            }\n        }\n        // these are conditions for handling mixed handle positions like Right -> Bottom for example\n        if (sourcePosition !== targetPosition) {\n            const dirAccessorOpposite = dirAccessor === 'x' ? 'y' : 'x';\n            const isSameDir = sourceDir[dirAccessor] === targetDir[dirAccessorOpposite];\n            const sourceGtTargetOppo = sourceGapped[dirAccessorOpposite] > targetGapped[dirAccessorOpposite];\n            const sourceLtTargetOppo = sourceGapped[dirAccessorOpposite] < targetGapped[dirAccessorOpposite];\n            const flipSourceTarget = (sourceDir[dirAccessor] === 1 && ((!isSameDir && sourceGtTargetOppo) || (isSameDir && sourceLtTargetOppo))) ||\n                (sourceDir[dirAccessor] !== 1 && ((!isSameDir && sourceLtTargetOppo) || (isSameDir && sourceGtTargetOppo)));\n            if (flipSourceTarget) {\n                points = dirAccessor === 'x' ? sourceTarget : targetSource;\n            }\n        }\n        const sourceGapPoint = { x: sourceGapped.x + sourceGapOffset.x, y: sourceGapped.y + sourceGapOffset.y };\n        const targetGapPoint = { x: targetGapped.x + targetGapOffset.x, y: targetGapped.y + targetGapOffset.y };\n        const maxXDistance = Math.max(Math.abs(sourceGapPoint.x - points[0].x), Math.abs(targetGapPoint.x - points[0].x));\n        const maxYDistance = Math.max(Math.abs(sourceGapPoint.y - points[0].y), Math.abs(targetGapPoint.y - points[0].y));\n        // we want to place the label on the longest segment of the edge\n        if (maxXDistance >= maxYDistance) {\n            centerX = (sourceGapPoint.x + targetGapPoint.x) / 2;\n            centerY = points[0].y;\n        }\n        else {\n            centerX = points[0].x;\n            centerY = (sourceGapPoint.y + targetGapPoint.y) / 2;\n        }\n    }\n    const pathPoints = [\n        source,\n        { x: sourceGapped.x + sourceGapOffset.x, y: sourceGapped.y + sourceGapOffset.y },\n        ...points,\n        { x: targetGapped.x + targetGapOffset.x, y: targetGapped.y + targetGapOffset.y },\n        target,\n    ];\n    return [pathPoints, centerX, centerY, defaultOffsetX, defaultOffsetY];\n}\nfunction getBend(a, b, c, size) {\n    const bendSize = Math.min(distance(a, b) / 2, distance(b, c) / 2, size);\n    const { x, y } = b;\n    // no bend\n    if ((a.x === x && x === c.x) || (a.y === y && y === c.y)) {\n        return `L${x} ${y}`;\n    }\n    // first segment is horizontal\n    if (a.y === y) {\n        const xDir = a.x < c.x ? -1 : 1;\n        const yDir = a.y < c.y ? 1 : -1;\n        return `L ${x + bendSize * xDir},${y}Q ${x},${y} ${x},${y + bendSize * yDir}`;\n    }\n    const xDir = a.x < c.x ? 1 : -1;\n    const yDir = a.y < c.y ? -1 : 1;\n    return `L ${x},${y + bendSize * yDir}Q ${x},${y} ${x + bendSize * xDir},${y}`;\n}\nfunction getSmoothStepPath({ sourceX, sourceY, sourcePosition = Position.Bottom, targetX, targetY, targetPosition = Position.Top, borderRadius = 5, centerX, centerY, offset = 20, }) {\n    const [points, labelX, labelY, offsetX, offsetY] = getPoints({\n        source: { x: sourceX, y: sourceY },\n        sourcePosition,\n        target: { x: targetX, y: targetY },\n        targetPosition,\n        center: { x: centerX, y: centerY },\n        offset,\n    });\n    const path = points.reduce((res, p, i) => {\n        let segment = '';\n        if (i > 0 && i < points.length - 1) {\n            segment = getBend(points[i - 1], p, points[i + 1], borderRadius);\n        }\n        else {\n            segment = `${i === 0 ? 'M' : 'L'}${p.x} ${p.y}`;\n        }\n        res += segment;\n        return res;\n    }, '');\n    return [path, labelX, labelY, offsetX, offsetY];\n}\nconst SmoothStepEdge = memo(({ sourceX, sourceY, targetX, targetY, label, labelStyle, labelShowBg, labelBgStyle, labelBgPadding, labelBgBorderRadius, style, sourcePosition = Position.Bottom, targetPosition = Position.Top, markerEnd, markerStart, pathOptions, interactionWidth, }) => {\n    const [path, labelX, labelY] = getSmoothStepPath({\n        sourceX,\n        sourceY,\n        sourcePosition,\n        targetX,\n        targetY,\n        targetPosition,\n        borderRadius: pathOptions?.borderRadius,\n        offset: pathOptions?.offset,\n    });\n    return (React.createElement(BaseEdge, { path: path, labelX: labelX, labelY: labelY, label: label, labelStyle: labelStyle, labelShowBg: labelShowBg, labelBgStyle: labelBgStyle, labelBgPadding: labelBgPadding, labelBgBorderRadius: labelBgBorderRadius, style: style, markerEnd: markerEnd, markerStart: markerStart, interactionWidth: interactionWidth }));\n});\nSmoothStepEdge.displayName = 'SmoothStepEdge';\n\nconst StepEdge = memo((props) => (React.createElement(SmoothStepEdge, { ...props, pathOptions: useMemo(() => ({ borderRadius: 0, offset: props.pathOptions?.offset }), [props.pathOptions?.offset]) })));\nStepEdge.displayName = 'StepEdge';\n\nfunction getStraightPath({ sourceX, sourceY, targetX, targetY, }) {\n    const [labelX, labelY, offsetX, offsetY] = getEdgeCenter({\n        sourceX,\n        sourceY,\n        targetX,\n        targetY,\n    });\n    return [`M ${sourceX},${sourceY}L ${targetX},${targetY}`, labelX, labelY, offsetX, offsetY];\n}\nconst StraightEdge = memo(({ sourceX, sourceY, targetX, targetY, label, labelStyle, labelShowBg, labelBgStyle, labelBgPadding, labelBgBorderRadius, style, markerEnd, markerStart, interactionWidth, }) => {\n    const [path, labelX, labelY] = getStraightPath({ sourceX, sourceY, targetX, targetY });\n    return (React.createElement(BaseEdge, { path: path, labelX: labelX, labelY: labelY, label: label, labelStyle: labelStyle, labelShowBg: labelShowBg, labelBgStyle: labelBgStyle, labelBgPadding: labelBgPadding, labelBgBorderRadius: labelBgBorderRadius, style: style, markerEnd: markerEnd, markerStart: markerStart, interactionWidth: interactionWidth }));\n});\nStraightEdge.displayName = 'StraightEdge';\n\nfunction calculateControlOffset(distance, curvature) {\n    if (distance >= 0) {\n        return 0.5 * distance;\n    }\n    return curvature * 25 * Math.sqrt(-distance);\n}\nfunction getControlWithCurvature({ pos, x1, y1, x2, y2, c }) {\n    switch (pos) {\n        case Position.Left:\n            return [x1 - calculateControlOffset(x1 - x2, c), y1];\n        case Position.Right:\n            return [x1 + calculateControlOffset(x2 - x1, c), y1];\n        case Position.Top:\n            return [x1, y1 - calculateControlOffset(y1 - y2, c)];\n        case Position.Bottom:\n            return [x1, y1 + calculateControlOffset(y2 - y1, c)];\n    }\n}\nfunction getBezierPath({ sourceX, sourceY, sourcePosition = Position.Bottom, targetX, targetY, targetPosition = Position.Top, curvature = 0.25, }) {\n    const [sourceControlX, sourceControlY] = getControlWithCurvature({\n        pos: sourcePosition,\n        x1: sourceX,\n        y1: sourceY,\n        x2: targetX,\n        y2: targetY,\n        c: curvature,\n    });\n    const [targetControlX, targetControlY] = getControlWithCurvature({\n        pos: targetPosition,\n        x1: targetX,\n        y1: targetY,\n        x2: sourceX,\n        y2: sourceY,\n        c: curvature,\n    });\n    const [labelX, labelY, offsetX, offsetY] = getBezierEdgeCenter({\n        sourceX,\n        sourceY,\n        targetX,\n        targetY,\n        sourceControlX,\n        sourceControlY,\n        targetControlX,\n        targetControlY,\n    });\n    return [\n        `M${sourceX},${sourceY} C${sourceControlX},${sourceControlY} ${targetControlX},${targetControlY} ${targetX},${targetY}`,\n        labelX,\n        labelY,\n        offsetX,\n        offsetY,\n    ];\n}\nconst BezierEdge = memo(({ sourceX, sourceY, targetX, targetY, sourcePosition = Position.Bottom, targetPosition = Position.Top, label, labelStyle, labelShowBg, labelBgStyle, labelBgPadding, labelBgBorderRadius, style, markerEnd, markerStart, pathOptions, interactionWidth, }) => {\n    const [path, labelX, labelY] = getBezierPath({\n        sourceX,\n        sourceY,\n        sourcePosition,\n        targetX,\n        targetY,\n        targetPosition,\n        curvature: pathOptions?.curvature,\n    });\n    return (React.createElement(BaseEdge, { path: path, labelX: labelX, labelY: labelY, label: label, labelStyle: labelStyle, labelShowBg: labelShowBg, labelBgStyle: labelBgStyle, labelBgPadding: labelBgPadding, labelBgBorderRadius: labelBgBorderRadius, style: style, markerEnd: markerEnd, markerStart: markerStart, interactionWidth: interactionWidth }));\n});\nBezierEdge.displayName = 'BezierEdge';\n\nconst NodeIdContext = createContext(null);\nconst Provider = NodeIdContext.Provider;\nNodeIdContext.Consumer;\nconst useNodeId = () => {\n    const nodeId = useContext(NodeIdContext);\n    return nodeId;\n};\n\nconst isEdge = (element) => 'id' in element && 'source' in element && 'target' in element;\nconst isNode = (element) => 'id' in element && !('source' in element) && !('target' in element);\nconst getOutgoers = (node, nodes, edges) => {\n    if (!isNode(node)) {\n        return [];\n    }\n    const outgoerIds = edges.filter((e) => e.source === node.id).map((e) => e.target);\n    return nodes.filter((n) => outgoerIds.includes(n.id));\n};\nconst getIncomers = (node, nodes, edges) => {\n    if (!isNode(node)) {\n        return [];\n    }\n    const incomersIds = edges.filter((e) => e.target === node.id).map((e) => e.source);\n    return nodes.filter((n) => incomersIds.includes(n.id));\n};\nconst getEdgeId = ({ source, sourceHandle, target, targetHandle }) => `reactflow__edge-${source}${sourceHandle || ''}-${target}${targetHandle || ''}`;\nconst getMarkerId = (marker, rfId) => {\n    if (typeof marker === 'undefined') {\n        return '';\n    }\n    if (typeof marker === 'string') {\n        return marker;\n    }\n    const idPrefix = rfId ? `${rfId}__` : '';\n    return `${idPrefix}${Object.keys(marker)\n        .sort()\n        .map((key) => `${key}=${marker[key]}`)\n        .join('&')}`;\n};\nconst connectionExists = (edge, edges) => {\n    return edges.some((el) => el.source === edge.source &&\n        el.target === edge.target &&\n        (el.sourceHandle === edge.sourceHandle || (!el.sourceHandle && !edge.sourceHandle)) &&\n        (el.targetHandle === edge.targetHandle || (!el.targetHandle && !edge.targetHandle)));\n};\nconst addEdge = (edgeParams, edges) => {\n    if (!edgeParams.source || !edgeParams.target) {\n        devWarn('006', errorMessages['error006']());\n        return edges;\n    }\n    let edge;\n    if (isEdge(edgeParams)) {\n        edge = { ...edgeParams };\n    }\n    else {\n        edge = {\n            ...edgeParams,\n            id: getEdgeId(edgeParams),\n        };\n    }\n    if (connectionExists(edge, edges)) {\n        return edges;\n    }\n    return edges.concat(edge);\n};\nconst reconnectEdge = (oldEdge, newConnection, edges, options = { shouldReplaceId: true }) => {\n    const { id: oldEdgeId, ...rest } = oldEdge;\n    if (!newConnection.source || !newConnection.target) {\n        devWarn('006', errorMessages['error006']());\n        return edges;\n    }\n    const foundEdge = edges.find((e) => e.id === oldEdgeId);\n    if (!foundEdge) {\n        devWarn('007', errorMessages['error007'](oldEdgeId));\n        return edges;\n    }\n    // Remove old edge and create the new edge with parameters of old edge.\n    const edge = {\n        ...rest,\n        id: options.shouldReplaceId ? getEdgeId(newConnection) : oldEdgeId,\n        source: newConnection.source,\n        target: newConnection.target,\n        sourceHandle: newConnection.sourceHandle,\n        targetHandle: newConnection.targetHandle,\n    };\n    return edges.filter((e) => e.id !== oldEdgeId).concat(edge);\n};\n/**\n *\n * @deprecated Use `reconnectEdge` instead.\n */\nconst updateEdge = (oldEdge, newConnection, edges, options = { shouldReplaceId: true }) => {\n    console.warn('[DEPRECATED] `updateEdge` is deprecated. Instead use `reconnectEdge` https://reactflow.dev/api-reference/utils/reconnect-edge');\n    return reconnectEdge(oldEdge, newConnection, edges, options);\n};\nconst pointToRendererPoint = ({ x, y }, [tx, ty, tScale], snapToGrid, [snapX, snapY]) => {\n    const position = {\n        x: (x - tx) / tScale,\n        y: (y - ty) / tScale,\n    };\n    if (snapToGrid) {\n        return {\n            x: snapX * Math.round(position.x / snapX),\n            y: snapY * Math.round(position.y / snapY),\n        };\n    }\n    return position;\n};\nconst rendererPointToPoint = ({ x, y }, [tx, ty, tScale]) => {\n    return {\n        x: x * tScale + tx,\n        y: y * tScale + ty,\n    };\n};\nconst getNodePositionWithOrigin = (node, nodeOrigin = [0, 0]) => {\n    if (!node) {\n        return {\n            x: 0,\n            y: 0,\n            positionAbsolute: {\n                x: 0,\n                y: 0,\n            },\n        };\n    }\n    const offsetX = (node.width ?? 0) * nodeOrigin[0];\n    const offsetY = (node.height ?? 0) * nodeOrigin[1];\n    const position = {\n        x: node.position.x - offsetX,\n        y: node.position.y - offsetY,\n    };\n    return {\n        ...position,\n        positionAbsolute: node.positionAbsolute\n            ? {\n                x: node.positionAbsolute.x - offsetX,\n                y: node.positionAbsolute.y - offsetY,\n            }\n            : position,\n    };\n};\nconst getNodesBounds = (nodes, nodeOrigin = [0, 0]) => {\n    if (nodes.length === 0) {\n        return { x: 0, y: 0, width: 0, height: 0 };\n    }\n    const box = nodes.reduce((currBox, node) => {\n        const { x, y } = getNodePositionWithOrigin(node, nodeOrigin).positionAbsolute;\n        return getBoundsOfBoxes(currBox, rectToBox({\n            x,\n            y,\n            width: node.width || 0,\n            height: node.height || 0,\n        }));\n    }, { x: Infinity, y: Infinity, x2: -Infinity, y2: -Infinity });\n    return boxToRect(box);\n};\n// @deprecated Use `getNodesBounds`.\nconst getRectOfNodes = (nodes, nodeOrigin = [0, 0]) => {\n    console.warn('[DEPRECATED] `getRectOfNodes` is deprecated. Instead use `getNodesBounds` https://reactflow.dev/api-reference/utils/get-nodes-bounds.');\n    return getNodesBounds(nodes, nodeOrigin);\n};\nconst getNodesInside = (nodeInternals, rect, [tx, ty, tScale] = [0, 0, 1], partially = false, \n// set excludeNonSelectableNodes if you want to pay attention to the nodes \"selectable\" attribute\nexcludeNonSelectableNodes = false, nodeOrigin = [0, 0]) => {\n    const paneRect = {\n        x: (rect.x - tx) / tScale,\n        y: (rect.y - ty) / tScale,\n        width: rect.width / tScale,\n        height: rect.height / tScale,\n    };\n    const visibleNodes = [];\n    nodeInternals.forEach((node) => {\n        const { width, height, selectable = true, hidden = false } = node;\n        if ((excludeNonSelectableNodes && !selectable) || hidden) {\n            return false;\n        }\n        const { positionAbsolute } = getNodePositionWithOrigin(node, nodeOrigin);\n        const nodeRect = {\n            x: positionAbsolute.x,\n            y: positionAbsolute.y,\n            width: width || 0,\n            height: height || 0,\n        };\n        const overlappingArea = getOverlappingArea(paneRect, nodeRect);\n        const notInitialized = typeof width === 'undefined' || typeof height === 'undefined' || width === null || height === null;\n        const partiallyVisible = partially && overlappingArea > 0;\n        const area = (width || 0) * (height || 0);\n        const isVisible = notInitialized || partiallyVisible || overlappingArea >= area;\n        if (isVisible || node.dragging) {\n            visibleNodes.push(node);\n        }\n    });\n    return visibleNodes;\n};\nconst getConnectedEdges = (nodes, edges) => {\n    const nodeIds = nodes.map((node) => node.id);\n    return edges.filter((edge) => nodeIds.includes(edge.source) || nodeIds.includes(edge.target));\n};\n// @deprecated Use `getViewportForBounds`.\nconst getTransformForBounds = (bounds, width, height, minZoom, maxZoom, padding = 0.1) => {\n    const { x, y, zoom } = getViewportForBounds(bounds, width, height, minZoom, maxZoom, padding);\n    console.warn('[DEPRECATED] `getTransformForBounds` is deprecated. Instead use `getViewportForBounds`. Beware that the return value is type Viewport (`{ x: number, y: number, zoom: number }`) instead of Transform (`[number, number, number]`). https://reactflow.dev/api-reference/utils/get-viewport-for-bounds');\n    return [x, y, zoom];\n};\nconst getViewportForBounds = (bounds, width, height, minZoom, maxZoom, padding = 0.1) => {\n    const xZoom = width / (bounds.width * (1 + padding));\n    const yZoom = height / (bounds.height * (1 + padding));\n    const zoom = Math.min(xZoom, yZoom);\n    const clampedZoom = clamp(zoom, minZoom, maxZoom);\n    const boundsCenterX = bounds.x + bounds.width / 2;\n    const boundsCenterY = bounds.y + bounds.height / 2;\n    const x = width / 2 - boundsCenterX * clampedZoom;\n    const y = height / 2 - boundsCenterY * clampedZoom;\n    return { x, y, zoom: clampedZoom };\n};\nconst getD3Transition = (selection, duration = 0) => {\n    return selection.transition().duration(duration);\n};\n\n// this functions collects all handles and adds an absolute position\n// so that we can later find the closest handle to the mouse position\nfunction getHandles(node, handleBounds, type, currentHandle) {\n    return (handleBounds[type] || []).reduce((res, h) => {\n        if (`${node.id}-${h.id}-${type}` !== currentHandle) {\n            res.push({\n                id: h.id || null,\n                type,\n                nodeId: node.id,\n                x: (node.positionAbsolute?.x ?? 0) + h.x + h.width / 2,\n                y: (node.positionAbsolute?.y ?? 0) + h.y + h.height / 2,\n            });\n        }\n        return res;\n    }, []);\n}\nfunction getClosestHandle(event, doc, pos, connectionRadius, handles, validator) {\n    // we always want to prioritize the handle below the mouse cursor over the closest distance handle,\n    // because it could be that the center of another handle is closer to the mouse pointer than the handle below the cursor\n    const { x, y } = getEventPosition(event);\n    const domNodes = doc.elementsFromPoint(x, y);\n    const handleBelow = domNodes.find((el) => el.classList.contains('react-flow__handle'));\n    if (handleBelow) {\n        const handleNodeId = handleBelow.getAttribute('data-nodeid');\n        if (handleNodeId) {\n            const handleType = getHandleType(undefined, handleBelow);\n            const handleId = handleBelow.getAttribute('data-handleid');\n            const validHandleResult = validator({ nodeId: handleNodeId, id: handleId, type: handleType });\n            if (validHandleResult) {\n                const handle = handles.find((h) => h.nodeId === handleNodeId && h.type === handleType && h.id === handleId);\n                return {\n                    handle: {\n                        id: handleId,\n                        type: handleType,\n                        nodeId: handleNodeId,\n                        x: handle?.x || pos.x,\n                        y: handle?.y || pos.y,\n                    },\n                    validHandleResult,\n                };\n            }\n        }\n    }\n    // if we couldn't find a handle below the mouse cursor we look for the closest distance based on the connectionRadius\n    let closestHandles = [];\n    let minDistance = Infinity;\n    handles.forEach((handle) => {\n        const distance = Math.sqrt((handle.x - pos.x) ** 2 + (handle.y - pos.y) ** 2);\n        if (distance <= connectionRadius) {\n            const validHandleResult = validator(handle);\n            if (distance <= minDistance) {\n                if (distance < minDistance) {\n                    closestHandles = [{ handle, validHandleResult }];\n                }\n                else if (distance === minDistance) {\n                    // when multiple handles are on the same distance we collect all of them\n                    closestHandles.push({\n                        handle,\n                        validHandleResult,\n                    });\n                }\n                minDistance = distance;\n            }\n        }\n    });\n    if (!closestHandles.length) {\n        return { handle: null, validHandleResult: defaultResult() };\n    }\n    if (closestHandles.length === 1) {\n        return closestHandles[0];\n    }\n    const hasValidHandle = closestHandles.some(({ validHandleResult }) => validHandleResult.isValid);\n    const hasTargetHandle = closestHandles.some(({ handle }) => handle.type === 'target');\n    // if multiple handles are layouted on top of each other we prefer the one with type = target and the one that is valid\n    return (closestHandles.find(({ handle, validHandleResult }) => hasTargetHandle ? handle.type === 'target' : (hasValidHandle ? validHandleResult.isValid : true)) || closestHandles[0]);\n}\nconst nullConnection = { source: null, target: null, sourceHandle: null, targetHandle: null };\nconst defaultResult = () => ({\n    handleDomNode: null,\n    isValid: false,\n    connection: nullConnection,\n    endHandle: null,\n});\n// checks if  and returns connection in fom of an object { source: 123, target: 312 }\nfunction isValidHandle(handle, connectionMode, fromNodeId, fromHandleId, fromType, isValidConnection, doc) {\n    const isTarget = fromType === 'target';\n    const handleToCheck = doc.querySelector(`.react-flow__handle[data-id=\"${handle?.nodeId}-${handle?.id}-${handle?.type}\"]`);\n    const result = {\n        ...defaultResult(),\n        handleDomNode: handleToCheck,\n    };\n    if (handleToCheck) {\n        const handleType = getHandleType(undefined, handleToCheck);\n        const handleNodeId = handleToCheck.getAttribute('data-nodeid');\n        const handleId = handleToCheck.getAttribute('data-handleid');\n        const connectable = handleToCheck.classList.contains('connectable');\n        const connectableEnd = handleToCheck.classList.contains('connectableend');\n        const connection = {\n            source: isTarget ? handleNodeId : fromNodeId,\n            sourceHandle: isTarget ? handleId : fromHandleId,\n            target: isTarget ? fromNodeId : handleNodeId,\n            targetHandle: isTarget ? fromHandleId : handleId,\n        };\n        result.connection = connection;\n        const isConnectable = connectable && connectableEnd;\n        // in strict mode we don't allow target to target or source to source connections\n        const isValid = isConnectable &&\n            (connectionMode === ConnectionMode.Strict\n                ? (isTarget && handleType === 'source') || (!isTarget && handleType === 'target')\n                : handleNodeId !== fromNodeId || handleId !== fromHandleId);\n        if (isValid) {\n            result.endHandle = {\n                nodeId: handleNodeId,\n                handleId,\n                type: handleType,\n            };\n            result.isValid = isValidConnection(connection);\n        }\n    }\n    return result;\n}\nfunction getHandleLookup({ nodes, nodeId, handleId, handleType }) {\n    return nodes.reduce((res, node) => {\n        if (node[internalsSymbol]) {\n            const { handleBounds } = node[internalsSymbol];\n            let sourceHandles = [];\n            let targetHandles = [];\n            if (handleBounds) {\n                sourceHandles = getHandles(node, handleBounds, 'source', `${nodeId}-${handleId}-${handleType}`);\n                targetHandles = getHandles(node, handleBounds, 'target', `${nodeId}-${handleId}-${handleType}`);\n            }\n            res.push(...sourceHandles, ...targetHandles);\n        }\n        return res;\n    }, []);\n}\nfunction getHandleType(edgeUpdaterType, handleDomNode) {\n    if (edgeUpdaterType) {\n        return edgeUpdaterType;\n    }\n    else if (handleDomNode?.classList.contains('target')) {\n        return 'target';\n    }\n    else if (handleDomNode?.classList.contains('source')) {\n        return 'source';\n    }\n    return null;\n}\nfunction resetRecentHandle(handleDomNode) {\n    handleDomNode?.classList.remove('valid', 'connecting', 'react-flow__handle-valid', 'react-flow__handle-connecting');\n}\nfunction getConnectionStatus(isInsideConnectionRadius, isHandleValid) {\n    let connectionStatus = null;\n    if (isHandleValid) {\n        connectionStatus = 'valid';\n    }\n    else if (isInsideConnectionRadius && !isHandleValid) {\n        connectionStatus = 'invalid';\n    }\n    return connectionStatus;\n}\n\nfunction handlePointerDown({ event, handleId, nodeId, onConnect, isTarget, getState, setState, isValidConnection, edgeUpdaterType, onReconnectEnd, }) {\n    // when react-flow is used inside a shadow root we can't use document\n    const doc = getHostForElement(event.target);\n    const { connectionMode, domNode, autoPanOnConnect, connectionRadius, onConnectStart, panBy, getNodes, cancelConnection, } = getState();\n    let autoPanId = 0;\n    let closestHandle;\n    const { x, y } = getEventPosition(event);\n    const clickedHandle = doc?.elementFromPoint(x, y);\n    const handleType = getHandleType(edgeUpdaterType, clickedHandle);\n    const containerBounds = domNode?.getBoundingClientRect();\n    if (!containerBounds || !handleType) {\n        return;\n    }\n    let prevActiveHandle;\n    let connectionPosition = getEventPosition(event, containerBounds);\n    let autoPanStarted = false;\n    let connection = null;\n    let isValid = false;\n    let handleDomNode = null;\n    const handleLookup = getHandleLookup({\n        nodes: getNodes(),\n        nodeId,\n        handleId,\n        handleType,\n    });\n    // when the user is moving the mouse close to the edge of the canvas while connecting we move the canvas\n    const autoPan = () => {\n        if (!autoPanOnConnect) {\n            return;\n        }\n        const [xMovement, yMovement] = calcAutoPan(connectionPosition, containerBounds);\n        panBy({ x: xMovement, y: yMovement });\n        autoPanId = requestAnimationFrame(autoPan);\n    };\n    setState({\n        connectionPosition,\n        connectionStatus: null,\n        // connectionNodeId etc will be removed in the next major in favor of connectionStartHandle\n        connectionNodeId: nodeId,\n        connectionHandleId: handleId,\n        connectionHandleType: handleType,\n        connectionStartHandle: {\n            nodeId,\n            handleId,\n            type: handleType,\n        },\n        connectionEndHandle: null,\n    });\n    onConnectStart?.(event, { nodeId, handleId, handleType });\n    function onPointerMove(event) {\n        const { transform } = getState();\n        connectionPosition = getEventPosition(event, containerBounds);\n        const { handle, validHandleResult } = getClosestHandle(event, doc, pointToRendererPoint(connectionPosition, transform, false, [1, 1]), connectionRadius, handleLookup, (handle) => isValidHandle(handle, connectionMode, nodeId, handleId, isTarget ? 'target' : 'source', isValidConnection, doc));\n        closestHandle = handle;\n        if (!autoPanStarted) {\n            autoPan();\n            autoPanStarted = true;\n        }\n        handleDomNode = validHandleResult.handleDomNode;\n        connection = validHandleResult.connection;\n        isValid = validHandleResult.isValid;\n        setState({\n            connectionPosition: closestHandle && isValid\n                ? rendererPointToPoint({\n                    x: closestHandle.x,\n                    y: closestHandle.y,\n                }, transform)\n                : connectionPosition,\n            connectionStatus: getConnectionStatus(!!closestHandle, isValid),\n            connectionEndHandle: validHandleResult.endHandle,\n        });\n        if (!closestHandle && !isValid && !handleDomNode) {\n            return resetRecentHandle(prevActiveHandle);\n        }\n        if (connection.source !== connection.target && handleDomNode) {\n            resetRecentHandle(prevActiveHandle);\n            prevActiveHandle = handleDomNode;\n            // @todo: remove the old class names \"react-flow__handle-\" in the next major version\n            handleDomNode.classList.add('connecting', 'react-flow__handle-connecting');\n            handleDomNode.classList.toggle('valid', isValid);\n            handleDomNode.classList.toggle('react-flow__handle-valid', isValid);\n        }\n    }\n    function onPointerUp(event) {\n        if ((closestHandle || handleDomNode) && connection && isValid) {\n            onConnect?.(connection);\n        }\n        // it's important to get a fresh reference from the store here\n        // in order to get the latest state of onConnectEnd\n        getState().onConnectEnd?.(event);\n        if (edgeUpdaterType) {\n            onReconnectEnd?.(event);\n        }\n        resetRecentHandle(prevActiveHandle);\n        cancelConnection();\n        cancelAnimationFrame(autoPanId);\n        autoPanStarted = false;\n        isValid = false;\n        connection = null;\n        handleDomNode = null;\n        doc.removeEventListener('mousemove', onPointerMove);\n        doc.removeEventListener('mouseup', onPointerUp);\n        doc.removeEventListener('touchmove', onPointerMove);\n        doc.removeEventListener('touchend', onPointerUp);\n    }\n    doc.addEventListener('mousemove', onPointerMove);\n    doc.addEventListener('mouseup', onPointerUp);\n    doc.addEventListener('touchmove', onPointerMove);\n    doc.addEventListener('touchend', onPointerUp);\n}\n\nconst alwaysValid = () => true;\nconst selector$f = (s) => ({\n    connectionStartHandle: s.connectionStartHandle,\n    connectOnClick: s.connectOnClick,\n    noPanClassName: s.noPanClassName,\n});\nconst connectingSelector = (nodeId, handleId, type) => (state) => {\n    const { connectionStartHandle: startHandle, connectionEndHandle: endHandle, connectionClickStartHandle: clickHandle, } = state;\n    return {\n        connecting: (startHandle?.nodeId === nodeId && startHandle?.handleId === handleId && startHandle?.type === type) ||\n            (endHandle?.nodeId === nodeId && endHandle?.handleId === handleId && endHandle?.type === type),\n        clickConnecting: clickHandle?.nodeId === nodeId && clickHandle?.handleId === handleId && clickHandle?.type === type,\n    };\n};\nconst Handle = forwardRef(({ type = 'source', position = Position.Top, isValidConnection, isConnectable = true, isConnectableStart = true, isConnectableEnd = true, id, onConnect, children, className, onMouseDown, onTouchStart, ...rest }, ref) => {\n    const handleId = id || null;\n    const isTarget = type === 'target';\n    const store = useStoreApi();\n    const nodeId = useNodeId();\n    const { connectOnClick, noPanClassName } = useStore(selector$f, shallow);\n    const { connecting, clickConnecting } = useStore(connectingSelector(nodeId, handleId, type), shallow);\n    if (!nodeId) {\n        store.getState().onError?.('010', errorMessages['error010']());\n    }\n    const onConnectExtended = (params) => {\n        const { defaultEdgeOptions, onConnect: onConnectAction, hasDefaultEdges } = store.getState();\n        const edgeParams = {\n            ...defaultEdgeOptions,\n            ...params,\n        };\n        if (hasDefaultEdges) {\n            const { edges, setEdges } = store.getState();\n            setEdges(addEdge(edgeParams, edges));\n        }\n        onConnectAction?.(edgeParams);\n        onConnect?.(edgeParams);\n    };\n    const onPointerDown = (event) => {\n        if (!nodeId) {\n            return;\n        }\n        const isMouseTriggered = isMouseEvent(event);\n        if (isConnectableStart && ((isMouseTriggered && event.button === 0) || !isMouseTriggered)) {\n            handlePointerDown({\n                event,\n                handleId,\n                nodeId,\n                onConnect: onConnectExtended,\n                isTarget,\n                getState: store.getState,\n                setState: store.setState,\n                isValidConnection: isValidConnection || store.getState().isValidConnection || alwaysValid,\n            });\n        }\n        if (isMouseTriggered) {\n            onMouseDown?.(event);\n        }\n        else {\n            onTouchStart?.(event);\n        }\n    };\n    const onClick = (event) => {\n        const { onClickConnectStart, onClickConnectEnd, connectionClickStartHandle, connectionMode, isValidConnection: isValidConnectionStore, } = store.getState();\n        if (!nodeId || (!connectionClickStartHandle && !isConnectableStart)) {\n            return;\n        }\n        if (!connectionClickStartHandle) {\n            onClickConnectStart?.(event, { nodeId, handleId, handleType: type });\n            store.setState({ connectionClickStartHandle: { nodeId, type, handleId } });\n            return;\n        }\n        const doc = getHostForElement(event.target);\n        const isValidConnectionHandler = isValidConnection || isValidConnectionStore || alwaysValid;\n        const { connection, isValid } = isValidHandle({\n            nodeId,\n            id: handleId,\n            type,\n        }, connectionMode, connectionClickStartHandle.nodeId, connectionClickStartHandle.handleId || null, connectionClickStartHandle.type, isValidConnectionHandler, doc);\n        if (isValid) {\n            onConnectExtended(connection);\n        }\n        onClickConnectEnd?.(event);\n        store.setState({ connectionClickStartHandle: null });\n    };\n    return (React.createElement(\"div\", { \"data-handleid\": handleId, \"data-nodeid\": nodeId, \"data-handlepos\": position, \"data-id\": `${nodeId}-${handleId}-${type}`, className: cc([\n            'react-flow__handle',\n            `react-flow__handle-${position}`,\n            'nodrag',\n            noPanClassName,\n            className,\n            {\n                source: !isTarget,\n                target: isTarget,\n                connectable: isConnectable,\n                connectablestart: isConnectableStart,\n                connectableend: isConnectableEnd,\n                connecting: clickConnecting,\n                // this class is used to style the handle when the user is connecting\n                connectionindicator: isConnectable && ((isConnectableStart && !connecting) || (isConnectableEnd && connecting)),\n            },\n        ]), onMouseDown: onPointerDown, onTouchStart: onPointerDown, onClick: connectOnClick ? onClick : undefined, ref: ref, ...rest }, children));\n});\nHandle.displayName = 'Handle';\nvar Handle$1 = memo(Handle);\n\nconst DefaultNode = ({ data, isConnectable, targetPosition = Position.Top, sourcePosition = Position.Bottom, }) => {\n    return (React.createElement(React.Fragment, null,\n        React.createElement(Handle$1, { type: \"target\", position: targetPosition, isConnectable: isConnectable }),\n        data?.label,\n        React.createElement(Handle$1, { type: \"source\", position: sourcePosition, isConnectable: isConnectable })));\n};\nDefaultNode.displayName = 'DefaultNode';\nvar DefaultNode$1 = memo(DefaultNode);\n\nconst InputNode = ({ data, isConnectable, sourcePosition = Position.Bottom }) => (React.createElement(React.Fragment, null,\n    data?.label,\n    React.createElement(Handle$1, { type: \"source\", position: sourcePosition, isConnectable: isConnectable })));\nInputNode.displayName = 'InputNode';\nvar InputNode$1 = memo(InputNode);\n\nconst OutputNode = ({ data, isConnectable, targetPosition = Position.Top }) => (React.createElement(React.Fragment, null,\n    React.createElement(Handle$1, { type: \"target\", position: targetPosition, isConnectable: isConnectable }),\n    data?.label));\nOutputNode.displayName = 'OutputNode';\nvar OutputNode$1 = memo(OutputNode);\n\nconst GroupNode = () => null;\nGroupNode.displayName = 'GroupNode';\n\nconst selector$e = (s) => ({\n    selectedNodes: s.getNodes().filter((n) => n.selected),\n    selectedEdges: s.edges.filter((e) => e.selected).map((e) => ({ ...e })),\n});\nconst selectId = (obj) => obj.id;\nfunction areEqual(a, b) {\n    return (shallow(a.selectedNodes.map(selectId), b.selectedNodes.map(selectId)) &&\n        shallow(a.selectedEdges.map(selectId), b.selectedEdges.map(selectId)));\n}\n// This is just a helper component for calling the onSelectionChange listener.\n// @TODO: Now that we have the onNodesChange and on EdgesChange listeners, do we still need this component?\nconst SelectionListener = memo(({ onSelectionChange }) => {\n    const store = useStoreApi();\n    const { selectedNodes, selectedEdges } = useStore(selector$e, areEqual);\n    useEffect(() => {\n        const params = { nodes: selectedNodes, edges: selectedEdges };\n        onSelectionChange?.(params);\n        store.getState().onSelectionChange.forEach((fn) => fn(params));\n    }, [selectedNodes, selectedEdges, onSelectionChange]);\n    return null;\n});\nSelectionListener.displayName = 'SelectionListener';\nconst changeSelector = (s) => !!s.onSelectionChange;\nfunction Wrapper$1({ onSelectionChange }) {\n    const storeHasSelectionChange = useStore(changeSelector);\n    if (onSelectionChange || storeHasSelectionChange) {\n        return React.createElement(SelectionListener, { onSelectionChange: onSelectionChange });\n    }\n    return null;\n}\n\nconst selector$d = (s) => ({\n    setNodes: s.setNodes,\n    setEdges: s.setEdges,\n    setDefaultNodesAndEdges: s.setDefaultNodesAndEdges,\n    setMinZoom: s.setMinZoom,\n    setMaxZoom: s.setMaxZoom,\n    setTranslateExtent: s.setTranslateExtent,\n    setNodeExtent: s.setNodeExtent,\n    reset: s.reset,\n});\nfunction useStoreUpdater(value, setStoreState) {\n    useEffect(() => {\n        if (typeof value !== 'undefined') {\n            setStoreState(value);\n        }\n    }, [value]);\n}\n// updates with values in store that don't have a dedicated setter function\nfunction useDirectStoreUpdater(key, value, setState) {\n    useEffect(() => {\n        if (typeof value !== 'undefined') {\n            setState({ [key]: value });\n        }\n    }, [value]);\n}\nconst StoreUpdater = ({ nodes, edges, defaultNodes, defaultEdges, onConnect, onConnectStart, onConnectEnd, onClickConnectStart, onClickConnectEnd, nodesDraggable, nodesConnectable, nodesFocusable, edgesFocusable, edgesUpdatable, elevateNodesOnSelect, minZoom, maxZoom, nodeExtent, onNodesChange, onEdgesChange, elementsSelectable, connectionMode, snapGrid, snapToGrid, translateExtent, connectOnClick, defaultEdgeOptions, fitView, fitViewOptions, onNodesDelete, onEdgesDelete, onNodeDrag, onNodeDragStart, onNodeDragStop, onSelectionDrag, onSelectionDragStart, onSelectionDragStop, noPanClassName, nodeOrigin, rfId, autoPanOnConnect, autoPanOnNodeDrag, onError, connectionRadius, isValidConnection, nodeDragThreshold, }) => {\n    const { setNodes, setEdges, setDefaultNodesAndEdges, setMinZoom, setMaxZoom, setTranslateExtent, setNodeExtent, reset, } = useStore(selector$d, shallow);\n    const store = useStoreApi();\n    useEffect(() => {\n        const edgesWithDefaults = defaultEdges?.map((e) => ({ ...e, ...defaultEdgeOptions }));\n        setDefaultNodesAndEdges(defaultNodes, edgesWithDefaults);\n        return () => {\n            reset();\n        };\n    }, []);\n    useDirectStoreUpdater('defaultEdgeOptions', defaultEdgeOptions, store.setState);\n    useDirectStoreUpdater('connectionMode', connectionMode, store.setState);\n    useDirectStoreUpdater('onConnect', onConnect, store.setState);\n    useDirectStoreUpdater('onConnectStart', onConnectStart, store.setState);\n    useDirectStoreUpdater('onConnectEnd', onConnectEnd, store.setState);\n    useDirectStoreUpdater('onClickConnectStart', onClickConnectStart, store.setState);\n    useDirectStoreUpdater('onClickConnectEnd', onClickConnectEnd, store.setState);\n    useDirectStoreUpdater('nodesDraggable', nodesDraggable, store.setState);\n    useDirectStoreUpdater('nodesConnectable', nodesConnectable, store.setState);\n    useDirectStoreUpdater('nodesFocusable', nodesFocusable, store.setState);\n    useDirectStoreUpdater('edgesFocusable', edgesFocusable, store.setState);\n    useDirectStoreUpdater('edgesUpdatable', edgesUpdatable, store.setState);\n    useDirectStoreUpdater('elementsSelectable', elementsSelectable, store.setState);\n    useDirectStoreUpdater('elevateNodesOnSelect', elevateNodesOnSelect, store.setState);\n    useDirectStoreUpdater('snapToGrid', snapToGrid, store.setState);\n    useDirectStoreUpdater('snapGrid', snapGrid, store.setState);\n    useDirectStoreUpdater('onNodesChange', onNodesChange, store.setState);\n    useDirectStoreUpdater('onEdgesChange', onEdgesChange, store.setState);\n    useDirectStoreUpdater('connectOnClick', connectOnClick, store.setState);\n    useDirectStoreUpdater('fitViewOnInit', fitView, store.setState);\n    useDirectStoreUpdater('fitViewOnInitOptions', fitViewOptions, store.setState);\n    useDirectStoreUpdater('onNodesDelete', onNodesDelete, store.setState);\n    useDirectStoreUpdater('onEdgesDelete', onEdgesDelete, store.setState);\n    useDirectStoreUpdater('onNodeDrag', onNodeDrag, store.setState);\n    useDirectStoreUpdater('onNodeDragStart', onNodeDragStart, store.setState);\n    useDirectStoreUpdater('onNodeDragStop', onNodeDragStop, store.setState);\n    useDirectStoreUpdater('onSelectionDrag', onSelectionDrag, store.setState);\n    useDirectStoreUpdater('onSelectionDragStart', onSelectionDragStart, store.setState);\n    useDirectStoreUpdater('onSelectionDragStop', onSelectionDragStop, store.setState);\n    useDirectStoreUpdater('noPanClassName', noPanClassName, store.setState);\n    useDirectStoreUpdater('nodeOrigin', nodeOrigin, store.setState);\n    useDirectStoreUpdater('rfId', rfId, store.setState);\n    useDirectStoreUpdater('autoPanOnConnect', autoPanOnConnect, store.setState);\n    useDirectStoreUpdater('autoPanOnNodeDrag', autoPanOnNodeDrag, store.setState);\n    useDirectStoreUpdater('onError', onError, store.setState);\n    useDirectStoreUpdater('connectionRadius', connectionRadius, store.setState);\n    useDirectStoreUpdater('isValidConnection', isValidConnection, store.setState);\n    useDirectStoreUpdater('nodeDragThreshold', nodeDragThreshold, store.setState);\n    useStoreUpdater(nodes, setNodes);\n    useStoreUpdater(edges, setEdges);\n    useStoreUpdater(minZoom, setMinZoom);\n    useStoreUpdater(maxZoom, setMaxZoom);\n    useStoreUpdater(translateExtent, setTranslateExtent);\n    useStoreUpdater(nodeExtent, setNodeExtent);\n    return null;\n};\n\nconst style = { display: 'none' };\nconst ariaLiveStyle = {\n    position: 'absolute',\n    width: 1,\n    height: 1,\n    margin: -1,\n    border: 0,\n    padding: 0,\n    overflow: 'hidden',\n    clip: 'rect(0px, 0px, 0px, 0px)',\n    clipPath: 'inset(100%)',\n};\nconst ARIA_NODE_DESC_KEY = 'react-flow__node-desc';\nconst ARIA_EDGE_DESC_KEY = 'react-flow__edge-desc';\nconst ARIA_LIVE_MESSAGE = 'react-flow__aria-live';\nconst selector$c = (s) => s.ariaLiveMessage;\nfunction AriaLiveMessage({ rfId }) {\n    const ariaLiveMessage = useStore(selector$c);\n    return (React.createElement(\"div\", { id: `${ARIA_LIVE_MESSAGE}-${rfId}`, \"aria-live\": \"assertive\", \"aria-atomic\": \"true\", style: ariaLiveStyle }, ariaLiveMessage));\n}\nfunction A11yDescriptions({ rfId, disableKeyboardA11y }) {\n    return (React.createElement(React.Fragment, null,\n        React.createElement(\"div\", { id: `${ARIA_NODE_DESC_KEY}-${rfId}`, style: style },\n            \"Press enter or space to select a node.\",\n            !disableKeyboardA11y && 'You can then use the arrow keys to move the node around.',\n            \" Press delete to remove it and escape to cancel.\",\n            ' '),\n        React.createElement(\"div\", { id: `${ARIA_EDGE_DESC_KEY}-${rfId}`, style: style }, \"Press enter or space to select an edge. You can then press delete to remove it or escape to cancel.\"),\n        !disableKeyboardA11y && React.createElement(AriaLiveMessage, { rfId: rfId })));\n}\n\n// the keycode can be a string 'a' or an array of strings ['a', 'a+d']\n// a string means a single key 'a' or a combination when '+' is used 'a+d'\n// an array means different possibilities. Explainer: ['a', 'd+s'] here the\n// user can use the single key 'a' or the combination 'd' + 's'\nvar useKeyPress = (keyCode = null, options = { actInsideInputWithModifier: true }) => {\n    const [keyPressed, setKeyPressed] = useState(false);\n    // we need to remember if a modifier key is pressed in order to track it\n    const modifierPressed = useRef(false);\n    // we need to remember the pressed keys in order to support combinations\n    const pressedKeys = useRef(new Set([]));\n    // keyCodes = array with single keys [['a']] or key combinations [['a', 's']]\n    // keysToWatch = array with all keys flattened ['a', 'd', 'ShiftLeft']\n    // used to check if we store event.code or event.key. When the code is in the list of keysToWatch\n    // we use the code otherwise the key. Explainer: When you press the left \"command\" key, the code is \"MetaLeft\"\n    // and the key is \"Meta\". We want users to be able to pass keys and codes so we assume that the key is meant when\n    // we can't find it in the list of keysToWatch.\n    const [keyCodes, keysToWatch] = useMemo(() => {\n        if (keyCode !== null) {\n            const keyCodeArr = Array.isArray(keyCode) ? keyCode : [keyCode];\n            const keys = keyCodeArr.filter((kc) => typeof kc === 'string').map((kc) => kc.split('+'));\n            const keysFlat = keys.reduce((res, item) => res.concat(...item), []);\n            return [keys, keysFlat];\n        }\n        return [[], []];\n    }, [keyCode]);\n    useEffect(() => {\n        const doc = typeof document !== 'undefined' ? document : null;\n        const target = options?.target || doc;\n        if (keyCode !== null) {\n            const downHandler = (event) => {\n                modifierPressed.current = event.ctrlKey || event.metaKey || event.shiftKey;\n                const preventAction = (!modifierPressed.current || (modifierPressed.current && !options.actInsideInputWithModifier)) &&\n                    isInputDOMNode(event);\n                if (preventAction) {\n                    return false;\n                }\n                const keyOrCode = useKeyOrCode(event.code, keysToWatch);\n                pressedKeys.current.add(event[keyOrCode]);\n                if (isMatchingKey(keyCodes, pressedKeys.current, false)) {\n                    event.preventDefault();\n                    setKeyPressed(true);\n                }\n            };\n            const upHandler = (event) => {\n                const preventAction = (!modifierPressed.current || (modifierPressed.current && !options.actInsideInputWithModifier)) &&\n                    isInputDOMNode(event);\n                if (preventAction) {\n                    return false;\n                }\n                const keyOrCode = useKeyOrCode(event.code, keysToWatch);\n                if (isMatchingKey(keyCodes, pressedKeys.current, true)) {\n                    setKeyPressed(false);\n                    pressedKeys.current.clear();\n                }\n                else {\n                    pressedKeys.current.delete(event[keyOrCode]);\n                }\n                // fix for Mac: when cmd key is pressed, keyup is not triggered for any other key, see: https://stackoverflow.com/questions/27380018/when-cmd-key-is-kept-pressed-keyup-is-not-triggered-for-any-other-key\n                if (event.key === 'Meta') {\n                    pressedKeys.current.clear();\n                }\n                modifierPressed.current = false;\n            };\n            const resetHandler = () => {\n                pressedKeys.current.clear();\n                setKeyPressed(false);\n            };\n            target?.addEventListener('keydown', downHandler);\n            target?.addEventListener('keyup', upHandler);\n            window.addEventListener('blur', resetHandler);\n            return () => {\n                target?.removeEventListener('keydown', downHandler);\n                target?.removeEventListener('keyup', upHandler);\n                window.removeEventListener('blur', resetHandler);\n            };\n        }\n    }, [keyCode, setKeyPressed]);\n    return keyPressed;\n};\n// utils\nfunction isMatchingKey(keyCodes, pressedKeys, isUp) {\n    return (keyCodes\n        // we only want to compare same sizes of keyCode definitions\n        // and pressed keys. When the user specified 'Meta' as a key somewhere\n        // this would also be truthy without this filter when user presses 'Meta' + 'r'\n        .filter((keys) => isUp || keys.length === pressedKeys.size)\n        // since we want to support multiple possibilities only one of the\n        // combinations need to be part of the pressed keys\n        .some((keys) => keys.every((k) => pressedKeys.has(k))));\n}\nfunction useKeyOrCode(eventCode, keysToWatch) {\n    return keysToWatch.includes(eventCode) ? 'code' : 'key';\n}\n\nfunction calculateXYZPosition(node, nodeInternals, result, nodeOrigin) {\n    const parentId = node.parentNode || node.parentId;\n    if (!parentId) {\n        return result;\n    }\n    const parentNode = nodeInternals.get(parentId);\n    const parentNodePosition = getNodePositionWithOrigin(parentNode, nodeOrigin);\n    return calculateXYZPosition(parentNode, nodeInternals, {\n        x: (result.x ?? 0) + parentNodePosition.x,\n        y: (result.y ?? 0) + parentNodePosition.y,\n        z: (parentNode[internalsSymbol]?.z ?? 0) > (result.z ?? 0) ? parentNode[internalsSymbol]?.z ?? 0 : result.z ?? 0,\n    }, nodeOrigin);\n}\nfunction updateAbsoluteNodePositions(nodeInternals, nodeOrigin, parentNodes) {\n    nodeInternals.forEach((node) => {\n        const parentId = node.parentNode || node.parentId;\n        if (parentId && !nodeInternals.has(parentId)) {\n            throw new Error(`Parent node ${parentId} not found`);\n        }\n        if (parentId || parentNodes?.[node.id]) {\n            const { x, y, z } = calculateXYZPosition(node, nodeInternals, {\n                ...node.position,\n                z: node[internalsSymbol]?.z ?? 0,\n            }, nodeOrigin);\n            node.positionAbsolute = {\n                x,\n                y,\n            };\n            node[internalsSymbol].z = z;\n            if (parentNodes?.[node.id]) {\n                node[internalsSymbol].isParent = true;\n            }\n        }\n    });\n}\nfunction createNodeInternals(nodes, nodeInternals, nodeOrigin, elevateNodesOnSelect) {\n    const nextNodeInternals = new Map();\n    const parentNodes = {};\n    const selectedNodeZ = elevateNodesOnSelect ? 1000 : 0;\n    nodes.forEach((node) => {\n        const z = (isNumeric(node.zIndex) ? node.zIndex : 0) + (node.selected ? selectedNodeZ : 0);\n        const currInternals = nodeInternals.get(node.id);\n        const internals = {\n            ...node,\n            positionAbsolute: {\n                x: node.position.x,\n                y: node.position.y,\n            },\n        };\n        const parentId = node.parentNode || node.parentId;\n        if (parentId) {\n            parentNodes[parentId] = true;\n        }\n        const resetHandleBounds = currInternals?.type && currInternals?.type !== node.type;\n        Object.defineProperty(internals, internalsSymbol, {\n            enumerable: false,\n            value: {\n                handleBounds: resetHandleBounds ? undefined : currInternals?.[internalsSymbol]?.handleBounds,\n                z,\n            },\n        });\n        nextNodeInternals.set(node.id, internals);\n    });\n    updateAbsoluteNodePositions(nextNodeInternals, nodeOrigin, parentNodes);\n    return nextNodeInternals;\n}\nfunction fitView(get, options = {}) {\n    const { getNodes, width, height, minZoom, maxZoom, d3Zoom, d3Selection, fitViewOnInitDone, fitViewOnInit, nodeOrigin, } = get();\n    const isInitialFitView = options.initial && !fitViewOnInitDone && fitViewOnInit;\n    const d3initialized = d3Zoom && d3Selection;\n    if (d3initialized && (isInitialFitView || !options.initial)) {\n        const nodes = getNodes().filter((n) => {\n            const isVisible = options.includeHiddenNodes ? n.width && n.height : !n.hidden;\n            if (options.nodes?.length) {\n                return isVisible && options.nodes.some((optionNode) => optionNode.id === n.id);\n            }\n            return isVisible;\n        });\n        const nodesInitialized = nodes.every((n) => n.width && n.height);\n        if (nodes.length > 0 && nodesInitialized) {\n            const bounds = getNodesBounds(nodes, nodeOrigin);\n            const { x, y, zoom } = getViewportForBounds(bounds, width, height, options.minZoom ?? minZoom, options.maxZoom ?? maxZoom, options.padding ?? 0.1);\n            const nextTransform = zoomIdentity.translate(x, y).scale(zoom);\n            if (typeof options.duration === 'number' && options.duration > 0) {\n                d3Zoom.transform(getD3Transition(d3Selection, options.duration), nextTransform);\n            }\n            else {\n                d3Zoom.transform(d3Selection, nextTransform);\n            }\n            return true;\n        }\n    }\n    return false;\n}\nfunction handleControlledNodeSelectionChange(nodeChanges, nodeInternals) {\n    nodeChanges.forEach((change) => {\n        const node = nodeInternals.get(change.id);\n        if (node) {\n            nodeInternals.set(node.id, {\n                ...node,\n                [internalsSymbol]: node[internalsSymbol],\n                selected: change.selected,\n            });\n        }\n    });\n    return new Map(nodeInternals);\n}\nfunction handleControlledEdgeSelectionChange(edgeChanges, edges) {\n    return edges.map((e) => {\n        const change = edgeChanges.find((change) => change.id === e.id);\n        if (change) {\n            e.selected = change.selected;\n        }\n        return e;\n    });\n}\nfunction updateNodesAndEdgesSelections({ changedNodes, changedEdges, get, set }) {\n    const { nodeInternals, edges, onNodesChange, onEdgesChange, hasDefaultNodes, hasDefaultEdges } = get();\n    if (changedNodes?.length) {\n        if (hasDefaultNodes) {\n            set({ nodeInternals: handleControlledNodeSelectionChange(changedNodes, nodeInternals) });\n        }\n        onNodesChange?.(changedNodes);\n    }\n    if (changedEdges?.length) {\n        if (hasDefaultEdges) {\n            set({ edges: handleControlledEdgeSelectionChange(changedEdges, edges) });\n        }\n        onEdgesChange?.(changedEdges);\n    }\n}\n\n// eslint-disable-next-line @typescript-eslint/no-empty-function\nconst noop = () => { };\nconst initialViewportHelper = {\n    zoomIn: noop,\n    zoomOut: noop,\n    zoomTo: noop,\n    getZoom: () => 1,\n    setViewport: noop,\n    getViewport: () => ({ x: 0, y: 0, zoom: 1 }),\n    fitView: () => false,\n    setCenter: noop,\n    fitBounds: noop,\n    project: (position) => position,\n    screenToFlowPosition: (position) => position,\n    flowToScreenPosition: (position) => position,\n    viewportInitialized: false,\n};\nconst selector$b = (s) => ({\n    d3Zoom: s.d3Zoom,\n    d3Selection: s.d3Selection,\n});\nconst useViewportHelper = () => {\n    const store = useStoreApi();\n    const { d3Zoom, d3Selection } = useStore(selector$b, shallow);\n    const viewportHelperFunctions = useMemo(() => {\n        if (d3Selection && d3Zoom) {\n            return {\n                zoomIn: (options) => d3Zoom.scaleBy(getD3Transition(d3Selection, options?.duration), 1.2),\n                zoomOut: (options) => d3Zoom.scaleBy(getD3Transition(d3Selection, options?.duration), 1 / 1.2),\n                zoomTo: (zoomLevel, options) => d3Zoom.scaleTo(getD3Transition(d3Selection, options?.duration), zoomLevel),\n                getZoom: () => store.getState().transform[2],\n                setViewport: (transform, options) => {\n                    const [x, y, zoom] = store.getState().transform;\n                    const nextTransform = zoomIdentity\n                        .translate(transform.x ?? x, transform.y ?? y)\n                        .scale(transform.zoom ?? zoom);\n                    d3Zoom.transform(getD3Transition(d3Selection, options?.duration), nextTransform);\n                },\n                getViewport: () => {\n                    const [x, y, zoom] = store.getState().transform;\n                    return { x, y, zoom };\n                },\n                fitView: (options) => fitView(store.getState, options),\n                setCenter: (x, y, options) => {\n                    const { width, height, maxZoom } = store.getState();\n                    const nextZoom = typeof options?.zoom !== 'undefined' ? options.zoom : maxZoom;\n                    const centerX = width / 2 - x * nextZoom;\n                    const centerY = height / 2 - y * nextZoom;\n                    const transform = zoomIdentity.translate(centerX, centerY).scale(nextZoom);\n                    d3Zoom.transform(getD3Transition(d3Selection, options?.duration), transform);\n                },\n                fitBounds: (bounds, options) => {\n                    const { width, height, minZoom, maxZoom } = store.getState();\n                    const { x, y, zoom } = getViewportForBounds(bounds, width, height, minZoom, maxZoom, options?.padding ?? 0.1);\n                    const transform = zoomIdentity.translate(x, y).scale(zoom);\n                    d3Zoom.transform(getD3Transition(d3Selection, options?.duration), transform);\n                },\n                // @deprecated Use `screenToFlowPosition`.\n                project: (position) => {\n                    const { transform, snapToGrid, snapGrid } = store.getState();\n                    console.warn('[DEPRECATED] `project` is deprecated. Instead use `screenToFlowPosition`. There is no need to subtract the react flow bounds anymore! https://reactflow.dev/api-reference/types/react-flow-instance#screen-to-flow-position');\n                    return pointToRendererPoint(position, transform, snapToGrid, snapGrid);\n                },\n                screenToFlowPosition: (position) => {\n                    const { transform, snapToGrid, snapGrid, domNode } = store.getState();\n                    if (!domNode) {\n                        return position;\n                    }\n                    const { x: domX, y: domY } = domNode.getBoundingClientRect();\n                    const relativePosition = {\n                        x: position.x - domX,\n                        y: position.y - domY,\n                    };\n                    return pointToRendererPoint(relativePosition, transform, snapToGrid, snapGrid);\n                },\n                flowToScreenPosition: (position) => {\n                    const { transform, domNode } = store.getState();\n                    if (!domNode) {\n                        return position;\n                    }\n                    const { x: domX, y: domY } = domNode.getBoundingClientRect();\n                    const rendererPosition = rendererPointToPoint(position, transform);\n                    return {\n                        x: rendererPosition.x + domX,\n                        y: rendererPosition.y + domY,\n                    };\n                },\n                viewportInitialized: true,\n            };\n        }\n        return initialViewportHelper;\n    }, [d3Zoom, d3Selection]);\n    return viewportHelperFunctions;\n};\n\n/* eslint-disable-next-line @typescript-eslint/no-explicit-any */\nfunction useReactFlow() {\n    const viewportHelper = useViewportHelper();\n    const store = useStoreApi();\n    const getNodes = useCallback(() => {\n        return store\n            .getState()\n            .getNodes()\n            .map((n) => ({ ...n }));\n    }, []);\n    const getNode = useCallback((id) => {\n        return store.getState().nodeInternals.get(id);\n    }, []);\n    const getEdges = useCallback(() => {\n        const { edges = [] } = store.getState();\n        return edges.map((e) => ({ ...e }));\n    }, []);\n    const getEdge = useCallback((id) => {\n        const { edges = [] } = store.getState();\n        return edges.find((e) => e.id === id);\n    }, []);\n    const setNodes = useCallback((payload) => {\n        const { getNodes, setNodes, hasDefaultNodes, onNodesChange } = store.getState();\n        const nodes = getNodes();\n        const nextNodes = typeof payload === 'function' ? payload(nodes) : payload;\n        if (hasDefaultNodes) {\n            setNodes(nextNodes);\n        }\n        else if (onNodesChange) {\n            const changes = nextNodes.length === 0\n                ? nodes.map((node) => ({ type: 'remove', id: node.id }))\n                : nextNodes.map((node) => ({ item: node, type: 'reset' }));\n            onNodesChange(changes);\n        }\n    }, []);\n    const setEdges = useCallback((payload) => {\n        const { edges = [], setEdges, hasDefaultEdges, onEdgesChange } = store.getState();\n        const nextEdges = typeof payload === 'function' ? payload(edges) : payload;\n        if (hasDefaultEdges) {\n            setEdges(nextEdges);\n        }\n        else if (onEdgesChange) {\n            const changes = nextEdges.length === 0\n                ? edges.map((edge) => ({ type: 'remove', id: edge.id }))\n                : nextEdges.map((edge) => ({ item: edge, type: 'reset' }));\n            onEdgesChange(changes);\n        }\n    }, []);\n    const addNodes = useCallback((payload) => {\n        const nodes = Array.isArray(payload) ? payload : [payload];\n        const { getNodes, setNodes, hasDefaultNodes, onNodesChange } = store.getState();\n        if (hasDefaultNodes) {\n            const currentNodes = getNodes();\n            const nextNodes = [...currentNodes, ...nodes];\n            setNodes(nextNodes);\n        }\n        else if (onNodesChange) {\n            const changes = nodes.map((node) => ({ item: node, type: 'add' }));\n            onNodesChange(changes);\n        }\n    }, []);\n    const addEdges = useCallback((payload) => {\n        const nextEdges = Array.isArray(payload) ? payload : [payload];\n        const { edges = [], setEdges, hasDefaultEdges, onEdgesChange } = store.getState();\n        if (hasDefaultEdges) {\n            setEdges([...edges, ...nextEdges]);\n        }\n        else if (onEdgesChange) {\n            const changes = nextEdges.map((edge) => ({ item: edge, type: 'add' }));\n            onEdgesChange(changes);\n        }\n    }, []);\n    const toObject = useCallback(() => {\n        const { getNodes, edges = [], transform } = store.getState();\n        const [x, y, zoom] = transform;\n        return {\n            nodes: getNodes().map((n) => ({ ...n })),\n            edges: edges.map((e) => ({ ...e })),\n            viewport: {\n                x,\n                y,\n                zoom,\n            },\n        };\n    }, []);\n    const deleteElements = useCallback(({ nodes: nodesDeleted, edges: edgesDeleted }) => {\n        const { nodeInternals, getNodes, edges, hasDefaultNodes, hasDefaultEdges, onNodesDelete, onEdgesDelete, onNodesChange, onEdgesChange, } = store.getState();\n        const nodeIds = (nodesDeleted || []).map((node) => node.id);\n        const edgeIds = (edgesDeleted || []).map((edge) => edge.id);\n        const nodesToRemove = getNodes().reduce((res, node) => {\n            const parentId = node.parentNode || node.parentId;\n            const parentHit = !nodeIds.includes(node.id) && parentId && res.find((n) => n.id === parentId);\n            const deletable = typeof node.deletable === 'boolean' ? node.deletable : true;\n            if (deletable && (nodeIds.includes(node.id) || parentHit)) {\n                res.push(node);\n            }\n            return res;\n        }, []);\n        const deletableEdges = edges.filter((e) => (typeof e.deletable === 'boolean' ? e.deletable : true));\n        const initialHitEdges = deletableEdges.filter((e) => edgeIds.includes(e.id));\n        if (nodesToRemove || initialHitEdges) {\n            const connectedEdges = getConnectedEdges(nodesToRemove, deletableEdges);\n            const edgesToRemove = [...initialHitEdges, ...connectedEdges];\n            const edgeIdsToRemove = edgesToRemove.reduce((res, edge) => {\n                if (!res.includes(edge.id)) {\n                    res.push(edge.id);\n                }\n                return res;\n            }, []);\n            if (hasDefaultEdges || hasDefaultNodes) {\n                if (hasDefaultEdges) {\n                    store.setState({\n                        edges: edges.filter((e) => !edgeIdsToRemove.includes(e.id)),\n                    });\n                }\n                if (hasDefaultNodes) {\n                    nodesToRemove.forEach((node) => {\n                        nodeInternals.delete(node.id);\n                    });\n                    store.setState({\n                        nodeInternals: new Map(nodeInternals),\n                    });\n                }\n            }\n            if (edgeIdsToRemove.length > 0) {\n                onEdgesDelete?.(edgesToRemove);\n                if (onEdgesChange) {\n                    onEdgesChange(edgeIdsToRemove.map((id) => ({\n                        id,\n                        type: 'remove',\n                    })));\n                }\n            }\n            if (nodesToRemove.length > 0) {\n                onNodesDelete?.(nodesToRemove);\n                if (onNodesChange) {\n                    const nodeChanges = nodesToRemove.map((n) => ({ id: n.id, type: 'remove' }));\n                    onNodesChange(nodeChanges);\n                }\n            }\n        }\n    }, []);\n    const getNodeRect = useCallback((nodeOrRect) => {\n        const isRect = isRectObject(nodeOrRect);\n        const node = isRect ? null : store.getState().nodeInternals.get(nodeOrRect.id);\n        if (!isRect && !node) {\n            return [null, null, isRect];\n        }\n        const nodeRect = isRect ? nodeOrRect : nodeToRect(node);\n        return [nodeRect, node, isRect];\n    }, []);\n    const getIntersectingNodes = useCallback((nodeOrRect, partially = true, nodes) => {\n        const [nodeRect, node, isRect] = getNodeRect(nodeOrRect);\n        if (!nodeRect) {\n            return [];\n        }\n        return (nodes || store.getState().getNodes()).filter((n) => {\n            if (!isRect && (n.id === node.id || !n.positionAbsolute)) {\n                return false;\n            }\n            const currNodeRect = nodeToRect(n);\n            const overlappingArea = getOverlappingArea(currNodeRect, nodeRect);\n            const partiallyVisible = partially && overlappingArea > 0;\n            return partiallyVisible || overlappingArea >= nodeRect.width * nodeRect.height;\n        });\n    }, []);\n    const isNodeIntersecting = useCallback((nodeOrRect, area, partially = true) => {\n        const [nodeRect] = getNodeRect(nodeOrRect);\n        if (!nodeRect) {\n            return false;\n        }\n        const overlappingArea = getOverlappingArea(nodeRect, area);\n        const partiallyVisible = partially && overlappingArea > 0;\n        return partiallyVisible || overlappingArea >= nodeRect.width * nodeRect.height;\n    }, []);\n    return useMemo(() => {\n        return {\n            ...viewportHelper,\n            getNodes,\n            getNode,\n            getEdges,\n            getEdge,\n            setNodes,\n            setEdges,\n            addNodes,\n            addEdges,\n            toObject,\n            deleteElements,\n            getIntersectingNodes,\n            isNodeIntersecting,\n        };\n    }, [\n        viewportHelper,\n        getNodes,\n        getNode,\n        getEdges,\n        getEdge,\n        setNodes,\n        setEdges,\n        addNodes,\n        addEdges,\n        toObject,\n        deleteElements,\n        getIntersectingNodes,\n        isNodeIntersecting,\n    ]);\n}\n\nconst deleteKeyOptions = { actInsideInputWithModifier: false };\nvar useGlobalKeyHandler = ({ deleteKeyCode, multiSelectionKeyCode }) => {\n    const store = useStoreApi();\n    const { deleteElements } = useReactFlow();\n    const deleteKeyPressed = useKeyPress(deleteKeyCode, deleteKeyOptions);\n    const multiSelectionKeyPressed = useKeyPress(multiSelectionKeyCode);\n    useEffect(() => {\n        if (deleteKeyPressed) {\n            const { edges, getNodes } = store.getState();\n            const selectedNodes = getNodes().filter((node) => node.selected);\n            const selectedEdges = edges.filter((edge) => edge.selected);\n            deleteElements({ nodes: selectedNodes, edges: selectedEdges });\n            store.setState({ nodesSelectionActive: false });\n        }\n    }, [deleteKeyPressed]);\n    useEffect(() => {\n        store.setState({ multiSelectionActive: multiSelectionKeyPressed });\n    }, [multiSelectionKeyPressed]);\n};\n\nfunction useResizeHandler(rendererNode) {\n    const store = useStoreApi();\n    useEffect(() => {\n        let resizeObserver;\n        const updateDimensions = () => {\n            if (!rendererNode.current) {\n                return;\n            }\n            const size = getDimensions(rendererNode.current);\n            if (size.height === 0 || size.width === 0) {\n                store.getState().onError?.('004', errorMessages['error004']());\n            }\n            store.setState({ width: size.width || 500, height: size.height || 500 });\n        };\n        updateDimensions();\n        window.addEventListener('resize', updateDimensions);\n        if (rendererNode.current) {\n            resizeObserver = new ResizeObserver(() => updateDimensions());\n            resizeObserver.observe(rendererNode.current);\n        }\n        return () => {\n            window.removeEventListener('resize', updateDimensions);\n            if (resizeObserver && rendererNode.current) {\n                resizeObserver.unobserve(rendererNode.current);\n            }\n        };\n    }, []);\n}\n\nconst containerStyle = {\n    position: 'absolute',\n    width: '100%',\n    height: '100%',\n    top: 0,\n    left: 0,\n};\n\n/* eslint-disable @typescript-eslint/ban-ts-comment */\nconst viewChanged = (prevViewport, eventTransform) => prevViewport.x !== eventTransform.x || prevViewport.y !== eventTransform.y || prevViewport.zoom !== eventTransform.k;\nconst eventToFlowTransform = (eventTransform) => ({\n    x: eventTransform.x,\n    y: eventTransform.y,\n    zoom: eventTransform.k,\n});\nconst isWrappedWithClass = (event, className) => event.target.closest(`.${className}`);\nconst isRightClickPan = (panOnDrag, usedButton) => usedButton === 2 && Array.isArray(panOnDrag) && panOnDrag.includes(2);\nconst wheelDelta = (event) => {\n    const factor = event.ctrlKey && isMacOs() ? 10 : 1;\n    return -event.deltaY * (event.deltaMode === 1 ? 0.05 : event.deltaMode ? 1 : 0.002) * factor;\n};\nconst selector$a = (s) => ({\n    d3Zoom: s.d3Zoom,\n    d3Selection: s.d3Selection,\n    d3ZoomHandler: s.d3ZoomHandler,\n    userSelectionActive: s.userSelectionActive,\n});\nconst ZoomPane = ({ onMove, onMoveStart, onMoveEnd, onPaneContextMenu, zoomOnScroll = true, zoomOnPinch = true, panOnScroll = false, panOnScrollSpeed = 0.5, panOnScrollMode = PanOnScrollMode.Free, zoomOnDoubleClick = true, elementsSelectable, panOnDrag = true, defaultViewport, translateExtent, minZoom, maxZoom, zoomActivationKeyCode, preventScrolling = true, children, noWheelClassName, noPanClassName, }) => {\n    const timerId = useRef();\n    const store = useStoreApi();\n    const isZoomingOrPanning = useRef(false);\n    const zoomedWithRightMouseButton = useRef(false);\n    const zoomPane = useRef(null);\n    const prevTransform = useRef({ x: 0, y: 0, zoom: 0 });\n    const { d3Zoom, d3Selection, d3ZoomHandler, userSelectionActive } = useStore(selector$a, shallow);\n    const zoomActivationKeyPressed = useKeyPress(zoomActivationKeyCode);\n    const mouseButton = useRef(0);\n    const isPanScrolling = useRef(false);\n    const panScrollTimeout = useRef();\n    useResizeHandler(zoomPane);\n    useEffect(() => {\n        if (zoomPane.current) {\n            const bbox = zoomPane.current.getBoundingClientRect();\n            const d3ZoomInstance = zoom().scaleExtent([minZoom, maxZoom]).translateExtent(translateExtent);\n            const selection = select(zoomPane.current).call(d3ZoomInstance);\n            const updatedTransform = zoomIdentity\n                .translate(defaultViewport.x, defaultViewport.y)\n                .scale(clamp(defaultViewport.zoom, minZoom, maxZoom));\n            const extent = [\n                [0, 0],\n                [bbox.width, bbox.height],\n            ];\n            const constrainedTransform = d3ZoomInstance.constrain()(updatedTransform, extent, translateExtent);\n            d3ZoomInstance.transform(selection, constrainedTransform);\n            d3ZoomInstance.wheelDelta(wheelDelta);\n            store.setState({\n                d3Zoom: d3ZoomInstance,\n                d3Selection: selection,\n                d3ZoomHandler: selection.on('wheel.zoom'),\n                // we need to pass transform because zoom handler is not registered when we set the initial transform\n                transform: [constrainedTransform.x, constrainedTransform.y, constrainedTransform.k],\n                domNode: zoomPane.current.closest('.react-flow'),\n            });\n        }\n    }, []);\n    useEffect(() => {\n        if (d3Selection && d3Zoom) {\n            if (panOnScroll && !zoomActivationKeyPressed && !userSelectionActive) {\n                d3Selection.on('wheel.zoom', (event) => {\n                    if (isWrappedWithClass(event, noWheelClassName)) {\n                        return false;\n                    }\n                    event.preventDefault();\n                    event.stopImmediatePropagation();\n                    const currentZoom = d3Selection.property('__zoom').k || 1;\n                    // macos and win set ctrlKey=true for pinch gesture on a trackpad\n                    if (event.ctrlKey && zoomOnPinch) {\n                        const point = pointer(event);\n                        const pinchDelta = wheelDelta(event);\n                        const zoom = currentZoom * Math.pow(2, pinchDelta);\n                        // @ts-ignore\n                        d3Zoom.scaleTo(d3Selection, zoom, point, event);\n                        return;\n                    }\n                    // increase scroll speed in firefox\n                    // firefox: deltaMode === 1; chrome: deltaMode === 0\n                    const deltaNormalize = event.deltaMode === 1 ? 20 : 1;\n                    let deltaX = panOnScrollMode === PanOnScrollMode.Vertical ? 0 : event.deltaX * deltaNormalize;\n                    let deltaY = panOnScrollMode === PanOnScrollMode.Horizontal ? 0 : event.deltaY * deltaNormalize;\n                    // this enables vertical scrolling with shift + scroll on windows\n                    if (!isMacOs() && event.shiftKey && panOnScrollMode !== PanOnScrollMode.Vertical) {\n                        deltaX = event.deltaY * deltaNormalize;\n                        deltaY = 0;\n                    }\n                    d3Zoom.translateBy(d3Selection, -(deltaX / currentZoom) * panOnScrollSpeed, -(deltaY / currentZoom) * panOnScrollSpeed, \n                    // @ts-ignore\n                    { internal: true });\n                    const nextViewport = eventToFlowTransform(d3Selection.property('__zoom'));\n                    const { onViewportChangeStart, onViewportChange, onViewportChangeEnd } = store.getState();\n                    clearTimeout(panScrollTimeout.current);\n                    // for pan on scroll we need to handle the event calls on our own\n                    // we can't use the start, zoom and end events from d3-zoom\n                    // because start and move gets called on every scroll event and not once at the beginning\n                    if (!isPanScrolling.current) {\n                        isPanScrolling.current = true;\n                        onMoveStart?.(event, nextViewport);\n                        onViewportChangeStart?.(nextViewport);\n                    }\n                    if (isPanScrolling.current) {\n                        onMove?.(event, nextViewport);\n                        onViewportChange?.(nextViewport);\n                        panScrollTimeout.current = setTimeout(() => {\n                            onMoveEnd?.(event, nextViewport);\n                            onViewportChangeEnd?.(nextViewport);\n                            isPanScrolling.current = false;\n                        }, 150);\n                    }\n                }, { passive: false });\n            }\n            else if (typeof d3ZoomHandler !== 'undefined') {\n                d3Selection.on('wheel.zoom', function (event, d) {\n                    // we still want to enable pinch zooming even if preventScrolling is set to false\n                    const invalidEvent = !preventScrolling && event.type === 'wheel' && !event.ctrlKey;\n                    if (invalidEvent || isWrappedWithClass(event, noWheelClassName)) {\n                        return null;\n                    }\n                    event.preventDefault();\n                    d3ZoomHandler.call(this, event, d);\n                }, { passive: false });\n            }\n        }\n    }, [\n        userSelectionActive,\n        panOnScroll,\n        panOnScrollMode,\n        d3Selection,\n        d3Zoom,\n        d3ZoomHandler,\n        zoomActivationKeyPressed,\n        zoomOnPinch,\n        preventScrolling,\n        noWheelClassName,\n        onMoveStart,\n        onMove,\n        onMoveEnd,\n    ]);\n    useEffect(() => {\n        if (d3Zoom) {\n            d3Zoom.on('start', (event) => {\n                if (!event.sourceEvent || event.sourceEvent.internal) {\n                    return null;\n                }\n                // we need to remember it here, because it's always 0 in the \"zoom\" event\n                mouseButton.current = event.sourceEvent?.button;\n                const { onViewportChangeStart } = store.getState();\n                const flowTransform = eventToFlowTransform(event.transform);\n                isZoomingOrPanning.current = true;\n                prevTransform.current = flowTransform;\n                if (event.sourceEvent?.type === 'mousedown') {\n                    store.setState({ paneDragging: true });\n                }\n                onViewportChangeStart?.(flowTransform);\n                onMoveStart?.(event.sourceEvent, flowTransform);\n            });\n        }\n    }, [d3Zoom, onMoveStart]);\n    useEffect(() => {\n        if (d3Zoom) {\n            if (userSelectionActive && !isZoomingOrPanning.current) {\n                d3Zoom.on('zoom', null);\n            }\n            else if (!userSelectionActive) {\n                d3Zoom.on('zoom', (event) => {\n                    const { onViewportChange } = store.getState();\n                    store.setState({ transform: [event.transform.x, event.transform.y, event.transform.k] });\n                    zoomedWithRightMouseButton.current = !!(onPaneContextMenu && isRightClickPan(panOnDrag, mouseButton.current ?? 0));\n                    if ((onMove || onViewportChange) && !event.sourceEvent?.internal) {\n                        const flowTransform = eventToFlowTransform(event.transform);\n                        onViewportChange?.(flowTransform);\n                        onMove?.(event.sourceEvent, flowTransform);\n                    }\n                });\n            }\n        }\n    }, [userSelectionActive, d3Zoom, onMove, panOnDrag, onPaneContextMenu]);\n    useEffect(() => {\n        if (d3Zoom) {\n            d3Zoom.on('end', (event) => {\n                if (!event.sourceEvent || event.sourceEvent.internal) {\n                    return null;\n                }\n                const { onViewportChangeEnd } = store.getState();\n                isZoomingOrPanning.current = false;\n                store.setState({ paneDragging: false });\n                if (onPaneContextMenu &&\n                    isRightClickPan(panOnDrag, mouseButton.current ?? 0) &&\n                    !zoomedWithRightMouseButton.current) {\n                    onPaneContextMenu(event.sourceEvent);\n                }\n                zoomedWithRightMouseButton.current = false;\n                if ((onMoveEnd || onViewportChangeEnd) && viewChanged(prevTransform.current, event.transform)) {\n                    const flowTransform = eventToFlowTransform(event.transform);\n                    prevTransform.current = flowTransform;\n                    clearTimeout(timerId.current);\n                    timerId.current = setTimeout(() => {\n                        onViewportChangeEnd?.(flowTransform);\n                        onMoveEnd?.(event.sourceEvent, flowTransform);\n                    }, panOnScroll ? 150 : 0);\n                }\n            });\n        }\n    }, [d3Zoom, panOnScroll, panOnDrag, onMoveEnd, onPaneContextMenu]);\n    useEffect(() => {\n        if (d3Zoom) {\n            d3Zoom.filter((event) => {\n                const zoomScroll = zoomActivationKeyPressed || zoomOnScroll;\n                const pinchZoom = zoomOnPinch && event.ctrlKey;\n                if ((panOnDrag === true || (Array.isArray(panOnDrag) && panOnDrag.includes(1))) &&\n                    event.button === 1 &&\n                    event.type === 'mousedown' &&\n                    (isWrappedWithClass(event, 'react-flow__node') || isWrappedWithClass(event, 'react-flow__edge'))) {\n                    return true;\n                }\n                // if all interactions are disabled, we prevent all zoom events\n                if (!panOnDrag && !zoomScroll && !panOnScroll && !zoomOnDoubleClick && !zoomOnPinch) {\n                    return false;\n                }\n                // during a selection we prevent all other interactions\n                if (userSelectionActive) {\n                    return false;\n                }\n                // if zoom on double click is disabled, we prevent the double click event\n                if (!zoomOnDoubleClick && event.type === 'dblclick') {\n                    return false;\n                }\n                // if the target element is inside an element with the nowheel class, we prevent zooming\n                if (isWrappedWithClass(event, noWheelClassName) && event.type === 'wheel') {\n                    return false;\n                }\n                // if the target element is inside an element with the nopan class, we prevent panning\n                if (isWrappedWithClass(event, noPanClassName) &&\n                    (event.type !== 'wheel' || (panOnScroll && event.type === 'wheel' && !zoomActivationKeyPressed))) {\n                    return false;\n                }\n                if (!zoomOnPinch && event.ctrlKey && event.type === 'wheel') {\n                    return false;\n                }\n                // when there is no scroll handling enabled, we prevent all wheel events\n                if (!zoomScroll && !panOnScroll && !pinchZoom && event.type === 'wheel') {\n                    return false;\n                }\n                // if the pane is not movable, we prevent dragging it with mousestart or touchstart\n                if (!panOnDrag && (event.type === 'mousedown' || event.type === 'touchstart')) {\n                    return false;\n                }\n                // if the pane is only movable using allowed clicks\n                if (Array.isArray(panOnDrag) && !panOnDrag.includes(event.button) && event.type === 'mousedown') {\n                    return false;\n                }\n                // We only allow right clicks if pan on drag is set to right click\n                const buttonAllowed = (Array.isArray(panOnDrag) && panOnDrag.includes(event.button)) || !event.button || event.button <= 1;\n                // default filter for d3-zoom\n                return (!event.ctrlKey || event.type === 'wheel') && buttonAllowed;\n            });\n        }\n    }, [\n        userSelectionActive,\n        d3Zoom,\n        zoomOnScroll,\n        zoomOnPinch,\n        panOnScroll,\n        zoomOnDoubleClick,\n        panOnDrag,\n        elementsSelectable,\n        zoomActivationKeyPressed,\n    ]);\n    return (React.createElement(\"div\", { className: \"react-flow__renderer\", ref: zoomPane, style: containerStyle }, children));\n};\n\nconst selector$9 = (s) => ({\n    userSelectionActive: s.userSelectionActive,\n    userSelectionRect: s.userSelectionRect,\n});\nfunction UserSelection() {\n    const { userSelectionActive, userSelectionRect } = useStore(selector$9, shallow);\n    const isActive = userSelectionActive && userSelectionRect;\n    if (!isActive) {\n        return null;\n    }\n    return (React.createElement(\"div\", { className: \"react-flow__selection react-flow__container\", style: {\n            width: userSelectionRect.width,\n            height: userSelectionRect.height,\n            transform: `translate(${userSelectionRect.x}px, ${userSelectionRect.y}px)`,\n        } }));\n}\n\nfunction handleParentExpand(res, updateItem) {\n    const parentId = updateItem.parentNode || updateItem.parentId;\n    const parent = res.find((e) => e.id === parentId);\n    if (parent) {\n        const extendWidth = updateItem.position.x + updateItem.width - parent.width;\n        const extendHeight = updateItem.position.y + updateItem.height - parent.height;\n        if (extendWidth > 0 || extendHeight > 0 || updateItem.position.x < 0 || updateItem.position.y < 0) {\n            parent.style = { ...parent.style } || {};\n            parent.style.width = parent.style.width ?? parent.width;\n            parent.style.height = parent.style.height ?? parent.height;\n            if (extendWidth > 0) {\n                parent.style.width += extendWidth;\n            }\n            if (extendHeight > 0) {\n                parent.style.height += extendHeight;\n            }\n            if (updateItem.position.x < 0) {\n                const xDiff = Math.abs(updateItem.position.x);\n                parent.position.x = parent.position.x - xDiff;\n                parent.style.width += xDiff;\n                updateItem.position.x = 0;\n            }\n            if (updateItem.position.y < 0) {\n                const yDiff = Math.abs(updateItem.position.y);\n                parent.position.y = parent.position.y - yDiff;\n                parent.style.height += yDiff;\n                updateItem.position.y = 0;\n            }\n            parent.width = parent.style.width;\n            parent.height = parent.style.height;\n        }\n    }\n}\nfunction applyChanges(changes, elements) {\n    // we need this hack to handle the setNodes and setEdges function of the useReactFlow hook for controlled flows\n    if (changes.some((c) => c.type === 'reset')) {\n        return changes.filter((c) => c.type === 'reset').map((c) => c.item);\n    }\n    const initElements = changes.filter((c) => c.type === 'add').map((c) => c.item);\n    return elements.reduce((res, item) => {\n        const currentChanges = changes.filter((c) => c.id === item.id);\n        if (currentChanges.length === 0) {\n            res.push(item);\n            return res;\n        }\n        const updateItem = { ...item };\n        for (const currentChange of currentChanges) {\n            if (currentChange) {\n                switch (currentChange.type) {\n                    case 'select': {\n                        updateItem.selected = currentChange.selected;\n                        break;\n                    }\n                    case 'position': {\n                        if (typeof currentChange.position !== 'undefined') {\n                            updateItem.position = currentChange.position;\n                        }\n                        if (typeof currentChange.positionAbsolute !== 'undefined') {\n                            updateItem.positionAbsolute = currentChange.positionAbsolute;\n                        }\n                        if (typeof currentChange.dragging !== 'undefined') {\n                            updateItem.dragging = currentChange.dragging;\n                        }\n                        if (updateItem.expandParent) {\n                            handleParentExpand(res, updateItem);\n                        }\n                        break;\n                    }\n                    case 'dimensions': {\n                        if (typeof currentChange.dimensions !== 'undefined') {\n                            updateItem.width = currentChange.dimensions.width;\n                            updateItem.height = currentChange.dimensions.height;\n                        }\n                        if (typeof currentChange.updateStyle !== 'undefined') {\n                            updateItem.style = { ...(updateItem.style || {}), ...currentChange.dimensions };\n                        }\n                        if (typeof currentChange.resizing === 'boolean') {\n                            updateItem.resizing = currentChange.resizing;\n                        }\n                        if (updateItem.expandParent) {\n                            handleParentExpand(res, updateItem);\n                        }\n                        break;\n                    }\n                    case 'remove': {\n                        return res;\n                    }\n                }\n            }\n        }\n        res.push(updateItem);\n        return res;\n    }, initElements);\n}\nfunction applyNodeChanges(changes, nodes) {\n    return applyChanges(changes, nodes);\n}\nfunction applyEdgeChanges(changes, edges) {\n    return applyChanges(changes, edges);\n}\nconst createSelectionChange = (id, selected) => ({\n    id,\n    type: 'select',\n    selected,\n});\nfunction getSelectionChanges(items, selectedIds) {\n    return items.reduce((res, item) => {\n        const willBeSelected = selectedIds.includes(item.id);\n        if (!item.selected && willBeSelected) {\n            item.selected = true;\n            res.push(createSelectionChange(item.id, true));\n        }\n        else if (item.selected && !willBeSelected) {\n            item.selected = false;\n            res.push(createSelectionChange(item.id, false));\n        }\n        return res;\n    }, []);\n}\n\n/**\n * The user selection rectangle gets displayed when a user drags the mouse while pressing shift\n */\nconst wrapHandler = (handler, containerRef) => {\n    return (event) => {\n        if (event.target !== containerRef.current) {\n            return;\n        }\n        handler?.(event);\n    };\n};\nconst selector$8 = (s) => ({\n    userSelectionActive: s.userSelectionActive,\n    elementsSelectable: s.elementsSelectable,\n    dragging: s.paneDragging,\n});\nconst Pane = memo(({ isSelecting, selectionMode = SelectionMode.Full, panOnDrag, onSelectionStart, onSelectionEnd, onPaneClick, onPaneContextMenu, onPaneScroll, onPaneMouseEnter, onPaneMouseMove, onPaneMouseLeave, children, }) => {\n    const container = useRef(null);\n    const store = useStoreApi();\n    const prevSelectedNodesCount = useRef(0);\n    const prevSelectedEdgesCount = useRef(0);\n    const containerBounds = useRef();\n    const { userSelectionActive, elementsSelectable, dragging } = useStore(selector$8, shallow);\n    const resetUserSelection = () => {\n        store.setState({ userSelectionActive: false, userSelectionRect: null });\n        prevSelectedNodesCount.current = 0;\n        prevSelectedEdgesCount.current = 0;\n    };\n    const onClick = (event) => {\n        onPaneClick?.(event);\n        store.getState().resetSelectedElements();\n        store.setState({ nodesSelectionActive: false });\n    };\n    const onContextMenu = (event) => {\n        if (Array.isArray(panOnDrag) && panOnDrag?.includes(2)) {\n            event.preventDefault();\n            return;\n        }\n        onPaneContextMenu?.(event);\n    };\n    const onWheel = onPaneScroll ? (event) => onPaneScroll(event) : undefined;\n    const onMouseDown = (event) => {\n        const { resetSelectedElements, domNode } = store.getState();\n        containerBounds.current = domNode?.getBoundingClientRect();\n        if (!elementsSelectable ||\n            !isSelecting ||\n            event.button !== 0 ||\n            event.target !== container.current ||\n            !containerBounds.current) {\n            return;\n        }\n        const { x, y } = getEventPosition(event, containerBounds.current);\n        resetSelectedElements();\n        store.setState({\n            userSelectionRect: {\n                width: 0,\n                height: 0,\n                startX: x,\n                startY: y,\n                x,\n                y,\n            },\n        });\n        onSelectionStart?.(event);\n    };\n    const onMouseMove = (event) => {\n        const { userSelectionRect, nodeInternals, edges, transform, onNodesChange, onEdgesChange, nodeOrigin, getNodes } = store.getState();\n        if (!isSelecting || !containerBounds.current || !userSelectionRect) {\n            return;\n        }\n        store.setState({ userSelectionActive: true, nodesSelectionActive: false });\n        const mousePos = getEventPosition(event, containerBounds.current);\n        const startX = userSelectionRect.startX ?? 0;\n        const startY = userSelectionRect.startY ?? 0;\n        const nextUserSelectRect = {\n            ...userSelectionRect,\n            x: mousePos.x < startX ? mousePos.x : startX,\n            y: mousePos.y < startY ? mousePos.y : startY,\n            width: Math.abs(mousePos.x - startX),\n            height: Math.abs(mousePos.y - startY),\n        };\n        const nodes = getNodes();\n        const selectedNodes = getNodesInside(nodeInternals, nextUserSelectRect, transform, selectionMode === SelectionMode.Partial, true, nodeOrigin);\n        const selectedEdgeIds = getConnectedEdges(selectedNodes, edges).map((e) => e.id);\n        const selectedNodeIds = selectedNodes.map((n) => n.id);\n        if (prevSelectedNodesCount.current !== selectedNodeIds.length) {\n            prevSelectedNodesCount.current = selectedNodeIds.length;\n            const changes = getSelectionChanges(nodes, selectedNodeIds);\n            if (changes.length) {\n                onNodesChange?.(changes);\n            }\n        }\n        if (prevSelectedEdgesCount.current !== selectedEdgeIds.length) {\n            prevSelectedEdgesCount.current = selectedEdgeIds.length;\n            const changes = getSelectionChanges(edges, selectedEdgeIds);\n            if (changes.length) {\n                onEdgesChange?.(changes);\n            }\n        }\n        store.setState({\n            userSelectionRect: nextUserSelectRect,\n        });\n    };\n    const onMouseUp = (event) => {\n        if (event.button !== 0) {\n            return;\n        }\n        const { userSelectionRect } = store.getState();\n        // We only want to trigger click functions when in selection mode if\n        // the user did not move the mouse.\n        if (!userSelectionActive && userSelectionRect && event.target === container.current) {\n            onClick?.(event);\n        }\n        store.setState({ nodesSelectionActive: prevSelectedNodesCount.current > 0 });\n        resetUserSelection();\n        onSelectionEnd?.(event);\n    };\n    const onMouseLeave = (event) => {\n        if (userSelectionActive) {\n            store.setState({ nodesSelectionActive: prevSelectedNodesCount.current > 0 });\n            onSelectionEnd?.(event);\n        }\n        resetUserSelection();\n    };\n    const hasActiveSelection = elementsSelectable && (isSelecting || userSelectionActive);\n    return (React.createElement(\"div\", { className: cc(['react-flow__pane', { dragging, selection: isSelecting }]), onClick: hasActiveSelection ? undefined : wrapHandler(onClick, container), onContextMenu: wrapHandler(onContextMenu, container), onWheel: wrapHandler(onWheel, container), onMouseEnter: hasActiveSelection ? undefined : onPaneMouseEnter, onMouseDown: hasActiveSelection ? onMouseDown : undefined, onMouseMove: hasActiveSelection ? onMouseMove : onPaneMouseMove, onMouseUp: hasActiveSelection ? onMouseUp : undefined, onMouseLeave: hasActiveSelection ? onMouseLeave : onPaneMouseLeave, ref: container, style: containerStyle },\n        children,\n        React.createElement(UserSelection, null)));\n});\nPane.displayName = 'Pane';\n\nfunction isParentSelected(node, nodeInternals) {\n    const parentId = node.parentNode || node.parentId;\n    if (!parentId) {\n        return false;\n    }\n    const parentNode = nodeInternals.get(parentId);\n    if (!parentNode) {\n        return false;\n    }\n    if (parentNode.selected) {\n        return true;\n    }\n    return isParentSelected(parentNode, nodeInternals);\n}\nfunction hasSelector(target, selector, nodeRef) {\n    let current = target;\n    do {\n        if (current?.matches(selector))\n            return true;\n        if (current === nodeRef.current)\n            return false;\n        current = current.parentElement;\n    } while (current);\n    return false;\n}\n// looks for all selected nodes and created a NodeDragItem for each of them\nfunction getDragItems(nodeInternals, nodesDraggable, mousePos, nodeId) {\n    return Array.from(nodeInternals.values())\n        .filter((n) => (n.selected || n.id === nodeId) &&\n        (!n.parentNode || n.parentId || !isParentSelected(n, nodeInternals)) &&\n        (n.draggable || (nodesDraggable && typeof n.draggable === 'undefined')))\n        .map((n) => ({\n        id: n.id,\n        position: n.position || { x: 0, y: 0 },\n        positionAbsolute: n.positionAbsolute || { x: 0, y: 0 },\n        distance: {\n            x: mousePos.x - (n.positionAbsolute?.x ?? 0),\n            y: mousePos.y - (n.positionAbsolute?.y ?? 0),\n        },\n        delta: {\n            x: 0,\n            y: 0,\n        },\n        extent: n.extent,\n        parentNode: n.parentNode || n.parentId,\n        parentId: n.parentNode || n.parentId,\n        width: n.width,\n        height: n.height,\n        expandParent: n.expandParent,\n    }));\n}\nfunction clampNodeExtent(node, extent) {\n    if (!extent || extent === 'parent') {\n        return extent;\n    }\n    return [extent[0], [extent[1][0] - (node.width || 0), extent[1][1] - (node.height || 0)]];\n}\nfunction calcNextPosition(node, nextPosition, nodeInternals, nodeExtent, nodeOrigin = [0, 0], onError) {\n    const clampedNodeExtent = clampNodeExtent(node, node.extent || nodeExtent);\n    let currentExtent = clampedNodeExtent;\n    const parentId = node.parentNode || node.parentId;\n    if (node.extent === 'parent' && !node.expandParent) {\n        if (parentId && node.width && node.height) {\n            const parent = nodeInternals.get(parentId);\n            const { x: parentX, y: parentY } = getNodePositionWithOrigin(parent, nodeOrigin).positionAbsolute;\n            currentExtent =\n                parent && isNumeric(parentX) && isNumeric(parentY) && isNumeric(parent.width) && isNumeric(parent.height)\n                    ? [\n                        [parentX + node.width * nodeOrigin[0], parentY + node.height * nodeOrigin[1]],\n                        [\n                            parentX + parent.width - node.width + node.width * nodeOrigin[0],\n                            parentY + parent.height - node.height + node.height * nodeOrigin[1],\n                        ],\n                    ]\n                    : currentExtent;\n        }\n        else {\n            onError?.('005', errorMessages['error005']());\n            currentExtent = clampedNodeExtent;\n        }\n    }\n    else if (node.extent && parentId && node.extent !== 'parent') {\n        const parent = nodeInternals.get(parentId);\n        const { x: parentX, y: parentY } = getNodePositionWithOrigin(parent, nodeOrigin).positionAbsolute;\n        currentExtent = [\n            [node.extent[0][0] + parentX, node.extent[0][1] + parentY],\n            [node.extent[1][0] + parentX, node.extent[1][1] + parentY],\n        ];\n    }\n    let parentPosition = { x: 0, y: 0 };\n    if (parentId) {\n        const parentNode = nodeInternals.get(parentId);\n        parentPosition = getNodePositionWithOrigin(parentNode, nodeOrigin).positionAbsolute;\n    }\n    const positionAbsolute = currentExtent && currentExtent !== 'parent'\n        ? clampPosition(nextPosition, currentExtent)\n        : nextPosition;\n    return {\n        position: {\n            x: positionAbsolute.x - parentPosition.x,\n            y: positionAbsolute.y - parentPosition.y,\n        },\n        positionAbsolute,\n    };\n}\n// returns two params:\n// 1. the dragged node (or the first of the list, if we are dragging a node selection)\n// 2. array of selected nodes (for multi selections)\nfunction getEventHandlerParams({ nodeId, dragItems, nodeInternals, }) {\n    const extentedDragItems = dragItems.map((n) => {\n        const node = nodeInternals.get(n.id);\n        return {\n            ...node,\n            position: n.position,\n            positionAbsolute: n.positionAbsolute,\n        };\n    });\n    return [nodeId ? extentedDragItems.find((n) => n.id === nodeId) : extentedDragItems[0], extentedDragItems];\n}\n\nconst getHandleBounds = (selector, nodeElement, zoom, nodeOrigin) => {\n    const handles = nodeElement.querySelectorAll(selector);\n    if (!handles || !handles.length) {\n        return null;\n    }\n    const handlesArray = Array.from(handles);\n    const nodeBounds = nodeElement.getBoundingClientRect();\n    const nodeOffset = {\n        x: nodeBounds.width * nodeOrigin[0],\n        y: nodeBounds.height * nodeOrigin[1],\n    };\n    return handlesArray.map((handle) => {\n        const handleBounds = handle.getBoundingClientRect();\n        return {\n            id: handle.getAttribute('data-handleid'),\n            position: handle.getAttribute('data-handlepos'),\n            x: (handleBounds.left - nodeBounds.left - nodeOffset.x) / zoom,\n            y: (handleBounds.top - nodeBounds.top - nodeOffset.y) / zoom,\n            ...getDimensions(handle),\n        };\n    });\n};\nfunction getMouseHandler(id, getState, handler) {\n    return handler === undefined\n        ? handler\n        : (event) => {\n            const node = getState().nodeInternals.get(id);\n            if (node) {\n                handler(event, { ...node });\n            }\n        };\n}\n// this handler is called by\n// 1. the click handler when node is not draggable or selectNodesOnDrag = false\n// or\n// 2. the on drag start handler when node is draggable and selectNodesOnDrag = true\nfunction handleNodeClick({ id, store, unselect = false, nodeRef, }) {\n    const { addSelectedNodes, unselectNodesAndEdges, multiSelectionActive, nodeInternals, onError } = store.getState();\n    const node = nodeInternals.get(id);\n    if (!node) {\n        onError?.('012', errorMessages['error012'](id));\n        return;\n    }\n    store.setState({ nodesSelectionActive: false });\n    if (!node.selected) {\n        addSelectedNodes([id]);\n    }\n    else if (unselect || (node.selected && multiSelectionActive)) {\n        unselectNodesAndEdges({ nodes: [node], edges: [] });\n        requestAnimationFrame(() => nodeRef?.current?.blur());\n    }\n}\n\nfunction useGetPointerPosition() {\n    const store = useStoreApi();\n    // returns the pointer position projected to the RF coordinate system\n    const getPointerPosition = useCallback(({ sourceEvent }) => {\n        const { transform, snapGrid, snapToGrid } = store.getState();\n        const x = sourceEvent.touches ? sourceEvent.touches[0].clientX : sourceEvent.clientX;\n        const y = sourceEvent.touches ? sourceEvent.touches[0].clientY : sourceEvent.clientY;\n        const pointerPos = {\n            x: (x - transform[0]) / transform[2],\n            y: (y - transform[1]) / transform[2],\n        };\n        // we need the snapped position in order to be able to skip unnecessary drag events\n        return {\n            xSnapped: snapToGrid ? snapGrid[0] * Math.round(pointerPos.x / snapGrid[0]) : pointerPos.x,\n            ySnapped: snapToGrid ? snapGrid[1] * Math.round(pointerPos.y / snapGrid[1]) : pointerPos.y,\n            ...pointerPos,\n        };\n    }, []);\n    return getPointerPosition;\n}\n\nfunction wrapSelectionDragFunc(selectionFunc) {\n    return (event, _, nodes) => selectionFunc?.(event, nodes);\n}\nfunction useDrag({ nodeRef, disabled = false, noDragClassName, handleSelector, nodeId, isSelectable, selectNodesOnDrag, }) {\n    const store = useStoreApi();\n    const [dragging, setDragging] = useState(false);\n    const dragItems = useRef([]);\n    const lastPos = useRef({ x: null, y: null });\n    const autoPanId = useRef(0);\n    const containerBounds = useRef(null);\n    const mousePosition = useRef({ x: 0, y: 0 });\n    const dragEvent = useRef(null);\n    const autoPanStarted = useRef(false);\n    const dragStarted = useRef(false);\n    const abortDrag = useRef(false);\n    const getPointerPosition = useGetPointerPosition();\n    useEffect(() => {\n        if (nodeRef?.current) {\n            const selection = select(nodeRef.current);\n            const updateNodes = ({ x, y }) => {\n                const { nodeInternals, onNodeDrag, onSelectionDrag, updateNodePositions, nodeExtent, snapGrid, snapToGrid, nodeOrigin, onError, } = store.getState();\n                lastPos.current = { x, y };\n                let hasChange = false;\n                let nodesBox = { x: 0, y: 0, x2: 0, y2: 0 };\n                if (dragItems.current.length > 1 && nodeExtent) {\n                    const rect = getNodesBounds(dragItems.current, nodeOrigin);\n                    nodesBox = rectToBox(rect);\n                }\n                dragItems.current = dragItems.current.map((n) => {\n                    const nextPosition = { x: x - n.distance.x, y: y - n.distance.y };\n                    if (snapToGrid) {\n                        nextPosition.x = snapGrid[0] * Math.round(nextPosition.x / snapGrid[0]);\n                        nextPosition.y = snapGrid[1] * Math.round(nextPosition.y / snapGrid[1]);\n                    }\n                    // if there is selection with multiple nodes and a node extent is set, we need to adjust the node extent for each node\n                    // based on its position so that the node stays at it's position relative to the selection.\n                    const adjustedNodeExtent = [\n                        [nodeExtent[0][0], nodeExtent[0][1]],\n                        [nodeExtent[1][0], nodeExtent[1][1]],\n                    ];\n                    if (dragItems.current.length > 1 && nodeExtent && !n.extent) {\n                        adjustedNodeExtent[0][0] = n.positionAbsolute.x - nodesBox.x + nodeExtent[0][0];\n                        adjustedNodeExtent[1][0] = n.positionAbsolute.x + (n.width ?? 0) - nodesBox.x2 + nodeExtent[1][0];\n                        adjustedNodeExtent[0][1] = n.positionAbsolute.y - nodesBox.y + nodeExtent[0][1];\n                        adjustedNodeExtent[1][1] = n.positionAbsolute.y + (n.height ?? 0) - nodesBox.y2 + nodeExtent[1][1];\n                    }\n                    const updatedPos = calcNextPosition(n, nextPosition, nodeInternals, adjustedNodeExtent, nodeOrigin, onError);\n                    // we want to make sure that we only fire a change event when there is a change\n                    hasChange = hasChange || n.position.x !== updatedPos.position.x || n.position.y !== updatedPos.position.y;\n                    n.position = updatedPos.position;\n                    n.positionAbsolute = updatedPos.positionAbsolute;\n                    return n;\n                });\n                if (!hasChange) {\n                    return;\n                }\n                updateNodePositions(dragItems.current, true, true);\n                setDragging(true);\n                const onDrag = nodeId ? onNodeDrag : wrapSelectionDragFunc(onSelectionDrag);\n                if (onDrag && dragEvent.current) {\n                    const [currentNode, nodes] = getEventHandlerParams({\n                        nodeId,\n                        dragItems: dragItems.current,\n                        nodeInternals,\n                    });\n                    onDrag(dragEvent.current, currentNode, nodes);\n                }\n            };\n            const autoPan = () => {\n                if (!containerBounds.current) {\n                    return;\n                }\n                const [xMovement, yMovement] = calcAutoPan(mousePosition.current, containerBounds.current);\n                if (xMovement !== 0 || yMovement !== 0) {\n                    const { transform, panBy } = store.getState();\n                    lastPos.current.x = (lastPos.current.x ?? 0) - xMovement / transform[2];\n                    lastPos.current.y = (lastPos.current.y ?? 0) - yMovement / transform[2];\n                    if (panBy({ x: xMovement, y: yMovement })) {\n                        updateNodes(lastPos.current);\n                    }\n                }\n                autoPanId.current = requestAnimationFrame(autoPan);\n            };\n            const startDrag = (event) => {\n                const { nodeInternals, multiSelectionActive, nodesDraggable, unselectNodesAndEdges, onNodeDragStart, onSelectionDragStart, } = store.getState();\n                dragStarted.current = true;\n                const onStart = nodeId ? onNodeDragStart : wrapSelectionDragFunc(onSelectionDragStart);\n                if ((!selectNodesOnDrag || !isSelectable) && !multiSelectionActive && nodeId) {\n                    if (!nodeInternals.get(nodeId)?.selected) {\n                        // we need to reset selected nodes when selectNodesOnDrag=false\n                        unselectNodesAndEdges();\n                    }\n                }\n                if (nodeId && isSelectable && selectNodesOnDrag) {\n                    handleNodeClick({\n                        id: nodeId,\n                        store,\n                        nodeRef: nodeRef,\n                    });\n                }\n                const pointerPos = getPointerPosition(event);\n                lastPos.current = pointerPos;\n                dragItems.current = getDragItems(nodeInternals, nodesDraggable, pointerPos, nodeId);\n                if (onStart && dragItems.current) {\n                    const [currentNode, nodes] = getEventHandlerParams({\n                        nodeId,\n                        dragItems: dragItems.current,\n                        nodeInternals,\n                    });\n                    onStart(event.sourceEvent, currentNode, nodes);\n                }\n            };\n            if (disabled) {\n                selection.on('.drag', null);\n            }\n            else {\n                const dragHandler = drag()\n                    .on('start', (event) => {\n                    const { domNode, nodeDragThreshold } = store.getState();\n                    if (nodeDragThreshold === 0) {\n                        startDrag(event);\n                    }\n                    abortDrag.current = false;\n                    const pointerPos = getPointerPosition(event);\n                    lastPos.current = pointerPos;\n                    containerBounds.current = domNode?.getBoundingClientRect() || null;\n                    mousePosition.current = getEventPosition(event.sourceEvent, containerBounds.current);\n                })\n                    .on('drag', (event) => {\n                    const pointerPos = getPointerPosition(event);\n                    const { autoPanOnNodeDrag, nodeDragThreshold } = store.getState();\n                    if (event.sourceEvent.type === 'touchmove' && event.sourceEvent.touches.length > 1) {\n                        abortDrag.current = true;\n                    }\n                    if (abortDrag.current) {\n                        return;\n                    }\n                    if (!autoPanStarted.current && dragStarted.current && autoPanOnNodeDrag) {\n                        autoPanStarted.current = true;\n                        autoPan();\n                    }\n                    if (!dragStarted.current) {\n                        const x = pointerPos.xSnapped - (lastPos?.current?.x ?? 0);\n                        const y = pointerPos.ySnapped - (lastPos?.current?.y ?? 0);\n                        const distance = Math.sqrt(x * x + y * y);\n                        if (distance > nodeDragThreshold) {\n                            startDrag(event);\n                        }\n                    }\n                    // skip events without movement\n                    if ((lastPos.current.x !== pointerPos.xSnapped || lastPos.current.y !== pointerPos.ySnapped) &&\n                        dragItems.current &&\n                        dragStarted.current) {\n                        dragEvent.current = event.sourceEvent;\n                        mousePosition.current = getEventPosition(event.sourceEvent, containerBounds.current);\n                        updateNodes(pointerPos);\n                    }\n                })\n                    .on('end', (event) => {\n                    if (!dragStarted.current || abortDrag.current) {\n                        return;\n                    }\n                    setDragging(false);\n                    autoPanStarted.current = false;\n                    dragStarted.current = false;\n                    cancelAnimationFrame(autoPanId.current);\n                    if (dragItems.current) {\n                        const { updateNodePositions, nodeInternals, onNodeDragStop, onSelectionDragStop } = store.getState();\n                        const onStop = nodeId ? onNodeDragStop : wrapSelectionDragFunc(onSelectionDragStop);\n                        updateNodePositions(dragItems.current, false, false);\n                        if (onStop) {\n                            const [currentNode, nodes] = getEventHandlerParams({\n                                nodeId,\n                                dragItems: dragItems.current,\n                                nodeInternals,\n                            });\n                            onStop(event.sourceEvent, currentNode, nodes);\n                        }\n                    }\n                })\n                    .filter((event) => {\n                    const target = event.target;\n                    const isDraggable = !event.button &&\n                        (!noDragClassName || !hasSelector(target, `.${noDragClassName}`, nodeRef)) &&\n                        (!handleSelector || hasSelector(target, handleSelector, nodeRef));\n                    return isDraggable;\n                });\n                selection.call(dragHandler);\n                return () => {\n                    selection.on('.drag', null);\n                };\n            }\n        }\n    }, [\n        nodeRef,\n        disabled,\n        noDragClassName,\n        handleSelector,\n        isSelectable,\n        store,\n        nodeId,\n        selectNodesOnDrag,\n        getPointerPosition,\n    ]);\n    return dragging;\n}\n\nfunction useUpdateNodePositions() {\n    const store = useStoreApi();\n    const updatePositions = useCallback((params) => {\n        const { nodeInternals, nodeExtent, updateNodePositions, getNodes, snapToGrid, snapGrid, onError, nodesDraggable } = store.getState();\n        const selectedNodes = getNodes().filter((n) => n.selected && (n.draggable || (nodesDraggable && typeof n.draggable === 'undefined')));\n        // by default a node moves 5px on each key press, or 20px if shift is pressed\n        // if snap grid is enabled, we use that for the velocity.\n        const xVelo = snapToGrid ? snapGrid[0] : 5;\n        const yVelo = snapToGrid ? snapGrid[1] : 5;\n        const factor = params.isShiftPressed ? 4 : 1;\n        const positionDiffX = params.x * xVelo * factor;\n        const positionDiffY = params.y * yVelo * factor;\n        const nodeUpdates = selectedNodes.map((n) => {\n            if (n.positionAbsolute) {\n                const nextPosition = { x: n.positionAbsolute.x + positionDiffX, y: n.positionAbsolute.y + positionDiffY };\n                if (snapToGrid) {\n                    nextPosition.x = snapGrid[0] * Math.round(nextPosition.x / snapGrid[0]);\n                    nextPosition.y = snapGrid[1] * Math.round(nextPosition.y / snapGrid[1]);\n                }\n                const { positionAbsolute, position } = calcNextPosition(n, nextPosition, nodeInternals, nodeExtent, undefined, onError);\n                n.position = position;\n                n.positionAbsolute = positionAbsolute;\n            }\n            return n;\n        });\n        updateNodePositions(nodeUpdates, true, false);\n    }, []);\n    return updatePositions;\n}\n\nconst arrowKeyDiffs = {\n    ArrowUp: { x: 0, y: -1 },\n    ArrowDown: { x: 0, y: 1 },\n    ArrowLeft: { x: -1, y: 0 },\n    ArrowRight: { x: 1, y: 0 },\n};\nvar wrapNode = (NodeComponent) => {\n    const NodeWrapper = ({ id, type, data, xPos, yPos, xPosOrigin, yPosOrigin, selected, onClick, onMouseEnter, onMouseMove, onMouseLeave, onContextMenu, onDoubleClick, style, className, isDraggable, isSelectable, isConnectable, isFocusable, selectNodesOnDrag, sourcePosition, targetPosition, hidden, resizeObserver, dragHandle, zIndex, isParent, noDragClassName, noPanClassName, initialized, disableKeyboardA11y, ariaLabel, rfId, hasHandleBounds, }) => {\n        const store = useStoreApi();\n        const nodeRef = useRef(null);\n        const prevNodeRef = useRef(null);\n        const prevSourcePosition = useRef(sourcePosition);\n        const prevTargetPosition = useRef(targetPosition);\n        const prevType = useRef(type);\n        const hasPointerEvents = isSelectable || isDraggable || onClick || onMouseEnter || onMouseMove || onMouseLeave;\n        const updatePositions = useUpdateNodePositions();\n        const onMouseEnterHandler = getMouseHandler(id, store.getState, onMouseEnter);\n        const onMouseMoveHandler = getMouseHandler(id, store.getState, onMouseMove);\n        const onMouseLeaveHandler = getMouseHandler(id, store.getState, onMouseLeave);\n        const onContextMenuHandler = getMouseHandler(id, store.getState, onContextMenu);\n        const onDoubleClickHandler = getMouseHandler(id, store.getState, onDoubleClick);\n        const onSelectNodeHandler = (event) => {\n            const { nodeDragThreshold } = store.getState();\n            if (isSelectable && (!selectNodesOnDrag || !isDraggable || nodeDragThreshold > 0)) {\n                // this handler gets called within the drag start event when selectNodesOnDrag=true\n                handleNodeClick({\n                    id,\n                    store,\n                    nodeRef,\n                });\n            }\n            if (onClick) {\n                const node = store.getState().nodeInternals.get(id);\n                if (node) {\n                    onClick(event, { ...node });\n                }\n            }\n        };\n        const onKeyDown = (event) => {\n            if (isInputDOMNode(event)) {\n                return;\n            }\n            if (disableKeyboardA11y) {\n                return;\n            }\n            if (elementSelectionKeys.includes(event.key) && isSelectable) {\n                const unselect = event.key === 'Escape';\n                handleNodeClick({\n                    id,\n                    store,\n                    unselect,\n                    nodeRef,\n                });\n            }\n            else if (isDraggable && selected && Object.prototype.hasOwnProperty.call(arrowKeyDiffs, event.key)) {\n                store.setState({\n                    ariaLiveMessage: `Moved selected node ${event.key\n                        .replace('Arrow', '')\n                        .toLowerCase()}. New position, x: ${~~xPos}, y: ${~~yPos}`,\n                });\n                updatePositions({\n                    x: arrowKeyDiffs[event.key].x,\n                    y: arrowKeyDiffs[event.key].y,\n                    isShiftPressed: event.shiftKey,\n                });\n            }\n        };\n        useEffect(() => {\n            return () => {\n                if (prevNodeRef.current) {\n                    resizeObserver?.unobserve(prevNodeRef.current);\n                    prevNodeRef.current = null;\n                }\n            };\n        }, []);\n        useEffect(() => {\n            if (nodeRef.current && !hidden) {\n                const currNode = nodeRef.current;\n                if (!initialized || !hasHandleBounds || prevNodeRef.current !== currNode) {\n                    // At this point we always want to make sure that the node gets re-measured / re-initialized.\n                    // We need to unobserve it first in case it is still observed\n                    if (prevNodeRef.current) {\n                        resizeObserver?.unobserve(prevNodeRef.current);\n                    }\n                    resizeObserver?.observe(currNode);\n                    prevNodeRef.current = currNode;\n                }\n            }\n        }, [hidden, initialized, hasHandleBounds]);\n        useEffect(() => {\n            // when the user programmatically changes the source or handle position, we re-initialize the node\n            const typeChanged = prevType.current !== type;\n            const sourcePosChanged = prevSourcePosition.current !== sourcePosition;\n            const targetPosChanged = prevTargetPosition.current !== targetPosition;\n            if (nodeRef.current && (typeChanged || sourcePosChanged || targetPosChanged)) {\n                if (typeChanged) {\n                    prevType.current = type;\n                }\n                if (sourcePosChanged) {\n                    prevSourcePosition.current = sourcePosition;\n                }\n                if (targetPosChanged) {\n                    prevTargetPosition.current = targetPosition;\n                }\n                store.getState().updateNodeDimensions([{ id, nodeElement: nodeRef.current, forceUpdate: true }]);\n            }\n        }, [id, type, sourcePosition, targetPosition]);\n        const dragging = useDrag({\n            nodeRef,\n            disabled: hidden || !isDraggable,\n            noDragClassName,\n            handleSelector: dragHandle,\n            nodeId: id,\n            isSelectable,\n            selectNodesOnDrag,\n        });\n        if (hidden) {\n            return null;\n        }\n        return (React.createElement(\"div\", { className: cc([\n                'react-flow__node',\n                `react-flow__node-${type}`,\n                {\n                    // this is overwritable by passing `nopan` as a class name\n                    [noPanClassName]: isDraggable,\n                },\n                className,\n                {\n                    selected,\n                    selectable: isSelectable,\n                    parent: isParent,\n                    dragging,\n                },\n            ]), ref: nodeRef, style: {\n                zIndex,\n                transform: `translate(${xPosOrigin}px,${yPosOrigin}px)`,\n                pointerEvents: hasPointerEvents ? 'all' : 'none',\n                visibility: initialized ? 'visible' : 'hidden',\n                ...style,\n            }, \"data-id\": id, \"data-testid\": `rf__node-${id}`, onMouseEnter: onMouseEnterHandler, onMouseMove: onMouseMoveHandler, onMouseLeave: onMouseLeaveHandler, onContextMenu: onContextMenuHandler, onClick: onSelectNodeHandler, onDoubleClick: onDoubleClickHandler, onKeyDown: isFocusable ? onKeyDown : undefined, tabIndex: isFocusable ? 0 : undefined, role: isFocusable ? 'button' : undefined, \"aria-describedby\": disableKeyboardA11y ? undefined : `${ARIA_NODE_DESC_KEY}-${rfId}`, \"aria-label\": ariaLabel },\n            React.createElement(Provider, { value: id },\n                React.createElement(NodeComponent, { id: id, data: data, type: type, xPos: xPos, yPos: yPos, selected: selected, isConnectable: isConnectable, sourcePosition: sourcePosition, targetPosition: targetPosition, dragging: dragging, dragHandle: dragHandle, zIndex: zIndex }))));\n    };\n    NodeWrapper.displayName = 'NodeWrapper';\n    return memo(NodeWrapper);\n};\n\n/**\n * The nodes selection rectangle gets displayed when a user\n * made a selection with on or several nodes\n */\nconst selector$7 = (s) => {\n    const selectedNodes = s.getNodes().filter((n) => n.selected);\n    return {\n        ...getNodesBounds(selectedNodes, s.nodeOrigin),\n        transformString: `translate(${s.transform[0]}px,${s.transform[1]}px) scale(${s.transform[2]})`,\n        userSelectionActive: s.userSelectionActive,\n    };\n};\nfunction NodesSelection({ onSelectionContextMenu, noPanClassName, disableKeyboardA11y }) {\n    const store = useStoreApi();\n    const { width, height, x: left, y: top, transformString, userSelectionActive } = useStore(selector$7, shallow);\n    const updatePositions = useUpdateNodePositions();\n    const nodeRef = useRef(null);\n    useEffect(() => {\n        if (!disableKeyboardA11y) {\n            nodeRef.current?.focus({\n                preventScroll: true,\n            });\n        }\n    }, [disableKeyboardA11y]);\n    useDrag({\n        nodeRef,\n    });\n    if (userSelectionActive || !width || !height) {\n        return null;\n    }\n    const onContextMenu = onSelectionContextMenu\n        ? (event) => {\n            const selectedNodes = store\n                .getState()\n                .getNodes()\n                .filter((n) => n.selected);\n            onSelectionContextMenu(event, selectedNodes);\n        }\n        : undefined;\n    const onKeyDown = (event) => {\n        if (Object.prototype.hasOwnProperty.call(arrowKeyDiffs, event.key)) {\n            updatePositions({\n                x: arrowKeyDiffs[event.key].x,\n                y: arrowKeyDiffs[event.key].y,\n                isShiftPressed: event.shiftKey,\n            });\n        }\n    };\n    return (React.createElement(\"div\", { className: cc(['react-flow__nodesselection', 'react-flow__container', noPanClassName]), style: {\n            transform: transformString,\n        } },\n        React.createElement(\"div\", { ref: nodeRef, className: \"react-flow__nodesselection-rect\", onContextMenu: onContextMenu, tabIndex: disableKeyboardA11y ? undefined : -1, onKeyDown: disableKeyboardA11y ? undefined : onKeyDown, style: {\n                width,\n                height,\n                top,\n                left,\n            } })));\n}\nvar NodesSelection$1 = memo(NodesSelection);\n\nconst selector$6 = (s) => s.nodesSelectionActive;\nconst FlowRenderer = ({ children, onPaneClick, onPaneMouseEnter, onPaneMouseMove, onPaneMouseLeave, onPaneContextMenu, onPaneScroll, deleteKeyCode, onMove, onMoveStart, onMoveEnd, selectionKeyCode, selectionOnDrag, selectionMode, onSelectionStart, onSelectionEnd, multiSelectionKeyCode, panActivationKeyCode, zoomActivationKeyCode, elementsSelectable, zoomOnScroll, zoomOnPinch, panOnScroll: _panOnScroll, panOnScrollSpeed, panOnScrollMode, zoomOnDoubleClick, panOnDrag: _panOnDrag, defaultViewport, translateExtent, minZoom, maxZoom, preventScrolling, onSelectionContextMenu, noWheelClassName, noPanClassName, disableKeyboardA11y, }) => {\n    const nodesSelectionActive = useStore(selector$6);\n    const selectionKeyPressed = useKeyPress(selectionKeyCode);\n    const panActivationKeyPressed = useKeyPress(panActivationKeyCode);\n    const panOnDrag = panActivationKeyPressed || _panOnDrag;\n    const panOnScroll = panActivationKeyPressed || _panOnScroll;\n    const isSelecting = selectionKeyPressed || (selectionOnDrag && panOnDrag !== true);\n    useGlobalKeyHandler({ deleteKeyCode, multiSelectionKeyCode });\n    return (React.createElement(ZoomPane, { onMove: onMove, onMoveStart: onMoveStart, onMoveEnd: onMoveEnd, onPaneContextMenu: onPaneContextMenu, elementsSelectable: elementsSelectable, zoomOnScroll: zoomOnScroll, zoomOnPinch: zoomOnPinch, panOnScroll: panOnScroll, panOnScrollSpeed: panOnScrollSpeed, panOnScrollMode: panOnScrollMode, zoomOnDoubleClick: zoomOnDoubleClick, panOnDrag: !selectionKeyPressed && panOnDrag, defaultViewport: defaultViewport, translateExtent: translateExtent, minZoom: minZoom, maxZoom: maxZoom, zoomActivationKeyCode: zoomActivationKeyCode, preventScrolling: preventScrolling, noWheelClassName: noWheelClassName, noPanClassName: noPanClassName },\n        React.createElement(Pane, { onSelectionStart: onSelectionStart, onSelectionEnd: onSelectionEnd, onPaneClick: onPaneClick, onPaneMouseEnter: onPaneMouseEnter, onPaneMouseMove: onPaneMouseMove, onPaneMouseLeave: onPaneMouseLeave, onPaneContextMenu: onPaneContextMenu, onPaneScroll: onPaneScroll, panOnDrag: panOnDrag, isSelecting: !!isSelecting, selectionMode: selectionMode },\n            children,\n            nodesSelectionActive && (React.createElement(NodesSelection$1, { onSelectionContextMenu: onSelectionContextMenu, noPanClassName: noPanClassName, disableKeyboardA11y: disableKeyboardA11y })))));\n};\nFlowRenderer.displayName = 'FlowRenderer';\nvar FlowRenderer$1 = memo(FlowRenderer);\n\nfunction useVisibleNodes(onlyRenderVisible) {\n    const nodes = useStore(useCallback((s) => onlyRenderVisible\n        ? getNodesInside(s.nodeInternals, { x: 0, y: 0, width: s.width, height: s.height }, s.transform, true)\n        : s.getNodes(), [onlyRenderVisible]));\n    return nodes;\n}\n\nfunction createNodeTypes(nodeTypes) {\n    const standardTypes = {\n        input: wrapNode((nodeTypes.input || InputNode$1)),\n        default: wrapNode((nodeTypes.default || DefaultNode$1)),\n        output: wrapNode((nodeTypes.output || OutputNode$1)),\n        group: wrapNode((nodeTypes.group || GroupNode)),\n    };\n    const wrappedTypes = {};\n    const specialTypes = Object.keys(nodeTypes)\n        .filter((k) => !['input', 'default', 'output', 'group'].includes(k))\n        .reduce((res, key) => {\n        res[key] = wrapNode((nodeTypes[key] || DefaultNode$1));\n        return res;\n    }, wrappedTypes);\n    return {\n        ...standardTypes,\n        ...specialTypes,\n    };\n}\nconst getPositionWithOrigin = ({ x, y, width, height, origin, }) => {\n    if (!width || !height) {\n        return { x, y };\n    }\n    if (origin[0] < 0 || origin[1] < 0 || origin[0] > 1 || origin[1] > 1) {\n        return { x, y };\n    }\n    return {\n        x: x - width * origin[0],\n        y: y - height * origin[1],\n    };\n};\n\nconst selector$5 = (s) => ({\n    nodesDraggable: s.nodesDraggable,\n    nodesConnectable: s.nodesConnectable,\n    nodesFocusable: s.nodesFocusable,\n    elementsSelectable: s.elementsSelectable,\n    updateNodeDimensions: s.updateNodeDimensions,\n    onError: s.onError,\n});\nconst NodeRenderer = (props) => {\n    const { nodesDraggable, nodesConnectable, nodesFocusable, elementsSelectable, updateNodeDimensions, onError } = useStore(selector$5, shallow);\n    const nodes = useVisibleNodes(props.onlyRenderVisibleElements);\n    const resizeObserverRef = useRef();\n    const resizeObserver = useMemo(() => {\n        if (typeof ResizeObserver === 'undefined') {\n            return null;\n        }\n        const observer = new ResizeObserver((entries) => {\n            const updates = entries.map((entry) => ({\n                id: entry.target.getAttribute('data-id'),\n                nodeElement: entry.target,\n                forceUpdate: true,\n            }));\n            updateNodeDimensions(updates);\n        });\n        resizeObserverRef.current = observer;\n        return observer;\n    }, []);\n    useEffect(() => {\n        return () => {\n            resizeObserverRef?.current?.disconnect();\n        };\n    }, []);\n    return (React.createElement(\"div\", { className: \"react-flow__nodes\", style: containerStyle }, nodes.map((node) => {\n        let nodeType = node.type || 'default';\n        if (!props.nodeTypes[nodeType]) {\n            onError?.('003', errorMessages['error003'](nodeType));\n            nodeType = 'default';\n        }\n        const NodeComponent = (props.nodeTypes[nodeType] || props.nodeTypes.default);\n        const isDraggable = !!(node.draggable || (nodesDraggable && typeof node.draggable === 'undefined'));\n        const isSelectable = !!(node.selectable || (elementsSelectable && typeof node.selectable === 'undefined'));\n        const isConnectable = !!(node.connectable || (nodesConnectable && typeof node.connectable === 'undefined'));\n        const isFocusable = !!(node.focusable || (nodesFocusable && typeof node.focusable === 'undefined'));\n        const clampedPosition = props.nodeExtent\n            ? clampPosition(node.positionAbsolute, props.nodeExtent)\n            : node.positionAbsolute;\n        const posX = clampedPosition?.x ?? 0;\n        const posY = clampedPosition?.y ?? 0;\n        const posOrigin = getPositionWithOrigin({\n            x: posX,\n            y: posY,\n            width: node.width ?? 0,\n            height: node.height ?? 0,\n            origin: props.nodeOrigin,\n        });\n        return (React.createElement(NodeComponent, { key: node.id, id: node.id, className: node.className, style: node.style, type: nodeType, data: node.data, sourcePosition: node.sourcePosition || Position.Bottom, targetPosition: node.targetPosition || Position.Top, hidden: node.hidden, xPos: posX, yPos: posY, xPosOrigin: posOrigin.x, yPosOrigin: posOrigin.y, selectNodesOnDrag: props.selectNodesOnDrag, onClick: props.onNodeClick, onMouseEnter: props.onNodeMouseEnter, onMouseMove: props.onNodeMouseMove, onMouseLeave: props.onNodeMouseLeave, onContextMenu: props.onNodeContextMenu, onDoubleClick: props.onNodeDoubleClick, selected: !!node.selected, isDraggable: isDraggable, isSelectable: isSelectable, isConnectable: isConnectable, isFocusable: isFocusable, resizeObserver: resizeObserver, dragHandle: node.dragHandle, zIndex: node[internalsSymbol]?.z ?? 0, isParent: !!node[internalsSymbol]?.isParent, noDragClassName: props.noDragClassName, noPanClassName: props.noPanClassName, initialized: !!node.width && !!node.height, rfId: props.rfId, disableKeyboardA11y: props.disableKeyboardA11y, ariaLabel: node.ariaLabel, hasHandleBounds: !!node[internalsSymbol]?.handleBounds }));\n    })));\n};\nNodeRenderer.displayName = 'NodeRenderer';\nvar NodeRenderer$1 = memo(NodeRenderer);\n\nconst shiftX = (x, shift, position) => {\n    if (position === Position.Left)\n        return x - shift;\n    if (position === Position.Right)\n        return x + shift;\n    return x;\n};\nconst shiftY = (y, shift, position) => {\n    if (position === Position.Top)\n        return y - shift;\n    if (position === Position.Bottom)\n        return y + shift;\n    return y;\n};\nconst EdgeUpdaterClassName = 'react-flow__edgeupdater';\nconst EdgeAnchor = ({ position, centerX, centerY, radius = 10, onMouseDown, onMouseEnter, onMouseOut, type, }) => (React.createElement(\"circle\", { onMouseDown: onMouseDown, onMouseEnter: onMouseEnter, onMouseOut: onMouseOut, className: cc([EdgeUpdaterClassName, `${EdgeUpdaterClassName}-${type}`]), cx: shiftX(centerX, radius, position), cy: shiftY(centerY, radius, position), r: radius, stroke: \"transparent\", fill: \"transparent\" }));\n\nconst alwaysValidConnection = () => true;\nvar wrapEdge = (EdgeComponent) => {\n    const EdgeWrapper = ({ id, className, type, data, onClick, onEdgeDoubleClick, selected, animated, label, labelStyle, labelShowBg, labelBgStyle, labelBgPadding, labelBgBorderRadius, style, source, target, sourceX, sourceY, targetX, targetY, sourcePosition, targetPosition, elementsSelectable, hidden, sourceHandleId, targetHandleId, onContextMenu, onMouseEnter, onMouseMove, onMouseLeave, reconnectRadius, onReconnect, onReconnectStart, onReconnectEnd, markerEnd, markerStart, rfId, ariaLabel, isFocusable, isReconnectable, pathOptions, interactionWidth, disableKeyboardA11y, }) => {\n        const edgeRef = useRef(null);\n        const [updateHover, setUpdateHover] = useState(false);\n        const [updating, setUpdating] = useState(false);\n        const store = useStoreApi();\n        const markerStartUrl = useMemo(() => `url('#${getMarkerId(markerStart, rfId)}')`, [markerStart, rfId]);\n        const markerEndUrl = useMemo(() => `url('#${getMarkerId(markerEnd, rfId)}')`, [markerEnd, rfId]);\n        if (hidden) {\n            return null;\n        }\n        const onEdgeClick = (event) => {\n            const { edges, addSelectedEdges, unselectNodesAndEdges, multiSelectionActive } = store.getState();\n            const edge = edges.find((e) => e.id === id);\n            if (!edge) {\n                return;\n            }\n            if (elementsSelectable) {\n                store.setState({ nodesSelectionActive: false });\n                if (edge.selected && multiSelectionActive) {\n                    unselectNodesAndEdges({ nodes: [], edges: [edge] });\n                    edgeRef.current?.blur();\n                }\n                else {\n                    addSelectedEdges([id]);\n                }\n            }\n            if (onClick) {\n                onClick(event, edge);\n            }\n        };\n        const onEdgeDoubleClickHandler = getMouseHandler$1(id, store.getState, onEdgeDoubleClick);\n        const onEdgeContextMenu = getMouseHandler$1(id, store.getState, onContextMenu);\n        const onEdgeMouseEnter = getMouseHandler$1(id, store.getState, onMouseEnter);\n        const onEdgeMouseMove = getMouseHandler$1(id, store.getState, onMouseMove);\n        const onEdgeMouseLeave = getMouseHandler$1(id, store.getState, onMouseLeave);\n        const handleEdgeUpdater = (event, isSourceHandle) => {\n            // avoid triggering edge updater if mouse btn is not left\n            if (event.button !== 0) {\n                return;\n            }\n            const { edges, isValidConnection: isValidConnectionStore } = store.getState();\n            const nodeId = isSourceHandle ? target : source;\n            const handleId = (isSourceHandle ? targetHandleId : sourceHandleId) || null;\n            const handleType = isSourceHandle ? 'target' : 'source';\n            const isValidConnection = isValidConnectionStore || alwaysValidConnection;\n            const isTarget = isSourceHandle;\n            const edge = edges.find((e) => e.id === id);\n            setUpdating(true);\n            onReconnectStart?.(event, edge, handleType);\n            const _onReconnectEnd = (evt) => {\n                setUpdating(false);\n                onReconnectEnd?.(evt, edge, handleType);\n            };\n            const onConnectEdge = (connection) => onReconnect?.(edge, connection);\n            handlePointerDown({\n                event,\n                handleId,\n                nodeId,\n                onConnect: onConnectEdge,\n                isTarget,\n                getState: store.getState,\n                setState: store.setState,\n                isValidConnection,\n                edgeUpdaterType: handleType,\n                onReconnectEnd: _onReconnectEnd,\n            });\n        };\n        const onEdgeUpdaterSourceMouseDown = (event) => handleEdgeUpdater(event, true);\n        const onEdgeUpdaterTargetMouseDown = (event) => handleEdgeUpdater(event, false);\n        const onEdgeUpdaterMouseEnter = () => setUpdateHover(true);\n        const onEdgeUpdaterMouseOut = () => setUpdateHover(false);\n        const inactive = !elementsSelectable && !onClick;\n        const onKeyDown = (event) => {\n            if (!disableKeyboardA11y && elementSelectionKeys.includes(event.key) && elementsSelectable) {\n                const { unselectNodesAndEdges, addSelectedEdges, edges } = store.getState();\n                const unselect = event.key === 'Escape';\n                if (unselect) {\n                    edgeRef.current?.blur();\n                    unselectNodesAndEdges({ edges: [edges.find((e) => e.id === id)] });\n                }\n                else {\n                    addSelectedEdges([id]);\n                }\n            }\n        };\n        return (React.createElement(\"g\", { className: cc([\n                'react-flow__edge',\n                `react-flow__edge-${type}`,\n                className,\n                { selected, animated, inactive, updating: updateHover },\n            ]), onClick: onEdgeClick, onDoubleClick: onEdgeDoubleClickHandler, onContextMenu: onEdgeContextMenu, onMouseEnter: onEdgeMouseEnter, onMouseMove: onEdgeMouseMove, onMouseLeave: onEdgeMouseLeave, onKeyDown: isFocusable ? onKeyDown : undefined, tabIndex: isFocusable ? 0 : undefined, role: isFocusable ? 'button' : 'img', \"data-testid\": `rf__edge-${id}`, \"aria-label\": ariaLabel === null ? undefined : ariaLabel ? ariaLabel : `Edge from ${source} to ${target}`, \"aria-describedby\": isFocusable ? `${ARIA_EDGE_DESC_KEY}-${rfId}` : undefined, ref: edgeRef },\n            !updating && (React.createElement(EdgeComponent, { id: id, source: source, target: target, selected: selected, animated: animated, label: label, labelStyle: labelStyle, labelShowBg: labelShowBg, labelBgStyle: labelBgStyle, labelBgPadding: labelBgPadding, labelBgBorderRadius: labelBgBorderRadius, data: data, style: style, sourceX: sourceX, sourceY: sourceY, targetX: targetX, targetY: targetY, sourcePosition: sourcePosition, targetPosition: targetPosition, sourceHandleId: sourceHandleId, targetHandleId: targetHandleId, markerStart: markerStartUrl, markerEnd: markerEndUrl, pathOptions: pathOptions, interactionWidth: interactionWidth })),\n            isReconnectable && (React.createElement(React.Fragment, null,\n                (isReconnectable === 'source' || isReconnectable === true) && (React.createElement(EdgeAnchor, { position: sourcePosition, centerX: sourceX, centerY: sourceY, radius: reconnectRadius, onMouseDown: onEdgeUpdaterSourceMouseDown, onMouseEnter: onEdgeUpdaterMouseEnter, onMouseOut: onEdgeUpdaterMouseOut, type: \"source\" })),\n                (isReconnectable === 'target' || isReconnectable === true) && (React.createElement(EdgeAnchor, { position: targetPosition, centerX: targetX, centerY: targetY, radius: reconnectRadius, onMouseDown: onEdgeUpdaterTargetMouseDown, onMouseEnter: onEdgeUpdaterMouseEnter, onMouseOut: onEdgeUpdaterMouseOut, type: \"target\" }))))));\n    };\n    EdgeWrapper.displayName = 'EdgeWrapper';\n    return memo(EdgeWrapper);\n};\n\nfunction createEdgeTypes(edgeTypes) {\n    const standardTypes = {\n        default: wrapEdge((edgeTypes.default || BezierEdge)),\n        straight: wrapEdge((edgeTypes.bezier || StraightEdge)),\n        step: wrapEdge((edgeTypes.step || StepEdge)),\n        smoothstep: wrapEdge((edgeTypes.step || SmoothStepEdge)),\n        simplebezier: wrapEdge((edgeTypes.simplebezier || SimpleBezierEdge)),\n    };\n    const wrappedTypes = {};\n    const specialTypes = Object.keys(edgeTypes)\n        .filter((k) => !['default', 'bezier'].includes(k))\n        .reduce((res, key) => {\n        res[key] = wrapEdge((edgeTypes[key] || BezierEdge));\n        return res;\n    }, wrappedTypes);\n    return {\n        ...standardTypes,\n        ...specialTypes,\n    };\n}\nfunction getHandlePosition(position, nodeRect, handle = null) {\n    const x = (handle?.x || 0) + nodeRect.x;\n    const y = (handle?.y || 0) + nodeRect.y;\n    const width = handle?.width || nodeRect.width;\n    const height = handle?.height || nodeRect.height;\n    switch (position) {\n        case Position.Top:\n            return {\n                x: x + width / 2,\n                y,\n            };\n        case Position.Right:\n            return {\n                x: x + width,\n                y: y + height / 2,\n            };\n        case Position.Bottom:\n            return {\n                x: x + width / 2,\n                y: y + height,\n            };\n        case Position.Left:\n            return {\n                x,\n                y: y + height / 2,\n            };\n    }\n}\nfunction getHandle(bounds, handleId) {\n    if (!bounds) {\n        return null;\n    }\n    if (bounds.length === 1 || !handleId) {\n        return bounds[0];\n    }\n    else if (handleId) {\n        return bounds.find((d) => d.id === handleId) || null;\n    }\n    return null;\n}\nconst getEdgePositions = (sourceNodeRect, sourceHandle, sourcePosition, targetNodeRect, targetHandle, targetPosition) => {\n    const sourceHandlePos = getHandlePosition(sourcePosition, sourceNodeRect, sourceHandle);\n    const targetHandlePos = getHandlePosition(targetPosition, targetNodeRect, targetHandle);\n    return {\n        sourceX: sourceHandlePos.x,\n        sourceY: sourceHandlePos.y,\n        targetX: targetHandlePos.x,\n        targetY: targetHandlePos.y,\n    };\n};\nfunction isEdgeVisible({ sourcePos, targetPos, sourceWidth, sourceHeight, targetWidth, targetHeight, width, height, transform, }) {\n    const edgeBox = {\n        x: Math.min(sourcePos.x, targetPos.x),\n        y: Math.min(sourcePos.y, targetPos.y),\n        x2: Math.max(sourcePos.x + sourceWidth, targetPos.x + targetWidth),\n        y2: Math.max(sourcePos.y + sourceHeight, targetPos.y + targetHeight),\n    };\n    if (edgeBox.x === edgeBox.x2) {\n        edgeBox.x2 += 1;\n    }\n    if (edgeBox.y === edgeBox.y2) {\n        edgeBox.y2 += 1;\n    }\n    const viewBox = rectToBox({\n        x: (0 - transform[0]) / transform[2],\n        y: (0 - transform[1]) / transform[2],\n        width: width / transform[2],\n        height: height / transform[2],\n    });\n    const xOverlap = Math.max(0, Math.min(viewBox.x2, edgeBox.x2) - Math.max(viewBox.x, edgeBox.x));\n    const yOverlap = Math.max(0, Math.min(viewBox.y2, edgeBox.y2) - Math.max(viewBox.y, edgeBox.y));\n    const overlappingArea = Math.ceil(xOverlap * yOverlap);\n    return overlappingArea > 0;\n}\nfunction getNodeData(node) {\n    const handleBounds = node?.[internalsSymbol]?.handleBounds || null;\n    const isValid = handleBounds &&\n        node?.width &&\n        node?.height &&\n        typeof node?.positionAbsolute?.x !== 'undefined' &&\n        typeof node?.positionAbsolute?.y !== 'undefined';\n    return [\n        {\n            x: node?.positionAbsolute?.x || 0,\n            y: node?.positionAbsolute?.y || 0,\n            width: node?.width || 0,\n            height: node?.height || 0,\n        },\n        handleBounds,\n        !!isValid,\n    ];\n}\n\nconst defaultEdgeTree = [{ level: 0, isMaxLevel: true, edges: [] }];\nfunction groupEdgesByZLevel(edges, nodeInternals, elevateEdgesOnSelect = false) {\n    let maxLevel = -1;\n    const levelLookup = edges.reduce((tree, edge) => {\n        const hasZIndex = isNumeric(edge.zIndex);\n        let z = hasZIndex ? edge.zIndex : 0;\n        if (elevateEdgesOnSelect) {\n            const targetNode = nodeInternals.get(edge.target);\n            const sourceNode = nodeInternals.get(edge.source);\n            const edgeOrConnectedNodeSelected = edge.selected || targetNode?.selected || sourceNode?.selected;\n            const selectedZIndex = Math.max(sourceNode?.[internalsSymbol]?.z || 0, targetNode?.[internalsSymbol]?.z || 0, 1000);\n            z = (hasZIndex ? edge.zIndex : 0) + (edgeOrConnectedNodeSelected ? selectedZIndex : 0);\n        }\n        if (tree[z]) {\n            tree[z].push(edge);\n        }\n        else {\n            tree[z] = [edge];\n        }\n        maxLevel = z > maxLevel ? z : maxLevel;\n        return tree;\n    }, {});\n    const edgeTree = Object.entries(levelLookup).map(([key, edges]) => {\n        const level = +key;\n        return {\n            edges,\n            level,\n            isMaxLevel: level === maxLevel,\n        };\n    });\n    if (edgeTree.length === 0) {\n        return defaultEdgeTree;\n    }\n    return edgeTree;\n}\nfunction useVisibleEdges(onlyRenderVisible, nodeInternals, elevateEdgesOnSelect) {\n    const edges = useStore(useCallback((s) => {\n        if (!onlyRenderVisible) {\n            return s.edges;\n        }\n        return s.edges.filter((e) => {\n            const sourceNode = nodeInternals.get(e.source);\n            const targetNode = nodeInternals.get(e.target);\n            return (sourceNode?.width &&\n                sourceNode?.height &&\n                targetNode?.width &&\n                targetNode?.height &&\n                isEdgeVisible({\n                    sourcePos: sourceNode.positionAbsolute || { x: 0, y: 0 },\n                    targetPos: targetNode.positionAbsolute || { x: 0, y: 0 },\n                    sourceWidth: sourceNode.width,\n                    sourceHeight: sourceNode.height,\n                    targetWidth: targetNode.width,\n                    targetHeight: targetNode.height,\n                    width: s.width,\n                    height: s.height,\n                    transform: s.transform,\n                }));\n        });\n    }, [onlyRenderVisible, nodeInternals]));\n    return groupEdgesByZLevel(edges, nodeInternals, elevateEdgesOnSelect);\n}\n\nconst ArrowSymbol = ({ color = 'none', strokeWidth = 1 }) => {\n    return (React.createElement(\"polyline\", { style: {\n            stroke: color,\n            strokeWidth,\n        }, strokeLinecap: \"round\", strokeLinejoin: \"round\", fill: \"none\", points: \"-5,-4 0,0 -5,4\" }));\n};\nconst ArrowClosedSymbol = ({ color = 'none', strokeWidth = 1 }) => {\n    return (React.createElement(\"polyline\", { style: {\n            stroke: color,\n            fill: color,\n            strokeWidth,\n        }, strokeLinecap: \"round\", strokeLinejoin: \"round\", points: \"-5,-4 0,0 -5,4 -5,-4\" }));\n};\nconst MarkerSymbols = {\n    [MarkerType.Arrow]: ArrowSymbol,\n    [MarkerType.ArrowClosed]: ArrowClosedSymbol,\n};\nfunction useMarkerSymbol(type) {\n    const store = useStoreApi();\n    const symbol = useMemo(() => {\n        const symbolExists = Object.prototype.hasOwnProperty.call(MarkerSymbols, type);\n        if (!symbolExists) {\n            store.getState().onError?.('009', errorMessages['error009'](type));\n            return null;\n        }\n        return MarkerSymbols[type];\n    }, [type]);\n    return symbol;\n}\n\nconst Marker = ({ id, type, color, width = 12.5, height = 12.5, markerUnits = 'strokeWidth', strokeWidth, orient = 'auto-start-reverse', }) => {\n    const Symbol = useMarkerSymbol(type);\n    if (!Symbol) {\n        return null;\n    }\n    return (React.createElement(\"marker\", { className: \"react-flow__arrowhead\", id: id, markerWidth: `${width}`, markerHeight: `${height}`, viewBox: \"-10 -10 20 20\", markerUnits: markerUnits, orient: orient, refX: \"0\", refY: \"0\" },\n        React.createElement(Symbol, { color: color, strokeWidth: strokeWidth })));\n};\nconst markerSelector = ({ defaultColor, rfId }) => (s) => {\n    const ids = [];\n    return s.edges\n        .reduce((markers, edge) => {\n        [edge.markerStart, edge.markerEnd].forEach((marker) => {\n            if (marker && typeof marker === 'object') {\n                const markerId = getMarkerId(marker, rfId);\n                if (!ids.includes(markerId)) {\n                    markers.push({ id: markerId, color: marker.color || defaultColor, ...marker });\n                    ids.push(markerId);\n                }\n            }\n        });\n        return markers;\n    }, [])\n        .sort((a, b) => a.id.localeCompare(b.id));\n};\n// when you have multiple flows on a page and you hide the first one, the other ones have no markers anymore\n// when they do have markers with the same ids. To prevent this the user can pass a unique id to the react flow wrapper\n// that we can then use for creating our unique marker ids\nconst MarkerDefinitions = ({ defaultColor, rfId }) => {\n    const markers = useStore(useCallback(markerSelector({ defaultColor, rfId }), [defaultColor, rfId]), \n    // the id includes all marker options, so we just need to look at that part of the marker\n    (a, b) => !(a.length !== b.length || a.some((m, i) => m.id !== b[i].id)));\n    return (React.createElement(\"defs\", null, markers.map((marker) => (React.createElement(Marker, { id: marker.id, key: marker.id, type: marker.type, color: marker.color, width: marker.width, height: marker.height, markerUnits: marker.markerUnits, strokeWidth: marker.strokeWidth, orient: marker.orient })))));\n};\nMarkerDefinitions.displayName = 'MarkerDefinitions';\nvar MarkerDefinitions$1 = memo(MarkerDefinitions);\n\nconst selector$4 = (s) => ({\n    nodesConnectable: s.nodesConnectable,\n    edgesFocusable: s.edgesFocusable,\n    edgesUpdatable: s.edgesUpdatable,\n    elementsSelectable: s.elementsSelectable,\n    width: s.width,\n    height: s.height,\n    connectionMode: s.connectionMode,\n    nodeInternals: s.nodeInternals,\n    onError: s.onError,\n});\nconst EdgeRenderer = ({ defaultMarkerColor, onlyRenderVisibleElements, elevateEdgesOnSelect, rfId, edgeTypes, noPanClassName, onEdgeContextMenu, onEdgeMouseEnter, onEdgeMouseMove, onEdgeMouseLeave, onEdgeClick, onEdgeDoubleClick, onReconnect, onReconnectStart, onReconnectEnd, reconnectRadius, children, disableKeyboardA11y, }) => {\n    const { edgesFocusable, edgesUpdatable, elementsSelectable, width, height, connectionMode, nodeInternals, onError } = useStore(selector$4, shallow);\n    const edgeTree = useVisibleEdges(onlyRenderVisibleElements, nodeInternals, elevateEdgesOnSelect);\n    if (!width) {\n        return null;\n    }\n    return (React.createElement(React.Fragment, null,\n        edgeTree.map(({ level, edges, isMaxLevel }) => (React.createElement(\"svg\", { key: level, style: { zIndex: level }, width: width, height: height, className: \"react-flow__edges react-flow__container\" },\n            isMaxLevel && React.createElement(MarkerDefinitions$1, { defaultColor: defaultMarkerColor, rfId: rfId }),\n            React.createElement(\"g\", null, edges.map((edge) => {\n                const [sourceNodeRect, sourceHandleBounds, sourceIsValid] = getNodeData(nodeInternals.get(edge.source));\n                const [targetNodeRect, targetHandleBounds, targetIsValid] = getNodeData(nodeInternals.get(edge.target));\n                if (!sourceIsValid || !targetIsValid) {\n                    return null;\n                }\n                let edgeType = edge.type || 'default';\n                if (!edgeTypes[edgeType]) {\n                    onError?.('011', errorMessages['error011'](edgeType));\n                    edgeType = 'default';\n                }\n                const EdgeComponent = edgeTypes[edgeType] || edgeTypes.default;\n                // when connection type is loose we can define all handles as sources and connect source -> source\n                const targetNodeHandles = connectionMode === ConnectionMode.Strict\n                    ? targetHandleBounds.target\n                    : (targetHandleBounds.target ?? []).concat(targetHandleBounds.source ?? []);\n                const sourceHandle = getHandle(sourceHandleBounds.source, edge.sourceHandle);\n                const targetHandle = getHandle(targetNodeHandles, edge.targetHandle);\n                const sourcePosition = sourceHandle?.position || Position.Bottom;\n                const targetPosition = targetHandle?.position || Position.Top;\n                const isFocusable = !!(edge.focusable || (edgesFocusable && typeof edge.focusable === 'undefined'));\n                const edgeReconnectable = edge.reconnectable || edge.updatable;\n                const isReconnectable = typeof onReconnect !== 'undefined' &&\n                    (edgeReconnectable || (edgesUpdatable && typeof edgeReconnectable === 'undefined'));\n                if (!sourceHandle || !targetHandle) {\n                    onError?.('008', errorMessages['error008'](sourceHandle, edge));\n                    return null;\n                }\n                const { sourceX, sourceY, targetX, targetY } = getEdgePositions(sourceNodeRect, sourceHandle, sourcePosition, targetNodeRect, targetHandle, targetPosition);\n                return (React.createElement(EdgeComponent, { key: edge.id, id: edge.id, className: cc([edge.className, noPanClassName]), type: edgeType, data: edge.data, selected: !!edge.selected, animated: !!edge.animated, hidden: !!edge.hidden, label: edge.label, labelStyle: edge.labelStyle, labelShowBg: edge.labelShowBg, labelBgStyle: edge.labelBgStyle, labelBgPadding: edge.labelBgPadding, labelBgBorderRadius: edge.labelBgBorderRadius, style: edge.style, source: edge.source, target: edge.target, sourceHandleId: edge.sourceHandle, targetHandleId: edge.targetHandle, markerEnd: edge.markerEnd, markerStart: edge.markerStart, sourceX: sourceX, sourceY: sourceY, targetX: targetX, targetY: targetY, sourcePosition: sourcePosition, targetPosition: targetPosition, elementsSelectable: elementsSelectable, onContextMenu: onEdgeContextMenu, onMouseEnter: onEdgeMouseEnter, onMouseMove: onEdgeMouseMove, onMouseLeave: onEdgeMouseLeave, onClick: onEdgeClick, onEdgeDoubleClick: onEdgeDoubleClick, onReconnect: onReconnect, onReconnectStart: onReconnectStart, onReconnectEnd: onReconnectEnd, reconnectRadius: reconnectRadius, rfId: rfId, ariaLabel: edge.ariaLabel, isFocusable: isFocusable, isReconnectable: isReconnectable, pathOptions: 'pathOptions' in edge ? edge.pathOptions : undefined, interactionWidth: edge.interactionWidth, disableKeyboardA11y: disableKeyboardA11y }));\n            }))))),\n        children));\n};\nEdgeRenderer.displayName = 'EdgeRenderer';\nvar EdgeRenderer$1 = memo(EdgeRenderer);\n\nconst selector$3 = (s) => `translate(${s.transform[0]}px,${s.transform[1]}px) scale(${s.transform[2]})`;\nfunction Viewport({ children }) {\n    const transform = useStore(selector$3);\n    return (React.createElement(\"div\", { className: \"react-flow__viewport react-flow__container\", style: { transform } }, children));\n}\n\nfunction useOnInitHandler(onInit) {\n    const rfInstance = useReactFlow();\n    const isInitialized = useRef(false);\n    useEffect(() => {\n        if (!isInitialized.current && rfInstance.viewportInitialized && onInit) {\n            setTimeout(() => onInit(rfInstance), 1);\n            isInitialized.current = true;\n        }\n    }, [onInit, rfInstance.viewportInitialized]);\n}\n\nconst oppositePosition = {\n    [Position.Left]: Position.Right,\n    [Position.Right]: Position.Left,\n    [Position.Top]: Position.Bottom,\n    [Position.Bottom]: Position.Top,\n};\nconst ConnectionLine = ({ nodeId, handleType, style, type = ConnectionLineType.Bezier, CustomComponent, connectionStatus, }) => {\n    const { fromNode, handleId, toX, toY, connectionMode } = useStore(useCallback((s) => ({\n        fromNode: s.nodeInternals.get(nodeId),\n        handleId: s.connectionHandleId,\n        toX: (s.connectionPosition.x - s.transform[0]) / s.transform[2],\n        toY: (s.connectionPosition.y - s.transform[1]) / s.transform[2],\n        connectionMode: s.connectionMode,\n    }), [nodeId]), shallow);\n    const fromHandleBounds = fromNode?.[internalsSymbol]?.handleBounds;\n    let handleBounds = fromHandleBounds?.[handleType];\n    if (connectionMode === ConnectionMode.Loose) {\n        handleBounds = handleBounds ? handleBounds : fromHandleBounds?.[handleType === 'source' ? 'target' : 'source'];\n    }\n    if (!fromNode || !handleBounds) {\n        return null;\n    }\n    const fromHandle = handleId ? handleBounds.find((d) => d.id === handleId) : handleBounds[0];\n    const fromHandleX = fromHandle ? fromHandle.x + fromHandle.width / 2 : (fromNode.width ?? 0) / 2;\n    const fromHandleY = fromHandle ? fromHandle.y + fromHandle.height / 2 : fromNode.height ?? 0;\n    const fromX = (fromNode.positionAbsolute?.x ?? 0) + fromHandleX;\n    const fromY = (fromNode.positionAbsolute?.y ?? 0) + fromHandleY;\n    const fromPosition = fromHandle?.position;\n    const toPosition = fromPosition ? oppositePosition[fromPosition] : null;\n    if (!fromPosition || !toPosition) {\n        return null;\n    }\n    if (CustomComponent) {\n        return (React.createElement(CustomComponent, { connectionLineType: type, connectionLineStyle: style, fromNode: fromNode, fromHandle: fromHandle, fromX: fromX, fromY: fromY, toX: toX, toY: toY, fromPosition: fromPosition, toPosition: toPosition, connectionStatus: connectionStatus }));\n    }\n    let dAttr = '';\n    const pathParams = {\n        sourceX: fromX,\n        sourceY: fromY,\n        sourcePosition: fromPosition,\n        targetX: toX,\n        targetY: toY,\n        targetPosition: toPosition,\n    };\n    if (type === ConnectionLineType.Bezier) {\n        // we assume the destination position is opposite to the source position\n        [dAttr] = getBezierPath(pathParams);\n    }\n    else if (type === ConnectionLineType.Step) {\n        [dAttr] = getSmoothStepPath({\n            ...pathParams,\n            borderRadius: 0,\n        });\n    }\n    else if (type === ConnectionLineType.SmoothStep) {\n        [dAttr] = getSmoothStepPath(pathParams);\n    }\n    else if (type === ConnectionLineType.SimpleBezier) {\n        [dAttr] = getSimpleBezierPath(pathParams);\n    }\n    else {\n        dAttr = `M${fromX},${fromY} ${toX},${toY}`;\n    }\n    return React.createElement(\"path\", { d: dAttr, fill: \"none\", className: \"react-flow__connection-path\", style: style });\n};\nConnectionLine.displayName = 'ConnectionLine';\nconst selector$2 = (s) => ({\n    nodeId: s.connectionNodeId,\n    handleType: s.connectionHandleType,\n    nodesConnectable: s.nodesConnectable,\n    connectionStatus: s.connectionStatus,\n    width: s.width,\n    height: s.height,\n});\nfunction ConnectionLineWrapper({ containerStyle, style, type, component }) {\n    const { nodeId, handleType, nodesConnectable, width, height, connectionStatus } = useStore(selector$2, shallow);\n    const isValid = !!(nodeId && handleType && width && nodesConnectable);\n    if (!isValid) {\n        return null;\n    }\n    return (React.createElement(\"svg\", { style: containerStyle, width: width, height: height, className: \"react-flow__edges react-flow__connectionline react-flow__container\" },\n        React.createElement(\"g\", { className: cc(['react-flow__connection', connectionStatus]) },\n            React.createElement(ConnectionLine, { nodeId: nodeId, handleType: handleType, style: style, type: type, CustomComponent: component, connectionStatus: connectionStatus }))));\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction useNodeOrEdgeTypes(nodeOrEdgeTypes, createTypes) {\n    const typesKeysRef = useRef(null);\n    const store = useStoreApi();\n    const typesParsed = useMemo(() => {\n        if (process.env.NODE_ENV === 'development') {\n            const typeKeys = Object.keys(nodeOrEdgeTypes);\n            if (shallow(typesKeysRef.current, typeKeys)) {\n                store.getState().onError?.('002', errorMessages['error002']());\n            }\n            typesKeysRef.current = typeKeys;\n        }\n        return createTypes(nodeOrEdgeTypes);\n    }, [nodeOrEdgeTypes]);\n    return typesParsed;\n}\n\nconst GraphView = ({ nodeTypes, edgeTypes, onMove, onMoveStart, onMoveEnd, onInit, onNodeClick, onEdgeClick, onNodeDoubleClick, onEdgeDoubleClick, onNodeMouseEnter, onNodeMouseMove, onNodeMouseLeave, onNodeContextMenu, onSelectionContextMenu, onSelectionStart, onSelectionEnd, connectionLineType, connectionLineStyle, connectionLineComponent, connectionLineContainerStyle, selectionKeyCode, selectionOnDrag, selectionMode, multiSelectionKeyCode, panActivationKeyCode, zoomActivationKeyCode, deleteKeyCode, onlyRenderVisibleElements, elementsSelectable, selectNodesOnDrag, defaultViewport, translateExtent, minZoom, maxZoom, preventScrolling, defaultMarkerColor, zoomOnScroll, zoomOnPinch, panOnScroll, panOnScrollSpeed, panOnScrollMode, zoomOnDoubleClick, panOnDrag, onPaneClick, onPaneMouseEnter, onPaneMouseMove, onPaneMouseLeave, onPaneScroll, onPaneContextMenu, onEdgeContextMenu, onEdgeMouseEnter, onEdgeMouseMove, onEdgeMouseLeave, onReconnect, onReconnectStart, onReconnectEnd, reconnectRadius, noDragClassName, noWheelClassName, noPanClassName, elevateEdgesOnSelect, disableKeyboardA11y, nodeOrigin, nodeExtent, rfId, }) => {\n    const nodeTypesWrapped = useNodeOrEdgeTypes(nodeTypes, createNodeTypes);\n    const edgeTypesWrapped = useNodeOrEdgeTypes(edgeTypes, createEdgeTypes);\n    useOnInitHandler(onInit);\n    return (React.createElement(FlowRenderer$1, { onPaneClick: onPaneClick, onPaneMouseEnter: onPaneMouseEnter, onPaneMouseMove: onPaneMouseMove, onPaneMouseLeave: onPaneMouseLeave, onPaneContextMenu: onPaneContextMenu, onPaneScroll: onPaneScroll, deleteKeyCode: deleteKeyCode, selectionKeyCode: selectionKeyCode, selectionOnDrag: selectionOnDrag, selectionMode: selectionMode, onSelectionStart: onSelectionStart, onSelectionEnd: onSelectionEnd, multiSelectionKeyCode: multiSelectionKeyCode, panActivationKeyCode: panActivationKeyCode, zoomActivationKeyCode: zoomActivationKeyCode, elementsSelectable: elementsSelectable, onMove: onMove, onMoveStart: onMoveStart, onMoveEnd: onMoveEnd, zoomOnScroll: zoomOnScroll, zoomOnPinch: zoomOnPinch, zoomOnDoubleClick: zoomOnDoubleClick, panOnScroll: panOnScroll, panOnScrollSpeed: panOnScrollSpeed, panOnScrollMode: panOnScrollMode, panOnDrag: panOnDrag, defaultViewport: defaultViewport, translateExtent: translateExtent, minZoom: minZoom, maxZoom: maxZoom, onSelectionContextMenu: onSelectionContextMenu, preventScrolling: preventScrolling, noDragClassName: noDragClassName, noWheelClassName: noWheelClassName, noPanClassName: noPanClassName, disableKeyboardA11y: disableKeyboardA11y },\n        React.createElement(Viewport, null,\n            React.createElement(EdgeRenderer$1, { edgeTypes: edgeTypesWrapped, onEdgeClick: onEdgeClick, onEdgeDoubleClick: onEdgeDoubleClick, onlyRenderVisibleElements: onlyRenderVisibleElements, onEdgeContextMenu: onEdgeContextMenu, onEdgeMouseEnter: onEdgeMouseEnter, onEdgeMouseMove: onEdgeMouseMove, onEdgeMouseLeave: onEdgeMouseLeave, onReconnect: onReconnect, onReconnectStart: onReconnectStart, onReconnectEnd: onReconnectEnd, reconnectRadius: reconnectRadius, defaultMarkerColor: defaultMarkerColor, noPanClassName: noPanClassName, elevateEdgesOnSelect: !!elevateEdgesOnSelect, disableKeyboardA11y: disableKeyboardA11y, rfId: rfId },\n                React.createElement(ConnectionLineWrapper, { style: connectionLineStyle, type: connectionLineType, component: connectionLineComponent, containerStyle: connectionLineContainerStyle })),\n            React.createElement(\"div\", { className: \"react-flow__edgelabel-renderer\" }),\n            React.createElement(NodeRenderer$1, { nodeTypes: nodeTypesWrapped, onNodeClick: onNodeClick, onNodeDoubleClick: onNodeDoubleClick, onNodeMouseEnter: onNodeMouseEnter, onNodeMouseMove: onNodeMouseMove, onNodeMouseLeave: onNodeMouseLeave, onNodeContextMenu: onNodeContextMenu, selectNodesOnDrag: selectNodesOnDrag, onlyRenderVisibleElements: onlyRenderVisibleElements, noPanClassName: noPanClassName, noDragClassName: noDragClassName, disableKeyboardA11y: disableKeyboardA11y, nodeOrigin: nodeOrigin, nodeExtent: nodeExtent, rfId: rfId }))));\n};\nGraphView.displayName = 'GraphView';\nvar GraphView$1 = memo(GraphView);\n\nconst infiniteExtent = [\n    [Number.NEGATIVE_INFINITY, Number.NEGATIVE_INFINITY],\n    [Number.POSITIVE_INFINITY, Number.POSITIVE_INFINITY],\n];\nconst initialState = {\n    rfId: '1',\n    width: 0,\n    height: 0,\n    transform: [0, 0, 1],\n    nodeInternals: new Map(),\n    edges: [],\n    onNodesChange: null,\n    onEdgesChange: null,\n    hasDefaultNodes: false,\n    hasDefaultEdges: false,\n    d3Zoom: null,\n    d3Selection: null,\n    d3ZoomHandler: undefined,\n    minZoom: 0.5,\n    maxZoom: 2,\n    translateExtent: infiniteExtent,\n    nodeExtent: infiniteExtent,\n    nodesSelectionActive: false,\n    userSelectionActive: false,\n    userSelectionRect: null,\n    connectionNodeId: null,\n    connectionHandleId: null,\n    connectionHandleType: 'source',\n    connectionPosition: { x: 0, y: 0 },\n    connectionStatus: null,\n    connectionMode: ConnectionMode.Strict,\n    domNode: null,\n    paneDragging: false,\n    noPanClassName: 'nopan',\n    nodeOrigin: [0, 0],\n    nodeDragThreshold: 0,\n    snapGrid: [15, 15],\n    snapToGrid: false,\n    nodesDraggable: true,\n    nodesConnectable: true,\n    nodesFocusable: true,\n    edgesFocusable: true,\n    edgesUpdatable: true,\n    elementsSelectable: true,\n    elevateNodesOnSelect: true,\n    fitViewOnInit: false,\n    fitViewOnInitDone: false,\n    fitViewOnInitOptions: undefined,\n    onSelectionChange: [],\n    multiSelectionActive: false,\n    connectionStartHandle: null,\n    connectionEndHandle: null,\n    connectionClickStartHandle: null,\n    connectOnClick: true,\n    ariaLiveMessage: '',\n    autoPanOnConnect: true,\n    autoPanOnNodeDrag: true,\n    connectionRadius: 20,\n    onError: devWarn,\n    isValidConnection: undefined,\n};\n\nconst createRFStore = () => createWithEqualityFn((set, get) => ({\n    ...initialState,\n    setNodes: (nodes) => {\n        const { nodeInternals, nodeOrigin, elevateNodesOnSelect } = get();\n        set({ nodeInternals: createNodeInternals(nodes, nodeInternals, nodeOrigin, elevateNodesOnSelect) });\n    },\n    getNodes: () => {\n        return Array.from(get().nodeInternals.values());\n    },\n    setEdges: (edges) => {\n        const { defaultEdgeOptions = {} } = get();\n        set({ edges: edges.map((e) => ({ ...defaultEdgeOptions, ...e })) });\n    },\n    setDefaultNodesAndEdges: (nodes, edges) => {\n        const hasDefaultNodes = typeof nodes !== 'undefined';\n        const hasDefaultEdges = typeof edges !== 'undefined';\n        const nodeInternals = hasDefaultNodes\n            ? createNodeInternals(nodes, new Map(), get().nodeOrigin, get().elevateNodesOnSelect)\n            : new Map();\n        const nextEdges = hasDefaultEdges ? edges : [];\n        set({ nodeInternals, edges: nextEdges, hasDefaultNodes, hasDefaultEdges });\n    },\n    updateNodeDimensions: (updates) => {\n        const { onNodesChange, nodeInternals, fitViewOnInit, fitViewOnInitDone, fitViewOnInitOptions, domNode, nodeOrigin, } = get();\n        const viewportNode = domNode?.querySelector('.react-flow__viewport');\n        if (!viewportNode) {\n            return;\n        }\n        const style = window.getComputedStyle(viewportNode);\n        const { m22: zoom } = new window.DOMMatrixReadOnly(style.transform);\n        const changes = updates.reduce((res, update) => {\n            const node = nodeInternals.get(update.id);\n            if (node?.hidden) {\n                nodeInternals.set(node.id, {\n                    ...node,\n                    [internalsSymbol]: {\n                        ...node[internalsSymbol],\n                        // we need to reset the handle bounds when the node is hidden\n                        // in order to force a new observation when the node is shown again\n                        handleBounds: undefined,\n                    },\n                });\n            }\n            else if (node) {\n                const dimensions = getDimensions(update.nodeElement);\n                const doUpdate = !!(dimensions.width &&\n                    dimensions.height &&\n                    (node.width !== dimensions.width || node.height !== dimensions.height || update.forceUpdate));\n                if (doUpdate) {\n                    nodeInternals.set(node.id, {\n                        ...node,\n                        [internalsSymbol]: {\n                            ...node[internalsSymbol],\n                            handleBounds: {\n                                source: getHandleBounds('.source', update.nodeElement, zoom, nodeOrigin),\n                                target: getHandleBounds('.target', update.nodeElement, zoom, nodeOrigin),\n                            },\n                        },\n                        ...dimensions,\n                    });\n                    res.push({\n                        id: node.id,\n                        type: 'dimensions',\n                        dimensions,\n                    });\n                }\n            }\n            return res;\n        }, []);\n        updateAbsoluteNodePositions(nodeInternals, nodeOrigin);\n        const nextFitViewOnInitDone = fitViewOnInitDone ||\n            (fitViewOnInit && !fitViewOnInitDone && fitView(get, { initial: true, ...fitViewOnInitOptions }));\n        set({ nodeInternals: new Map(nodeInternals), fitViewOnInitDone: nextFitViewOnInitDone });\n        if (changes?.length > 0) {\n            onNodesChange?.(changes);\n        }\n    },\n    updateNodePositions: (nodeDragItems, positionChanged = true, dragging = false) => {\n        const { triggerNodeChanges } = get();\n        const changes = nodeDragItems.map((node) => {\n            const change = {\n                id: node.id,\n                type: 'position',\n                dragging,\n            };\n            if (positionChanged) {\n                change.positionAbsolute = node.positionAbsolute;\n                change.position = node.position;\n            }\n            return change;\n        });\n        triggerNodeChanges(changes);\n    },\n    triggerNodeChanges: (changes) => {\n        const { onNodesChange, nodeInternals, hasDefaultNodes, nodeOrigin, getNodes, elevateNodesOnSelect } = get();\n        if (changes?.length) {\n            if (hasDefaultNodes) {\n                const nodes = applyNodeChanges(changes, getNodes());\n                const nextNodeInternals = createNodeInternals(nodes, nodeInternals, nodeOrigin, elevateNodesOnSelect);\n                set({ nodeInternals: nextNodeInternals });\n            }\n            onNodesChange?.(changes);\n        }\n    },\n    addSelectedNodes: (selectedNodeIds) => {\n        const { multiSelectionActive, edges, getNodes } = get();\n        let changedNodes;\n        let changedEdges = null;\n        if (multiSelectionActive) {\n            changedNodes = selectedNodeIds.map((nodeId) => createSelectionChange(nodeId, true));\n        }\n        else {\n            changedNodes = getSelectionChanges(getNodes(), selectedNodeIds);\n            changedEdges = getSelectionChanges(edges, []);\n        }\n        updateNodesAndEdgesSelections({\n            changedNodes,\n            changedEdges,\n            get,\n            set,\n        });\n    },\n    addSelectedEdges: (selectedEdgeIds) => {\n        const { multiSelectionActive, edges, getNodes } = get();\n        let changedEdges;\n        let changedNodes = null;\n        if (multiSelectionActive) {\n            changedEdges = selectedEdgeIds.map((edgeId) => createSelectionChange(edgeId, true));\n        }\n        else {\n            changedEdges = getSelectionChanges(edges, selectedEdgeIds);\n            changedNodes = getSelectionChanges(getNodes(), []);\n        }\n        updateNodesAndEdgesSelections({\n            changedNodes,\n            changedEdges,\n            get,\n            set,\n        });\n    },\n    unselectNodesAndEdges: ({ nodes, edges } = {}) => {\n        const { edges: storeEdges, getNodes } = get();\n        const nodesToUnselect = nodes ? nodes : getNodes();\n        const edgesToUnselect = edges ? edges : storeEdges;\n        const changedNodes = nodesToUnselect.map((n) => {\n            n.selected = false;\n            return createSelectionChange(n.id, false);\n        });\n        const changedEdges = edgesToUnselect.map((edge) => createSelectionChange(edge.id, false));\n        updateNodesAndEdgesSelections({\n            changedNodes,\n            changedEdges,\n            get,\n            set,\n        });\n    },\n    setMinZoom: (minZoom) => {\n        const { d3Zoom, maxZoom } = get();\n        d3Zoom?.scaleExtent([minZoom, maxZoom]);\n        set({ minZoom });\n    },\n    setMaxZoom: (maxZoom) => {\n        const { d3Zoom, minZoom } = get();\n        d3Zoom?.scaleExtent([minZoom, maxZoom]);\n        set({ maxZoom });\n    },\n    setTranslateExtent: (translateExtent) => {\n        get().d3Zoom?.translateExtent(translateExtent);\n        set({ translateExtent });\n    },\n    resetSelectedElements: () => {\n        const { edges, getNodes } = get();\n        const nodes = getNodes();\n        const nodesToUnselect = nodes\n            .filter((e) => e.selected)\n            .map((n) => createSelectionChange(n.id, false));\n        const edgesToUnselect = edges\n            .filter((e) => e.selected)\n            .map((e) => createSelectionChange(e.id, false));\n        updateNodesAndEdgesSelections({\n            changedNodes: nodesToUnselect,\n            changedEdges: edgesToUnselect,\n            get,\n            set,\n        });\n    },\n    setNodeExtent: (nodeExtent) => {\n        const { nodeInternals } = get();\n        nodeInternals.forEach((node) => {\n            node.positionAbsolute = clampPosition(node.position, nodeExtent);\n        });\n        set({\n            nodeExtent,\n            nodeInternals: new Map(nodeInternals),\n        });\n    },\n    panBy: (delta) => {\n        const { transform, width, height, d3Zoom, d3Selection, translateExtent } = get();\n        if (!d3Zoom || !d3Selection || (!delta.x && !delta.y)) {\n            return false;\n        }\n        const nextTransform = zoomIdentity\n            .translate(transform[0] + delta.x, transform[1] + delta.y)\n            .scale(transform[2]);\n        const extent = [\n            [0, 0],\n            [width, height],\n        ];\n        const constrainedTransform = d3Zoom?.constrain()(nextTransform, extent, translateExtent);\n        d3Zoom.transform(d3Selection, constrainedTransform);\n        const transformChanged = transform[0] !== constrainedTransform.x ||\n            transform[1] !== constrainedTransform.y ||\n            transform[2] !== constrainedTransform.k;\n        return transformChanged;\n    },\n    cancelConnection: () => set({\n        connectionNodeId: initialState.connectionNodeId,\n        connectionHandleId: initialState.connectionHandleId,\n        connectionHandleType: initialState.connectionHandleType,\n        connectionStatus: initialState.connectionStatus,\n        connectionStartHandle: initialState.connectionStartHandle,\n        connectionEndHandle: initialState.connectionEndHandle,\n    }),\n    reset: () => set({ ...initialState }),\n}), Object.is);\n\nconst ReactFlowProvider = ({ children }) => {\n    const storeRef = useRef(null);\n    if (!storeRef.current) {\n        storeRef.current = createRFStore();\n    }\n    return React.createElement(Provider$1, { value: storeRef.current }, children);\n};\nReactFlowProvider.displayName = 'ReactFlowProvider';\n\nconst Wrapper = ({ children }) => {\n    const isWrapped = useContext(StoreContext);\n    if (isWrapped) {\n        // we need to wrap it with a fragment because it's not allowed for children to be a ReactNode\n        // https://github.com/DefinitelyTyped/DefinitelyTyped/issues/18051\n        return React.createElement(React.Fragment, null, children);\n    }\n    return React.createElement(ReactFlowProvider, null, children);\n};\nWrapper.displayName = 'ReactFlowWrapper';\n\nconst defaultNodeTypes = {\n    input: InputNode$1,\n    default: DefaultNode$1,\n    output: OutputNode$1,\n    group: GroupNode,\n};\nconst defaultEdgeTypes = {\n    default: BezierEdge,\n    straight: StraightEdge,\n    step: StepEdge,\n    smoothstep: SmoothStepEdge,\n    simplebezier: SimpleBezierEdge,\n};\nconst initNodeOrigin = [0, 0];\nconst initSnapGrid = [15, 15];\nconst initDefaultViewport = { x: 0, y: 0, zoom: 1 };\nconst wrapperStyle = {\n    width: '100%',\n    height: '100%',\n    overflow: 'hidden',\n    position: 'relative',\n    zIndex: 0,\n};\nconst ReactFlow = forwardRef(({ nodes, edges, defaultNodes, defaultEdges, className, nodeTypes = defaultNodeTypes, edgeTypes = defaultEdgeTypes, onNodeClick, onEdgeClick, onInit, onMove, onMoveStart, onMoveEnd, onConnect, onConnectStart, onConnectEnd, onClickConnectStart, onClickConnectEnd, onNodeMouseEnter, onNodeMouseMove, onNodeMouseLeave, onNodeContextMenu, onNodeDoubleClick, onNodeDragStart, onNodeDrag, onNodeDragStop, onNodesDelete, onEdgesDelete, onSelectionChange, onSelectionDragStart, onSelectionDrag, onSelectionDragStop, onSelectionContextMenu, onSelectionStart, onSelectionEnd, connectionMode = ConnectionMode.Strict, connectionLineType = ConnectionLineType.Bezier, connectionLineStyle, connectionLineComponent, connectionLineContainerStyle, deleteKeyCode = 'Backspace', selectionKeyCode = 'Shift', selectionOnDrag = false, selectionMode = SelectionMode.Full, panActivationKeyCode = 'Space', multiSelectionKeyCode = isMacOs() ? 'Meta' : 'Control', zoomActivationKeyCode = isMacOs() ? 'Meta' : 'Control', snapToGrid = false, snapGrid = initSnapGrid, onlyRenderVisibleElements = false, selectNodesOnDrag = true, nodesDraggable, nodesConnectable, nodesFocusable, nodeOrigin = initNodeOrigin, edgesFocusable, edgesUpdatable, elementsSelectable, defaultViewport = initDefaultViewport, minZoom = 0.5, maxZoom = 2, translateExtent = infiniteExtent, preventScrolling = true, nodeExtent, defaultMarkerColor = '#b1b1b7', zoomOnScroll = true, zoomOnPinch = true, panOnScroll = false, panOnScrollSpeed = 0.5, panOnScrollMode = PanOnScrollMode.Free, zoomOnDoubleClick = true, panOnDrag = true, onPaneClick, onPaneMouseEnter, onPaneMouseMove, onPaneMouseLeave, onPaneScroll, onPaneContextMenu, children, onEdgeContextMenu, onEdgeDoubleClick, onEdgeMouseEnter, onEdgeMouseMove, onEdgeMouseLeave, onEdgeUpdate, onEdgeUpdateStart, onEdgeUpdateEnd, onReconnect, onReconnectStart, onReconnectEnd, reconnectRadius = 10, edgeUpdaterRadius = 10, onNodesChange, onEdgesChange, noDragClassName = 'nodrag', noWheelClassName = 'nowheel', noPanClassName = 'nopan', fitView = false, fitViewOptions, connectOnClick = true, attributionPosition, proOptions, defaultEdgeOptions, elevateNodesOnSelect = true, elevateEdgesOnSelect = false, disableKeyboardA11y = false, autoPanOnConnect = true, autoPanOnNodeDrag = true, connectionRadius = 20, isValidConnection, onError, style, id, nodeDragThreshold, ...rest }, ref) => {\n    const rfId = id || '1';\n    return (React.createElement(\"div\", { ...rest, style: { ...style, ...wrapperStyle }, ref: ref, className: cc(['react-flow', className]), \"data-testid\": \"rf__wrapper\", id: id },\n        React.createElement(Wrapper, null,\n            React.createElement(GraphView$1, { onInit: onInit, onMove: onMove, onMoveStart: onMoveStart, onMoveEnd: onMoveEnd, onNodeClick: onNodeClick, onEdgeClick: onEdgeClick, onNodeMouseEnter: onNodeMouseEnter, onNodeMouseMove: onNodeMouseMove, onNodeMouseLeave: onNodeMouseLeave, onNodeContextMenu: onNodeContextMenu, onNodeDoubleClick: onNodeDoubleClick, nodeTypes: nodeTypes, edgeTypes: edgeTypes, connectionLineType: connectionLineType, connectionLineStyle: connectionLineStyle, connectionLineComponent: connectionLineComponent, connectionLineContainerStyle: connectionLineContainerStyle, selectionKeyCode: selectionKeyCode, selectionOnDrag: selectionOnDrag, selectionMode: selectionMode, deleteKeyCode: deleteKeyCode, multiSelectionKeyCode: multiSelectionKeyCode, panActivationKeyCode: panActivationKeyCode, zoomActivationKeyCode: zoomActivationKeyCode, onlyRenderVisibleElements: onlyRenderVisibleElements, selectNodesOnDrag: selectNodesOnDrag, defaultViewport: defaultViewport, translateExtent: translateExtent, minZoom: minZoom, maxZoom: maxZoom, preventScrolling: preventScrolling, zoomOnScroll: zoomOnScroll, zoomOnPinch: zoomOnPinch, zoomOnDoubleClick: zoomOnDoubleClick, panOnScroll: panOnScroll, panOnScrollSpeed: panOnScrollSpeed, panOnScrollMode: panOnScrollMode, panOnDrag: panOnDrag, onPaneClick: onPaneClick, onPaneMouseEnter: onPaneMouseEnter, onPaneMouseMove: onPaneMouseMove, onPaneMouseLeave: onPaneMouseLeave, onPaneScroll: onPaneScroll, onPaneContextMenu: onPaneContextMenu, onSelectionContextMenu: onSelectionContextMenu, onSelectionStart: onSelectionStart, onSelectionEnd: onSelectionEnd, onEdgeContextMenu: onEdgeContextMenu, onEdgeDoubleClick: onEdgeDoubleClick, onEdgeMouseEnter: onEdgeMouseEnter, onEdgeMouseMove: onEdgeMouseMove, onEdgeMouseLeave: onEdgeMouseLeave, onReconnect: onReconnect ?? onEdgeUpdate, onReconnectStart: onReconnectStart ?? onEdgeUpdateStart, onReconnectEnd: onReconnectEnd ?? onEdgeUpdateEnd, reconnectRadius: reconnectRadius ?? edgeUpdaterRadius, defaultMarkerColor: defaultMarkerColor, noDragClassName: noDragClassName, noWheelClassName: noWheelClassName, noPanClassName: noPanClassName, elevateEdgesOnSelect: elevateEdgesOnSelect, rfId: rfId, disableKeyboardA11y: disableKeyboardA11y, nodeOrigin: nodeOrigin, nodeExtent: nodeExtent }),\n            React.createElement(StoreUpdater, { nodes: nodes, edges: edges, defaultNodes: defaultNodes, defaultEdges: defaultEdges, onConnect: onConnect, onConnectStart: onConnectStart, onConnectEnd: onConnectEnd, onClickConnectStart: onClickConnectStart, onClickConnectEnd: onClickConnectEnd, nodesDraggable: nodesDraggable, nodesConnectable: nodesConnectable, nodesFocusable: nodesFocusable, edgesFocusable: edgesFocusable, edgesUpdatable: edgesUpdatable, elementsSelectable: elementsSelectable, elevateNodesOnSelect: elevateNodesOnSelect, minZoom: minZoom, maxZoom: maxZoom, nodeExtent: nodeExtent, onNodesChange: onNodesChange, onEdgesChange: onEdgesChange, snapToGrid: snapToGrid, snapGrid: snapGrid, connectionMode: connectionMode, translateExtent: translateExtent, connectOnClick: connectOnClick, defaultEdgeOptions: defaultEdgeOptions, fitView: fitView, fitViewOptions: fitViewOptions, onNodesDelete: onNodesDelete, onEdgesDelete: onEdgesDelete, onNodeDragStart: onNodeDragStart, onNodeDrag: onNodeDrag, onNodeDragStop: onNodeDragStop, onSelectionDrag: onSelectionDrag, onSelectionDragStart: onSelectionDragStart, onSelectionDragStop: onSelectionDragStop, noPanClassName: noPanClassName, nodeOrigin: nodeOrigin, rfId: rfId, autoPanOnConnect: autoPanOnConnect, autoPanOnNodeDrag: autoPanOnNodeDrag, onError: onError, connectionRadius: connectionRadius, isValidConnection: isValidConnection, nodeDragThreshold: nodeDragThreshold }),\n            React.createElement(Wrapper$1, { onSelectionChange: onSelectionChange }),\n            children,\n            React.createElement(Attribution, { proOptions: proOptions, position: attributionPosition }),\n            React.createElement(A11yDescriptions, { rfId: rfId, disableKeyboardA11y: disableKeyboardA11y }))));\n});\nReactFlow.displayName = 'ReactFlow';\n\nconst selector$1 = (s) => s.domNode?.querySelector('.react-flow__edgelabel-renderer');\nfunction EdgeLabelRenderer({ children }) {\n    const edgeLabelRenderer = useStore(selector$1);\n    if (!edgeLabelRenderer) {\n        return null;\n    }\n    return createPortal(children, edgeLabelRenderer);\n}\n\nfunction useUpdateNodeInternals() {\n    const store = useStoreApi();\n    return useCallback((id) => {\n        const { domNode, updateNodeDimensions } = store.getState();\n        const updateIds = Array.isArray(id) ? id : [id];\n        const updates = updateIds.reduce((res, updateId) => {\n            const nodeElement = domNode?.querySelector(`.react-flow__node[data-id=\"${updateId}\"]`);\n            if (nodeElement) {\n                res.push({ id: updateId, nodeElement, forceUpdate: true });\n            }\n            return res;\n        }, []);\n        requestAnimationFrame(() => updateNodeDimensions(updates));\n    }, []);\n}\n\nconst nodesSelector = (state) => state.getNodes();\nfunction useNodes() {\n    const nodes = useStore(nodesSelector, shallow);\n    return nodes;\n}\n\nconst edgesSelector = (state) => state.edges;\nfunction useEdges() {\n    const edges = useStore(edgesSelector, shallow);\n    return edges;\n}\n\nconst viewportSelector = (state) => ({\n    x: state.transform[0],\n    y: state.transform[1],\n    zoom: state.transform[2],\n});\nfunction useViewport() {\n    const viewport = useStore(viewportSelector, shallow);\n    return viewport;\n}\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\nfunction createUseItemsState(applyChanges) {\n    return (initialItems) => {\n        const [items, setItems] = useState(initialItems);\n        const onItemsChange = useCallback((changes) => setItems((items) => applyChanges(changes, items)), []);\n        return [items, setItems, onItemsChange];\n    };\n}\nconst useNodesState = createUseItemsState(applyNodeChanges);\nconst useEdgesState = createUseItemsState(applyEdgeChanges);\n\nfunction useOnViewportChange({ onStart, onChange, onEnd }) {\n    const store = useStoreApi();\n    useEffect(() => {\n        store.setState({ onViewportChangeStart: onStart });\n    }, [onStart]);\n    useEffect(() => {\n        store.setState({ onViewportChange: onChange });\n    }, [onChange]);\n    useEffect(() => {\n        store.setState({ onViewportChangeEnd: onEnd });\n    }, [onEnd]);\n}\n\nfunction useOnSelectionChange({ onChange }) {\n    const store = useStoreApi();\n    useEffect(() => {\n        const nextSelectionChangeHandlers = [...store.getState().onSelectionChange, onChange];\n        store.setState({ onSelectionChange: nextSelectionChangeHandlers });\n        return () => {\n            const nextHandlers = store.getState().onSelectionChange.filter((fn) => fn !== onChange);\n            store.setState({ onSelectionChange: nextHandlers });\n        };\n    }, [onChange]);\n}\n\nconst selector = (options) => (s) => {\n    if (s.nodeInternals.size === 0) {\n        return false;\n    }\n    return s\n        .getNodes()\n        .filter((n) => (options.includeHiddenNodes ? true : !n.hidden))\n        .every((n) => n[internalsSymbol]?.handleBounds !== undefined);\n};\nconst defaultOptions = {\n    includeHiddenNodes: false,\n};\nfunction useNodesInitialized(options = defaultOptions) {\n    const initialized = useStore(selector(options));\n    return initialized;\n}\n\nexport { BaseEdge, BezierEdge, ConnectionLineType, ConnectionMode, EdgeLabelRenderer, EdgeText$1 as EdgeText, Handle$1 as Handle, MarkerType, PanOnScrollMode, Panel, Position, ReactFlow, ReactFlowProvider, SelectionMode, SimpleBezierEdge, SmoothStepEdge, StepEdge, StraightEdge, addEdge, applyEdgeChanges, applyNodeChanges, boxToRect, clamp, getBezierPath, getBoundsOfRects, getConnectedEdges, getIncomers, getMarkerEnd, getNodePositionWithOrigin, getNodesBounds, getOutgoers, getRectOfNodes, getSimpleBezierPath, getSmoothStepPath, getStraightPath, getTransformForBounds, getViewportForBounds, handleParentExpand, internalsSymbol, isEdge, isNode, reconnectEdge, rectToBox, updateEdge, useEdges, useEdgesState, useGetPointerPosition, useKeyPress, useNodeId, useNodes, useNodesInitialized, useNodesState, useOnSelectionChange, useOnViewportChange, useReactFlow, useStore, useStoreApi, useUpdateNodeInternals, useViewport };\n", "export default function cc(names) {\n  if (typeof names === \"string\" || typeof names === \"number\") return \"\" + names\n\n  let out = \"\"\n\n  if (Array.isArray(names)) {\n    for (let i = 0, tmp; i < names.length; i++) {\n      if ((tmp = cc(names[i])) !== \"\") {\n        out += (out && \" \") + tmp\n      }\n    }\n  } else {\n    for (let k in names) {\n      if (names[k]) out += (out && \" \") + k\n    }\n  }\n\n  return out\n}\n", "import ReactExports from 'react';\nimport useSyncExternalStoreExports from 'use-sync-external-store/shim/with-selector.js';\nimport { createStore } from 'zustand/vanilla';\n\nconst { useDebugValue } = ReactExports;\nconst { useSyncExternalStoreWithSelector } = useSyncExternalStoreExports;\nconst identity = (arg) => arg;\nfunction useStoreWithEqualityFn(api, selector = identity, equalityFn) {\n  const slice = useSyncExternalStoreWithSelector(\n    api.subscribe,\n    api.getState,\n    api.getServerState || api.getInitialState,\n    selector,\n    equalityFn\n  );\n  useDebugValue(slice);\n  return slice;\n}\nconst createWithEqualityFnImpl = (createState, defaultEqualityFn) => {\n  const api = createStore(createState);\n  const useBoundStoreWithEqualityFn = (selector, equalityFn = defaultEqualityFn) => useStoreWithEqualityFn(api, selector, equalityFn);\n  Object.assign(useBoundStoreWithEqualityFn, api);\n  return useBoundStoreWithEqualityFn;\n};\nconst createWithEqualityFn = (createState, defaultEqualityFn) => createState ? createWithEqualityFnImpl(createState, defaultEqualityFn) : createWithEqualityFnImpl;\n\nexport { createWithEqualityFn, useStoreWithEqualityFn };\n", "const createStoreImpl = (createState) => {\n  let state;\n  const listeners = /* @__PURE__ */ new Set();\n  const setState = (partial, replace) => {\n    const nextState = typeof partial === \"function\" ? partial(state) : partial;\n    if (!Object.is(nextState, state)) {\n      const previousState = state;\n      state = (replace != null ? replace : typeof nextState !== \"object\" || nextState === null) ? nextState : Object.assign({}, state, nextState);\n      listeners.forEach((listener) => listener(state, previousState));\n    }\n  };\n  const getState = () => state;\n  const getInitialState = () => initialState;\n  const subscribe = (listener) => {\n    listeners.add(listener);\n    return () => listeners.delete(listener);\n  };\n  const destroy = () => {\n    if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") {\n      console.warn(\n        \"[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected.\"\n      );\n    }\n    listeners.clear();\n  };\n  const api = { setState, getState, getInitialState, subscribe, destroy };\n  const initialState = state = createState(setState, getState, api);\n  return api;\n};\nconst createStore = (createState) => createState ? createStoreImpl(createState) : createStoreImpl;\nvar vanilla = (createState) => {\n  if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") {\n    console.warn(\n      \"[DEPRECATED] Default export is deprecated. Instead use import { createStore } from 'zustand/vanilla'.\"\n    );\n  }\n  return createStore(createState);\n};\n\nexport { createStore, vanilla as default };\n", "function shallow$1(objA, objB) {\n  if (Object.is(objA, objB)) {\n    return true;\n  }\n  if (typeof objA !== \"object\" || objA === null || typeof objB !== \"object\" || objB === null) {\n    return false;\n  }\n  if (objA instanceof Map && objB instanceof Map) {\n    if (objA.size !== objB.size) return false;\n    for (const [key, value] of objA) {\n      if (!Object.is(value, objB.get(key))) {\n        return false;\n      }\n    }\n    return true;\n  }\n  if (objA instanceof Set && objB instanceof Set) {\n    if (objA.size !== objB.size) return false;\n    for (const value of objA) {\n      if (!objB.has(value)) {\n        return false;\n      }\n    }\n    return true;\n  }\n  const keysA = Object.keys(objA);\n  if (keysA.length !== Object.keys(objB).length) {\n    return false;\n  }\n  for (const keyA of keysA) {\n    if (!Object.prototype.hasOwnProperty.call(objB, keyA) || !Object.is(objA[keyA], objB[keyA])) {\n      return false;\n    }\n  }\n  return true;\n}\n\nvar shallow = (objA, objB) => {\n  if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") {\n    console.warn(\n      \"[DEPRECATED] Default export is deprecated. Instead use `import { shallow } from 'zustand/shallow'`.\"\n    );\n  }\n  return shallow$1(objA, objB);\n};\n\nexport { shallow as default, shallow$1 as shallow };\n", "import React, { memo, useRef, useEffect } from 'react';\nimport cc from 'classcat';\nimport { shallow } from 'zustand/shallow';\nimport { zoom, zoomIdentity } from 'd3-zoom';\nimport { select, pointer } from 'd3-selection';\nimport { useStore, getNodePositionWithOrigin, useStoreApi, Panel, getBoundsOfRects, getNodesBounds } from '@reactflow/core';\n\nconst MiniMapNode = ({ id, x, y, width, height, style, color, strokeColor, strokeWidth, className, borderRadius, shapeRendering, onClick, selected, }) => {\n    const { background, backgroundColor } = style || {};\n    const fill = (color || background || backgroundColor);\n    return (React.createElement(\"rect\", { className: cc(['react-flow__minimap-node', { selected }, className]), x: x, y: y, rx: borderRadius, ry: borderRadius, width: width, height: height, fill: fill, stroke: strokeColor, strokeWidth: strokeWidth, shapeRendering: shapeRendering, onClick: onClick ? (event) => onClick(event, id) : undefined }));\n};\nMiniMapNode.displayName = 'MiniMapNode';\nvar MiniMapNode$1 = memo(MiniMapNode);\n\n/* eslint-disable @typescript-eslint/ban-ts-comment */\nconst selector$1 = (s) => s.nodeOrigin;\nconst selectorNodes = (s) => s.getNodes().filter((node) => !node.hidden && node.width && node.height);\nconst getAttrFunction = (func) => (func instanceof Function ? func : () => func);\nfunction MiniMapNodes({ nodeStrokeColor = 'transparent', nodeColor = '#e2e2e2', nodeClassName = '', nodeBorderRadius = 5, nodeStrokeWidth = 2, \n// We need to rename the prop to be `CapitalCase` so that JSX will render it as\n// a component properly.\nnodeComponent: NodeComponent = MiniMapNode$1, onClick, }) {\n    const nodes = useStore(selectorNodes, shallow);\n    const nodeOrigin = useStore(selector$1);\n    const nodeColorFunc = getAttrFunction(nodeColor);\n    const nodeStrokeColorFunc = getAttrFunction(nodeStrokeColor);\n    const nodeClassNameFunc = getAttrFunction(nodeClassName);\n    const shapeRendering = typeof window === 'undefined' || !!window.chrome ? 'crispEdges' : 'geometricPrecision';\n    return (React.createElement(React.Fragment, null, nodes.map((node) => {\n        const { x, y } = getNodePositionWithOrigin(node, nodeOrigin).positionAbsolute;\n        return (React.createElement(NodeComponent, { key: node.id, x: x, y: y, width: node.width, height: node.height, style: node.style, selected: node.selected, className: nodeClassNameFunc(node), color: nodeColorFunc(node), borderRadius: nodeBorderRadius, strokeColor: nodeStrokeColorFunc(node), strokeWidth: nodeStrokeWidth, shapeRendering: shapeRendering, onClick: onClick, id: node.id }));\n    })));\n}\nvar MiniMapNodes$1 = memo(MiniMapNodes);\n\n/* eslint-disable @typescript-eslint/ban-ts-comment */\nconst defaultWidth = 200;\nconst defaultHeight = 150;\nconst selector = (s) => {\n    const nodes = s.getNodes();\n    const viewBB = {\n        x: -s.transform[0] / s.transform[2],\n        y: -s.transform[1] / s.transform[2],\n        width: s.width / s.transform[2],\n        height: s.height / s.transform[2],\n    };\n    return {\n        viewBB,\n        boundingRect: nodes.length > 0 ? getBoundsOfRects(getNodesBounds(nodes, s.nodeOrigin), viewBB) : viewBB,\n        rfId: s.rfId,\n    };\n};\nconst ARIA_LABEL_KEY = 'react-flow__minimap-desc';\nfunction MiniMap({ style, className, nodeStrokeColor = 'transparent', nodeColor = '#e2e2e2', nodeClassName = '', nodeBorderRadius = 5, nodeStrokeWidth = 2, \n// We need to rename the prop to be `CapitalCase` so that JSX will render it as\n// a component properly.\nnodeComponent, maskColor = 'rgb(240, 240, 240, 0.6)', maskStrokeColor = 'none', maskStrokeWidth = 1, position = 'bottom-right', onClick, onNodeClick, pannable = false, zoomable = false, ariaLabel = 'React Flow mini map', inversePan = false, zoomStep = 10, offsetScale = 5, }) {\n    const store = useStoreApi();\n    const svg = useRef(null);\n    const { boundingRect, viewBB, rfId } = useStore(selector, shallow);\n    const elementWidth = style?.width ?? defaultWidth;\n    const elementHeight = style?.height ?? defaultHeight;\n    const scaledWidth = boundingRect.width / elementWidth;\n    const scaledHeight = boundingRect.height / elementHeight;\n    const viewScale = Math.max(scaledWidth, scaledHeight);\n    const viewWidth = viewScale * elementWidth;\n    const viewHeight = viewScale * elementHeight;\n    const offset = offsetScale * viewScale;\n    const x = boundingRect.x - (viewWidth - boundingRect.width) / 2 - offset;\n    const y = boundingRect.y - (viewHeight - boundingRect.height) / 2 - offset;\n    const width = viewWidth + offset * 2;\n    const height = viewHeight + offset * 2;\n    const labelledBy = `${ARIA_LABEL_KEY}-${rfId}`;\n    const viewScaleRef = useRef(0);\n    viewScaleRef.current = viewScale;\n    useEffect(() => {\n        if (svg.current) {\n            const selection = select(svg.current);\n            const zoomHandler = (event) => {\n                const { transform, d3Selection, d3Zoom } = store.getState();\n                if (event.sourceEvent.type !== 'wheel' || !d3Selection || !d3Zoom) {\n                    return;\n                }\n                const pinchDelta = -event.sourceEvent.deltaY *\n                    (event.sourceEvent.deltaMode === 1 ? 0.05 : event.sourceEvent.deltaMode ? 1 : 0.002) *\n                    zoomStep;\n                const zoom = transform[2] * Math.pow(2, pinchDelta);\n                d3Zoom.scaleTo(d3Selection, zoom);\n            };\n            const panHandler = (event) => {\n                const { transform, d3Selection, d3Zoom, translateExtent, width, height } = store.getState();\n                if (event.sourceEvent.type !== 'mousemove' || !d3Selection || !d3Zoom) {\n                    return;\n                }\n                // @TODO: how to calculate the correct next position? Math.max(1, transform[2]) is a workaround.\n                const moveScale = viewScaleRef.current * Math.max(1, transform[2]) * (inversePan ? -1 : 1);\n                const position = {\n                    x: transform[0] - event.sourceEvent.movementX * moveScale,\n                    y: transform[1] - event.sourceEvent.movementY * moveScale,\n                };\n                const extent = [\n                    [0, 0],\n                    [width, height],\n                ];\n                const nextTransform = zoomIdentity.translate(position.x, position.y).scale(transform[2]);\n                const constrainedTransform = d3Zoom.constrain()(nextTransform, extent, translateExtent);\n                d3Zoom.transform(d3Selection, constrainedTransform);\n            };\n            const zoomAndPanHandler = zoom()\n                // @ts-ignore\n                .on('zoom', pannable ? panHandler : null)\n                // @ts-ignore\n                .on('zoom.wheel', zoomable ? zoomHandler : null);\n            selection.call(zoomAndPanHandler);\n            return () => {\n                selection.on('zoom', null);\n            };\n        }\n    }, [pannable, zoomable, inversePan, zoomStep]);\n    const onSvgClick = onClick\n        ? (event) => {\n            const rfCoord = pointer(event);\n            onClick(event, { x: rfCoord[0], y: rfCoord[1] });\n        }\n        : undefined;\n    const onSvgNodeClick = onNodeClick\n        ? (event, nodeId) => {\n            const node = store.getState().nodeInternals.get(nodeId);\n            onNodeClick(event, node);\n        }\n        : undefined;\n    return (React.createElement(Panel, { position: position, style: style, className: cc(['react-flow__minimap', className]), \"data-testid\": \"rf__minimap\" },\n        React.createElement(\"svg\", { width: elementWidth, height: elementHeight, viewBox: `${x} ${y} ${width} ${height}`, role: \"img\", \"aria-labelledby\": labelledBy, ref: svg, onClick: onSvgClick },\n            ariaLabel && React.createElement(\"title\", { id: labelledBy }, ariaLabel),\n            React.createElement(MiniMapNodes$1, { onClick: onSvgNodeClick, nodeColor: nodeColor, nodeStrokeColor: nodeStrokeColor, nodeBorderRadius: nodeBorderRadius, nodeClassName: nodeClassName, nodeStrokeWidth: nodeStrokeWidth, nodeComponent: nodeComponent }),\n            React.createElement(\"path\", { className: \"react-flow__minimap-mask\", d: `M${x - offset},${y - offset}h${width + offset * 2}v${height + offset * 2}h${-width - offset * 2}z\n        M${viewBB.x},${viewBB.y}h${viewBB.width}v${viewBB.height}h${-viewBB.width}z`, fill: maskColor, fillRule: \"evenodd\", stroke: maskStrokeColor, strokeWidth: maskStrokeWidth, pointerEvents: \"none\" }))));\n}\nMiniMap.displayName = 'MiniMap';\nvar MiniMap$1 = memo(MiniMap);\n\nexport { MiniMap$1 as MiniMap };\n", "function shallow$1(objA, objB) {\n  if (Object.is(objA, objB)) {\n    return true;\n  }\n  if (typeof objA !== \"object\" || objA === null || typeof objB !== \"object\" || objB === null) {\n    return false;\n  }\n  if (objA instanceof Map && objB instanceof Map) {\n    if (objA.size !== objB.size) return false;\n    for (const [key, value] of objA) {\n      if (!Object.is(value, objB.get(key))) {\n        return false;\n      }\n    }\n    return true;\n  }\n  if (objA instanceof Set && objB instanceof Set) {\n    if (objA.size !== objB.size) return false;\n    for (const value of objA) {\n      if (!objB.has(value)) {\n        return false;\n      }\n    }\n    return true;\n  }\n  const keysA = Object.keys(objA);\n  if (keysA.length !== Object.keys(objB).length) {\n    return false;\n  }\n  for (const keyA of keysA) {\n    if (!Object.prototype.hasOwnProperty.call(objB, keyA) || !Object.is(objA[keyA], objB[keyA])) {\n      return false;\n    }\n  }\n  return true;\n}\n\nvar shallow = (objA, objB) => {\n  if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") {\n    console.warn(\n      \"[DEPRECATED] Default export is deprecated. Instead use `import { shallow } from 'zustand/shallow'`.\"\n    );\n  }\n  return shallow$1(objA, objB);\n};\n\nexport { shallow as default, shallow$1 as shallow };\n", "import React, { memo, useState, useEffect } from 'react';\nimport cc from 'classcat';\nimport { shallow } from 'zustand/shallow';\nimport { useStore<PERSON>pi, useStore, useReactFlow, Panel } from '@reactflow/core';\n\nfunction PlusIcon() {\n    return (React.createElement(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", viewBox: \"0 0 32 32\" },\n        React.createElement(\"path\", { d: \"M32 18.133H18.133V32h-4.266V18.133H0v-4.266h13.867V0h4.266v13.867H32z\" })));\n}\n\nfunction MinusIcon() {\n    return (React.createElement(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", viewBox: \"0 0 32 5\" },\n        React.createElement(\"path\", { d: \"M0 0h32v4.2H0z\" })));\n}\n\nfunction FitViewIcon() {\n    return (React.createElement(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", viewBox: \"0 0 32 30\" },\n        React.createElement(\"path\", { d: \"M3.692 4.63c0-.53.4-.938.939-.938h5.215V0H4.708C2.13 0 0 2.054 0 4.63v5.216h3.692V4.631zM27.354 0h-5.2v3.692h5.17c.53 0 .984.4.984.939v5.215H32V4.631A4.624 4.624 0 0027.354 0zm.954 24.83c0 .532-.4.94-.939.94h-5.215v3.768h5.215c2.577 0 4.631-2.13 4.631-4.707v-5.139h-3.692v5.139zm-23.677.94c-.531 0-.939-.4-.939-.94v-5.138H0v5.139c0 2.577 2.13 4.707 4.708 4.707h5.138V25.77H4.631z\" })));\n}\n\nfunction LockIcon() {\n    return (React.createElement(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", viewBox: \"0 0 25 32\" },\n        React.createElement(\"path\", { d: \"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0 8 0 4.571 3.429 4.571 7.619v3.048H3.048A3.056 3.056 0 000 13.714v15.238A3.056 3.056 0 003.048 32h18.285a3.056 3.056 0 003.048-3.048V13.714a3.056 3.056 0 00-3.048-3.047zM12.19 24.533a3.056 3.056 0 01-3.047-3.047 3.056 3.056 0 013.047-3.048 3.056 3.056 0 013.048 3.048 3.056 3.056 0 01-3.048 3.047zm4.724-13.866H7.467V7.619c0-2.59 2.133-4.724 4.723-4.724 2.591 0 4.724 2.133 4.724 4.724v3.048z\" })));\n}\n\nfunction UnlockIcon() {\n    return (React.createElement(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", viewBox: \"0 0 25 32\" },\n        React.createElement(\"path\", { d: \"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0c-4.114 1.828-1.37 2.133.305 2.438 1.676.305 4.42 2.59 4.42 5.181v3.048H3.047A3.056 3.056 0 000 13.714v15.238A3.056 3.056 0 003.048 32h18.285a3.056 3.056 0 003.048-3.048V13.714a3.056 3.056 0 00-3.048-3.047zM12.19 24.533a3.056 3.056 0 01-3.047-3.047 3.056 3.056 0 013.047-3.048 3.056 3.056 0 013.048 3.048 3.056 3.056 0 01-3.048 3.047z\" })));\n}\n\nconst ControlButton = ({ children, className, ...rest }) => (React.createElement(\"button\", { type: \"button\", className: cc(['react-flow__controls-button', className]), ...rest }, children));\nControlButton.displayName = 'ControlButton';\n\nconst selector = (s) => ({\n    isInteractive: s.nodesDraggable || s.nodesConnectable || s.elementsSelectable,\n    minZoomReached: s.transform[2] <= s.minZoom,\n    maxZoomReached: s.transform[2] >= s.maxZoom,\n});\nconst Controls = ({ style, showZoom = true, showFitView = true, showInteractive = true, fitViewOptions, onZoomIn, onZoomOut, onFitView, onInteractiveChange, className, children, position = 'bottom-left', }) => {\n    const store = useStoreApi();\n    const [isVisible, setIsVisible] = useState(false);\n    const { isInteractive, minZoomReached, maxZoomReached } = useStore(selector, shallow);\n    const { zoomIn, zoomOut, fitView } = useReactFlow();\n    useEffect(() => {\n        setIsVisible(true);\n    }, []);\n    if (!isVisible) {\n        return null;\n    }\n    const onZoomInHandler = () => {\n        zoomIn();\n        onZoomIn?.();\n    };\n    const onZoomOutHandler = () => {\n        zoomOut();\n        onZoomOut?.();\n    };\n    const onFitViewHandler = () => {\n        fitView(fitViewOptions);\n        onFitView?.();\n    };\n    const onToggleInteractivity = () => {\n        store.setState({\n            nodesDraggable: !isInteractive,\n            nodesConnectable: !isInteractive,\n            elementsSelectable: !isInteractive,\n        });\n        onInteractiveChange?.(!isInteractive);\n    };\n    return (React.createElement(Panel, { className: cc(['react-flow__controls', className]), position: position, style: style, \"data-testid\": \"rf__controls\" },\n        showZoom && (React.createElement(React.Fragment, null,\n            React.createElement(ControlButton, { onClick: onZoomInHandler, className: \"react-flow__controls-zoomin\", title: \"zoom in\", \"aria-label\": \"zoom in\", disabled: maxZoomReached },\n                React.createElement(PlusIcon, null)),\n            React.createElement(ControlButton, { onClick: onZoomOutHandler, className: \"react-flow__controls-zoomout\", title: \"zoom out\", \"aria-label\": \"zoom out\", disabled: minZoomReached },\n                React.createElement(MinusIcon, null)))),\n        showFitView && (React.createElement(ControlButton, { className: \"react-flow__controls-fitview\", onClick: onFitViewHandler, title: \"fit view\", \"aria-label\": \"fit view\" },\n            React.createElement(FitViewIcon, null))),\n        showInteractive && (React.createElement(ControlButton, { className: \"react-flow__controls-interactive\", onClick: onToggleInteractivity, title: \"toggle interactivity\", \"aria-label\": \"toggle interactivity\" }, isInteractive ? React.createElement(UnlockIcon, null) : React.createElement(LockIcon, null))),\n        children));\n};\nControls.displayName = 'Controls';\nvar Controls$1 = memo(Controls);\n\nexport { ControlButton, Controls$1 as Controls };\n", "function shallow$1(objA, objB) {\n  if (Object.is(objA, objB)) {\n    return true;\n  }\n  if (typeof objA !== \"object\" || objA === null || typeof objB !== \"object\" || objB === null) {\n    return false;\n  }\n  if (objA instanceof Map && objB instanceof Map) {\n    if (objA.size !== objB.size) return false;\n    for (const [key, value] of objA) {\n      if (!Object.is(value, objB.get(key))) {\n        return false;\n      }\n    }\n    return true;\n  }\n  if (objA instanceof Set && objB instanceof Set) {\n    if (objA.size !== objB.size) return false;\n    for (const value of objA) {\n      if (!objB.has(value)) {\n        return false;\n      }\n    }\n    return true;\n  }\n  const keysA = Object.keys(objA);\n  if (keysA.length !== Object.keys(objB).length) {\n    return false;\n  }\n  for (const keyA of keysA) {\n    if (!Object.prototype.hasOwnProperty.call(objB, keyA) || !Object.is(objA[keyA], objB[keyA])) {\n      return false;\n    }\n  }\n  return true;\n}\n\nvar shallow = (objA, objB) => {\n  if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") {\n    console.warn(\n      \"[DEPRECATED] Default export is deprecated. Instead use `import { shallow } from 'zustand/shallow'`.\"\n    );\n  }\n  return shallow$1(objA, objB);\n};\n\nexport { shallow as default, shallow$1 as shallow };\n", "import React, { memo, useRef } from 'react';\nimport cc from 'classcat';\nimport { useStore } from '@reactflow/core';\nimport { shallow } from 'zustand/shallow';\n\nvar BackgroundVariant;\n(function (BackgroundVariant) {\n    BackgroundVariant[\"Lines\"] = \"lines\";\n    BackgroundVariant[\"Dots\"] = \"dots\";\n    BackgroundVariant[\"Cross\"] = \"cross\";\n})(BackgroundVariant || (BackgroundVariant = {}));\n\nfunction LinePattern({ color, dimensions, lineWidth }) {\n    return (React.createElement(\"path\", { stroke: color, strokeWidth: lineWidth, d: `M${dimensions[0] / 2} 0 V${dimensions[1]} M0 ${dimensions[1] / 2} H${dimensions[0]}` }));\n}\nfunction DotPattern({ color, radius }) {\n    return React.createElement(\"circle\", { cx: radius, cy: radius, r: radius, fill: color });\n}\n\nconst defaultColor = {\n    [BackgroundVariant.Dots]: '#91919a',\n    [BackgroundVariant.Lines]: '#eee',\n    [BackgroundVariant.Cross]: '#e2e2e2',\n};\nconst defaultSize = {\n    [BackgroundVariant.Dots]: 1,\n    [BackgroundVariant.Lines]: 1,\n    [BackgroundVariant.Cross]: 6,\n};\nconst selector = (s) => ({ transform: s.transform, patternId: `pattern-${s.rfId}` });\nfunction Background({ id, variant = BackgroundVariant.Dots, \n// only used for dots and cross\ngap = 20, \n// only used for lines and cross\nsize, lineWidth = 1, offset = 2, color, style, className, }) {\n    const ref = useRef(null);\n    const { transform, patternId } = useStore(selector, shallow);\n    const patternColor = color || defaultColor[variant];\n    const patternSize = size || defaultSize[variant];\n    const isDots = variant === BackgroundVariant.Dots;\n    const isCross = variant === BackgroundVariant.Cross;\n    const gapXY = Array.isArray(gap) ? gap : [gap, gap];\n    const scaledGap = [gapXY[0] * transform[2] || 1, gapXY[1] * transform[2] || 1];\n    const scaledSize = patternSize * transform[2];\n    const patternDimensions = isCross ? [scaledSize, scaledSize] : scaledGap;\n    const patternOffset = isDots\n        ? [scaledSize / offset, scaledSize / offset]\n        : [patternDimensions[0] / offset, patternDimensions[1] / offset];\n    return (React.createElement(\"svg\", { className: cc(['react-flow__background', className]), style: {\n            ...style,\n            position: 'absolute',\n            width: '100%',\n            height: '100%',\n            top: 0,\n            left: 0,\n        }, ref: ref, \"data-testid\": \"rf__background\" },\n        React.createElement(\"pattern\", { id: patternId + id, x: transform[0] % scaledGap[0], y: transform[1] % scaledGap[1], width: scaledGap[0], height: scaledGap[1], patternUnits: \"userSpaceOnUse\", patternTransform: `translate(-${patternOffset[0]},-${patternOffset[1]})` }, isDots ? (React.createElement(DotPattern, { color: patternColor, radius: scaledSize / offset })) : (React.createElement(LinePattern, { dimensions: patternDimensions, color: patternColor, lineWidth: lineWidth }))),\n        React.createElement(\"rect\", { x: \"0\", y: \"0\", width: \"100%\", height: \"100%\", fill: `url(#${patternId + id})` })));\n}\nBackground.displayName = 'Background';\nvar Background$1 = memo(Background);\n\nexport { Background$1 as Background, BackgroundVariant };\n", "function shallow$1(objA, objB) {\n  if (Object.is(objA, objB)) {\n    return true;\n  }\n  if (typeof objA !== \"object\" || objA === null || typeof objB !== \"object\" || objB === null) {\n    return false;\n  }\n  if (objA instanceof Map && objB instanceof Map) {\n    if (objA.size !== objB.size) return false;\n    for (const [key, value] of objA) {\n      if (!Object.is(value, objB.get(key))) {\n        return false;\n      }\n    }\n    return true;\n  }\n  if (objA instanceof Set && objB instanceof Set) {\n    if (objA.size !== objB.size) return false;\n    for (const value of objA) {\n      if (!objB.has(value)) {\n        return false;\n      }\n    }\n    return true;\n  }\n  const keysA = Object.keys(objA);\n  if (keysA.length !== Object.keys(objB).length) {\n    return false;\n  }\n  for (const keyA of keysA) {\n    if (!Object.prototype.hasOwnProperty.call(objB, keyA) || !Object.is(objA[keyA], objB[keyA])) {\n      return false;\n    }\n  }\n  return true;\n}\n\nvar shallow = (objA, objB) => {\n  if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") {\n    console.warn(\n      \"[DEPRECATED] Default export is deprecated. Instead use `import { shallow } from 'zustand/shallow'`.\"\n    );\n  }\n  return shallow$1(objA, objB);\n};\n\nexport { shallow as default, shallow$1 as shallow };\n", "import React, { useCallback } from 'react';\nimport { useStore, useNodeId, getNodesBounds, internalsSymbol, Position } from '@reactflow/core';\nimport cc from 'classcat';\nimport { shallow } from 'zustand/shallow';\nimport { createPortal } from 'react-dom';\n\nconst selector = (state) => state.domNode?.querySelector('.react-flow__renderer');\nfunction NodeToolbarPortal({ children }) {\n    const wrapperRef = useStore(selector);\n    if (!wrapperRef) {\n        return null;\n    }\n    return createPortal(children, wrapperRef);\n}\n\nconst nodeEqualityFn = (a, b) => a?.positionAbsolute?.x === b?.positionAbsolute?.x &&\n    a?.positionAbsolute?.y === b?.positionAbsolute?.y &&\n    a?.width === b?.width &&\n    a?.height === b?.height &&\n    a?.selected === b?.selected &&\n    a?.[internalsSymbol]?.z === b?.[internalsSymbol]?.z;\nconst nodesEqualityFn = (a, b) => {\n    return a.length === b.length && a.every((node, i) => nodeEqualityFn(node, b[i]));\n};\nconst storeSelector = (state) => ({\n    transform: state.transform,\n    nodeOrigin: state.nodeOrigin,\n    selectedNodesCount: state.getNodes().filter((node) => node.selected).length,\n});\nfunction getTransform(nodeRect, transform, position, offset, align) {\n    let alignmentOffset = 0.5;\n    if (align === 'start') {\n        alignmentOffset = 0;\n    }\n    else if (align === 'end') {\n        alignmentOffset = 1;\n    }\n    // position === Position.Top\n    // we set the x any y position of the toolbar based on the nodes position\n    let pos = [\n        (nodeRect.x + nodeRect.width * alignmentOffset) * transform[2] + transform[0],\n        nodeRect.y * transform[2] + transform[1] - offset,\n    ];\n    // and than shift it based on the alignment. The shift values are in %.\n    let shift = [-100 * alignmentOffset, -100];\n    switch (position) {\n        case Position.Right:\n            pos = [\n                (nodeRect.x + nodeRect.width) * transform[2] + transform[0] + offset,\n                (nodeRect.y + nodeRect.height * alignmentOffset) * transform[2] + transform[1],\n            ];\n            shift = [0, -100 * alignmentOffset];\n            break;\n        case Position.Bottom:\n            pos[1] = (nodeRect.y + nodeRect.height) * transform[2] + transform[1] + offset;\n            shift[1] = 0;\n            break;\n        case Position.Left:\n            pos = [\n                nodeRect.x * transform[2] + transform[0] - offset,\n                (nodeRect.y + nodeRect.height * alignmentOffset) * transform[2] + transform[1],\n            ];\n            shift = [-100, -100 * alignmentOffset];\n            break;\n    }\n    return `translate(${pos[0]}px, ${pos[1]}px) translate(${shift[0]}%, ${shift[1]}%)`;\n}\nfunction NodeToolbar({ nodeId, children, className, style, isVisible, position = Position.Top, offset = 10, align = 'center', ...rest }) {\n    const contextNodeId = useNodeId();\n    const nodesSelector = useCallback((state) => {\n        const nodeIds = Array.isArray(nodeId) ? nodeId : [nodeId || contextNodeId || ''];\n        return nodeIds.reduce((acc, id) => {\n            const node = state.nodeInternals.get(id);\n            if (node) {\n                acc.push(node);\n            }\n            return acc;\n        }, []);\n    }, [nodeId, contextNodeId]);\n    const nodes = useStore(nodesSelector, nodesEqualityFn);\n    const { transform, nodeOrigin, selectedNodesCount } = useStore(storeSelector, shallow);\n    const isActive = typeof isVisible === 'boolean' ? isVisible : nodes.length === 1 && nodes[0].selected && selectedNodesCount === 1;\n    if (!isActive || !nodes.length) {\n        return null;\n    }\n    const nodeRect = getNodesBounds(nodes, nodeOrigin);\n    const zIndex = Math.max(...nodes.map((node) => (node[internalsSymbol]?.z || 1) + 1));\n    const wrapperStyle = {\n        position: 'absolute',\n        transform: getTransform(nodeRect, transform, position, offset, align),\n        zIndex,\n        ...style,\n    };\n    return (React.createElement(NodeToolbarPortal, null,\n        React.createElement(\"div\", { style: wrapperStyle, className: cc(['react-flow__node-toolbar', className]), ...rest }, children)));\n}\n\nexport { NodeToolbar };\n", "function shallow$1(objA, objB) {\n  if (Object.is(objA, objB)) {\n    return true;\n  }\n  if (typeof objA !== \"object\" || objA === null || typeof objB !== \"object\" || objB === null) {\n    return false;\n  }\n  if (objA instanceof Map && objB instanceof Map) {\n    if (objA.size !== objB.size) return false;\n    for (const [key, value] of objA) {\n      if (!Object.is(value, objB.get(key))) {\n        return false;\n      }\n    }\n    return true;\n  }\n  if (objA instanceof Set && objB instanceof Set) {\n    if (objA.size !== objB.size) return false;\n    for (const value of objA) {\n      if (!objB.has(value)) {\n        return false;\n      }\n    }\n    return true;\n  }\n  const keysA = Object.keys(objA);\n  if (keysA.length !== Object.keys(objB).length) {\n    return false;\n  }\n  for (const keyA of keysA) {\n    if (!Object.prototype.hasOwnProperty.call(objB, keyA) || !Object.is(objA[keyA], objB[keyA])) {\n      return false;\n    }\n  }\n  return true;\n}\n\nvar shallow = (objA, objB) => {\n  if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") {\n    console.warn(\n      \"[DEPRECATED] Default export is deprecated. Instead use `import { shallow } from 'zustand/shallow'`.\"\n    );\n  }\n  return shallow$1(objA, objB);\n};\n\nexport { shallow as default, shallow$1 as shallow };\n", "import React, { memo, useRef, useEffect } from 'react';\nimport cc from 'classcat';\nimport { drag } from 'd3-drag';\nimport { select } from 'd3-selection';\nimport { useNodeId, useStoreApi, useGetPointerPosition, clamp } from '@reactflow/core';\n\nvar ResizeControlVariant;\n(function (ResizeControlVariant) {\n    ResizeControlVariant[\"Line\"] = \"line\";\n    ResizeControlVariant[\"Handle\"] = \"handle\";\n})(ResizeControlVariant || (ResizeControlVariant = {}));\n\n// returns an array of two numbers (0, 1 or -1) representing the direction of the resize\n// 0 = no change, 1 = increase, -1 = decrease\nfunction getDirection({ width, prevWidth, height, prevHeight, invertX, invertY }) {\n    const deltaWidth = width - prevWidth;\n    const deltaHeight = height - prevHeight;\n    const direction = [deltaWidth > 0 ? 1 : deltaWidth < 0 ? -1 : 0, deltaHeight > 0 ? 1 : deltaHeight < 0 ? -1 : 0];\n    if (deltaWidth && invertX) {\n        direction[0] = direction[0] * -1;\n    }\n    if (deltaHeight && invertY) {\n        direction[1] = direction[1] * -1;\n    }\n    return direction;\n}\n\nconst initPrevValues = { width: 0, height: 0, x: 0, y: 0 };\nconst initStartValues = {\n    ...initPrevValues,\n    pointerX: 0,\n    pointerY: 0,\n    aspectRatio: 1,\n};\nfunction ResizeControl({ nodeId, position, variant = ResizeControlVariant.Handle, className, style = {}, children, color, minWidth = 10, minHeight = 10, maxWidth = Number.MAX_VALUE, maxHeight = Number.MAX_VALUE, keepAspectRatio = false, shouldResize, onResizeStart, onResize, onResizeEnd, }) {\n    const contextNodeId = useNodeId();\n    const id = typeof nodeId === 'string' ? nodeId : contextNodeId;\n    const store = useStoreApi();\n    const resizeControlRef = useRef(null);\n    const startValues = useRef(initStartValues);\n    const prevValues = useRef(initPrevValues);\n    const getPointerPosition = useGetPointerPosition();\n    const defaultPosition = variant === ResizeControlVariant.Line ? 'right' : 'bottom-right';\n    const controlPosition = position ?? defaultPosition;\n    useEffect(() => {\n        if (!resizeControlRef.current || !id) {\n            return;\n        }\n        const selection = select(resizeControlRef.current);\n        const enableX = controlPosition.includes('right') || controlPosition.includes('left');\n        const enableY = controlPosition.includes('bottom') || controlPosition.includes('top');\n        const invertX = controlPosition.includes('left');\n        const invertY = controlPosition.includes('top');\n        const dragHandler = drag()\n            .on('start', (event) => {\n            const node = store.getState().nodeInternals.get(id);\n            const { xSnapped, ySnapped } = getPointerPosition(event);\n            prevValues.current = {\n                width: node?.width ?? 0,\n                height: node?.height ?? 0,\n                x: node?.position.x ?? 0,\n                y: node?.position.y ?? 0,\n            };\n            startValues.current = {\n                ...prevValues.current,\n                pointerX: xSnapped,\n                pointerY: ySnapped,\n                aspectRatio: prevValues.current.width / prevValues.current.height,\n            };\n            onResizeStart?.(event, { ...prevValues.current });\n        })\n            .on('drag', (event) => {\n            const { nodeInternals, triggerNodeChanges } = store.getState();\n            const { xSnapped, ySnapped } = getPointerPosition(event);\n            const node = nodeInternals.get(id);\n            if (node) {\n                const changes = [];\n                const { pointerX: startX, pointerY: startY, width: startWidth, height: startHeight, x: startNodeX, y: startNodeY, aspectRatio, } = startValues.current;\n                const { x: prevX, y: prevY, width: prevWidth, height: prevHeight } = prevValues.current;\n                const distX = Math.floor(enableX ? xSnapped - startX : 0);\n                const distY = Math.floor(enableY ? ySnapped - startY : 0);\n                let width = clamp(startWidth + (invertX ? -distX : distX), minWidth, maxWidth);\n                let height = clamp(startHeight + (invertY ? -distY : distY), minHeight, maxHeight);\n                if (keepAspectRatio) {\n                    const nextAspectRatio = width / height;\n                    const isDiagonal = enableX && enableY;\n                    const isHorizontal = enableX && !enableY;\n                    const isVertical = enableY && !enableX;\n                    width = (nextAspectRatio <= aspectRatio && isDiagonal) || isVertical ? height * aspectRatio : width;\n                    height = (nextAspectRatio > aspectRatio && isDiagonal) || isHorizontal ? width / aspectRatio : height;\n                    if (width >= maxWidth) {\n                        width = maxWidth;\n                        height = maxWidth / aspectRatio;\n                    }\n                    else if (width <= minWidth) {\n                        width = minWidth;\n                        height = minWidth / aspectRatio;\n                    }\n                    if (height >= maxHeight) {\n                        height = maxHeight;\n                        width = maxHeight * aspectRatio;\n                    }\n                    else if (height <= minHeight) {\n                        height = minHeight;\n                        width = minHeight * aspectRatio;\n                    }\n                }\n                const isWidthChange = width !== prevWidth;\n                const isHeightChange = height !== prevHeight;\n                if (invertX || invertY) {\n                    const x = invertX ? startNodeX - (width - startWidth) : startNodeX;\n                    const y = invertY ? startNodeY - (height - startHeight) : startNodeY;\n                    // only transform the node if the width or height changes\n                    const isXPosChange = x !== prevX && isWidthChange;\n                    const isYPosChange = y !== prevY && isHeightChange;\n                    if (isXPosChange || isYPosChange) {\n                        const positionChange = {\n                            id: node.id,\n                            type: 'position',\n                            position: {\n                                x: isXPosChange ? x : prevX,\n                                y: isYPosChange ? y : prevY,\n                            },\n                        };\n                        changes.push(positionChange);\n                        prevValues.current.x = positionChange.position.x;\n                        prevValues.current.y = positionChange.position.y;\n                    }\n                }\n                if (isWidthChange || isHeightChange) {\n                    const dimensionChange = {\n                        id: id,\n                        type: 'dimensions',\n                        updateStyle: true,\n                        resizing: true,\n                        dimensions: {\n                            width: width,\n                            height: height,\n                        },\n                    };\n                    changes.push(dimensionChange);\n                    prevValues.current.width = width;\n                    prevValues.current.height = height;\n                }\n                if (changes.length === 0) {\n                    return;\n                }\n                const direction = getDirection({\n                    width: prevValues.current.width,\n                    prevWidth,\n                    height: prevValues.current.height,\n                    prevHeight,\n                    invertX,\n                    invertY,\n                });\n                const nextValues = { ...prevValues.current, direction };\n                const callResize = shouldResize?.(event, nextValues);\n                if (callResize === false) {\n                    return;\n                }\n                onResize?.(event, nextValues);\n                triggerNodeChanges(changes);\n            }\n        })\n            .on('end', (event) => {\n            const dimensionChange = {\n                id: id,\n                type: 'dimensions',\n                resizing: false,\n            };\n            onResizeEnd?.(event, { ...prevValues.current });\n            store.getState().triggerNodeChanges([dimensionChange]);\n        });\n        selection.call(dragHandler);\n        return () => {\n            selection.on('.drag', null);\n        };\n    }, [\n        id,\n        controlPosition,\n        minWidth,\n        minHeight,\n        maxWidth,\n        maxHeight,\n        keepAspectRatio,\n        getPointerPosition,\n        onResizeStart,\n        onResize,\n        onResizeEnd,\n    ]);\n    const positionClassNames = controlPosition.split('-');\n    const colorStyleProp = variant === ResizeControlVariant.Line ? 'borderColor' : 'backgroundColor';\n    const controlStyle = color ? { ...style, [colorStyleProp]: color } : style;\n    return (React.createElement(\"div\", { className: cc(['react-flow__resize-control', 'nodrag', ...positionClassNames, variant, className]), ref: resizeControlRef, style: controlStyle }, children));\n}\nvar ResizeControl$1 = memo(ResizeControl);\n\nconst handleControls = ['top-left', 'top-right', 'bottom-left', 'bottom-right'];\nconst lineControls = ['top', 'right', 'bottom', 'left'];\nfunction NodeResizer({ nodeId, isVisible = true, handleClassName, handleStyle, lineClassName, lineStyle, color, minWidth = 10, minHeight = 10, maxWidth = Number.MAX_VALUE, maxHeight = Number.MAX_VALUE, keepAspectRatio = false, shouldResize, onResizeStart, onResize, onResizeEnd, }) {\n    if (!isVisible) {\n        return null;\n    }\n    return (React.createElement(React.Fragment, null,\n        lineControls.map((c) => (React.createElement(ResizeControl$1, { key: c, className: lineClassName, style: lineStyle, nodeId: nodeId, position: c, variant: ResizeControlVariant.Line, color: color, minWidth: minWidth, minHeight: minHeight, maxWidth: maxWidth, maxHeight: maxHeight, onResizeStart: onResizeStart, keepAspectRatio: keepAspectRatio, shouldResize: shouldResize, onResize: onResize, onResizeEnd: onResizeEnd }))),\n        handleControls.map((c) => (React.createElement(ResizeControl$1, { key: c, className: handleClassName, style: handleStyle, nodeId: nodeId, position: c, color: color, minWidth: minWidth, minHeight: minHeight, maxWidth: maxWidth, maxHeight: maxHeight, onResizeStart: onResizeStart, keepAspectRatio: keepAspectRatio, shouldResize: shouldResize, onResize: onResize, onResizeEnd: onResizeEnd })))));\n}\n\nexport { ResizeControl$1 as NodeResizeControl, NodeResizer, ResizeControlVariant };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAWA,KACG,WAAY;AACX,eAAS,GAAG,GAAG,GAAG;AAChB,eAAQ,MAAM,MAAM,MAAM,KAAK,IAAI,MAAM,IAAI,MAAQ,MAAM,KAAK,MAAM;AAAA,MACxE;AACA,eAAS,uBAAuB,WAAW,aAAa;AACtD,6BACE,WAAWA,OAAM,oBACf,oBAAoB,MACtB,QAAQ;AAAA,UACN;AAAA,QACF;AACF,YAAI,QAAQ,YAAY;AACxB,YAAI,CAAC,4BAA4B;AAC/B,cAAI,cAAc,YAAY;AAC9B,mBAAS,OAAO,WAAW,MACxB,QAAQ;AAAA,YACP;AAAA,UACF,GACC,6BAA6B;AAAA,QAClC;AACA,sBAAcC,UAAS;AAAA,UACrB,MAAM,EAAE,OAAc,YAAyB;AAAA,QACjD,CAAC;AACD,YAAI,OAAO,YAAY,CAAC,EAAE,MACxB,cAAc,YAAY,CAAC;AAC7B;AAAA,UACE,WAAY;AACV,iBAAK,QAAQ;AACb,iBAAK,cAAc;AACnB,mCAAuB,IAAI,KAAK,YAAY,EAAE,KAAW,CAAC;AAAA,UAC5D;AAAA,UACA,CAAC,WAAW,OAAO,WAAW;AAAA,QAChC;AACA,QAAAC;AAAA,UACE,WAAY;AACV,mCAAuB,IAAI,KAAK,YAAY,EAAE,KAAW,CAAC;AAC1D,mBAAO,UAAU,WAAY;AAC3B,qCAAuB,IAAI,KAAK,YAAY,EAAE,KAAW,CAAC;AAAA,YAC5D,CAAC;AAAA,UACH;AAAA,UACA,CAAC,SAAS;AAAA,QACZ;AACA,QAAAC,eAAc,KAAK;AACnB,eAAO;AAAA,MACT;AACA,eAAS,uBAAuB,MAAM;AACpC,YAAI,oBAAoB,KAAK;AAC7B,eAAO,KAAK;AACZ,YAAI;AACF,cAAI,YAAY,kBAAkB;AAClC,iBAAO,CAAC,SAAS,MAAM,SAAS;AAAA,QAClC,SAAS,OAAO;AACd,iBAAO;AAAA,QACT;AAAA,MACF;AACA,eAAS,uBAAuB,WAAW,aAAa;AACtD,eAAO,YAAY;AAAA,MACrB;AACA,sBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,+BACxC,+BAA+B,4BAA4B,MAAM,CAAC;AACpE,UAAIH,SAAQ,iBACV,WAAW,eAAe,OAAO,OAAO,KAAK,OAAO,KAAK,IACzDC,YAAWD,OAAM,UACjBE,aAAYF,OAAM,WAClB,kBAAkBA,OAAM,iBACxBG,iBAAgBH,OAAM,eACtB,oBAAoB,OACpB,6BAA6B,OAC7B,OACE,gBAAgB,OAAO,UACvB,gBAAgB,OAAO,OAAO,YAC9B,gBAAgB,OAAO,OAAO,SAAS,gBACnC,yBACA;AACR,cAAQ,uBACN,WAAWA,OAAM,uBAAuBA,OAAM,uBAAuB;AACvE,sBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,8BACxC,+BAA+B,2BAA2B,MAAM,CAAC;AAAA,IACrE,GAAG;AAAA;AAAA;;;AC9FL;AAAA;AAAA;AAEA,QAAI,OAAuC;AACzC,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;;;ACNA;AAAA;AAAA;AAWA,KACG,WAAY;AACX,eAAS,GAAG,GAAG,GAAG;AAChB,eAAQ,MAAM,MAAM,MAAM,KAAK,IAAI,MAAM,IAAI,MAAQ,MAAM,KAAK,MAAM;AAAA,MACxE;AACA,sBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,+BACxC,+BAA+B,4BAA4B,MAAM,CAAC;AACpE,UAAII,SAAQ,iBACV,OAAO,gBACP,WAAW,eAAe,OAAO,OAAO,KAAK,OAAO,KAAK,IACzD,uBAAuB,KAAK,sBAC5BC,UAASD,OAAM,QACfE,aAAYF,OAAM,WAClBG,WAAUH,OAAM,SAChBI,iBAAgBJ,OAAM;AACxB,cAAQ,mCAAmC,SACzC,WACA,aACA,mBACAK,WACA,SACA;AACA,YAAI,UAAUJ,QAAO,IAAI;AACzB,YAAI,SAAS,QAAQ,SAAS;AAC5B,cAAI,OAAO,EAAE,UAAU,OAAI,OAAO,KAAK;AACvC,kBAAQ,UAAU;AAAA,QACpB,MAAO,QAAO,QAAQ;AACtB,kBAAUE;AAAA,UACR,WAAY;AACV,qBAAS,iBAAiB,cAAc;AACtC,kBAAI,CAAC,SAAS;AACZ,0BAAU;AACV,mCAAmB;AACnB,+BAAeE,UAAS,YAAY;AACpC,oBAAI,WAAW,WAAW,KAAK,UAAU;AACvC,sBAAI,mBAAmB,KAAK;AAC5B,sBAAI,QAAQ,kBAAkB,YAAY;AACxC,2BAAQ,oBAAoB;AAAA,gBAChC;AACA,uBAAQ,oBAAoB;AAAA,cAC9B;AACA,iCAAmB;AACnB,kBAAI,SAAS,kBAAkB,YAAY;AACzC,uBAAO;AACT,kBAAI,gBAAgBA,UAAS,YAAY;AACzC,kBAAI,WAAW,WAAW,QAAQ,kBAAkB,aAAa;AAC/D,uBAAQ,mBAAmB,cAAe;AAC5C,iCAAmB;AACnB,qBAAQ,oBAAoB;AAAA,YAC9B;AACA,gBAAI,UAAU,OACZ,kBACA,mBACA,yBACE,WAAW,oBAAoB,OAAO;AAC1C,mBAAO;AAAA,cACL,WAAY;AACV,uBAAO,iBAAiB,YAAY,CAAC;AAAA,cACvC;AAAA,cACA,SAAS,yBACL,SACA,WAAY;AACV,uBAAO,iBAAiB,uBAAuB,CAAC;AAAA,cAClD;AAAA,YACN;AAAA,UACF;AAAA,UACA,CAAC,aAAa,mBAAmBA,WAAU,OAAO;AAAA,QACpD;AACA,YAAI,QAAQ,qBAAqB,WAAW,QAAQ,CAAC,GAAG,QAAQ,CAAC,CAAC;AAClE,QAAAH;AAAA,UACE,WAAY;AACV,iBAAK,WAAW;AAChB,iBAAK,QAAQ;AAAA,UACf;AAAA,UACA,CAAC,KAAK;AAAA,QACR;AACA,QAAAE,eAAc,KAAK;AACnB,eAAO;AAAA,MACT;AACA,sBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,8BACxC,+BAA+B,2BAA2B,MAAM,CAAC;AAAA,IACrE,GAAG;AAAA;AAAA;;;AChGL;AAAA;AAAA;AAEA,QAAI,OAAuC;AACzC,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;;;ACNA,IAAAE,gBAAsH;;;ACAvG,SAAR,GAAoB,OAAO;AAChC,MAAI,OAAO,UAAU,YAAY,OAAO,UAAU,SAAU,QAAO,KAAK;AAExE,MAAI,MAAM;AAEV,MAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,aAAS,IAAI,GAAG,KAAK,IAAI,MAAM,QAAQ,KAAK;AAC1C,WAAK,MAAM,GAAG,MAAM,CAAC,CAAC,OAAO,IAAI;AAC/B,gBAAQ,OAAO,OAAO;AAAA,MACxB;AAAA,IACF;AAAA,EACF,OAAO;AACL,aAAS,KAAK,OAAO;AACnB,UAAI,MAAM,CAAC,EAAG,SAAQ,OAAO,OAAO;AAAA,IACtC;AAAA,EACF;AAEA,SAAO;AACT;;;AClBA,mBAAyB;AACzB,2BAAwC;;;ACDxC,IAAM,kBAAkB,CAAC,gBAAgB;AACvC,MAAI;AACJ,QAAM,YAA4B,oBAAI,IAAI;AAC1C,QAAM,WAAW,CAAC,SAAS,YAAY;AACrC,UAAM,YAAY,OAAO,YAAY,aAAa,QAAQ,KAAK,IAAI;AACnE,QAAI,CAAC,OAAO,GAAG,WAAW,KAAK,GAAG;AAChC,YAAM,gBAAgB;AACtB,eAAS,WAAW,OAAO,UAAU,OAAO,cAAc,YAAY,cAAc,QAAQ,YAAY,OAAO,OAAO,CAAC,GAAG,OAAO,SAAS;AAC1I,gBAAU,QAAQ,CAAC,aAAa,SAAS,OAAO,aAAa,CAAC;AAAA,IAChE;AAAA,EACF;AACA,QAAM,WAAW,MAAM;AACvB,QAAM,kBAAkB,MAAMC;AAC9B,QAAM,YAAY,CAAC,aAAa;AAC9B,cAAU,IAAI,QAAQ;AACtB,WAAO,MAAM,UAAU,OAAO,QAAQ;AAAA,EACxC;AACA,QAAM,UAAU,MAAM;AACpB,SAAK,YAAY,MAAM,YAAY,IAAI,OAAO,YAAY,cAAc;AACtE,cAAQ;AAAA,QACN;AAAA,MACF;AAAA,IACF;AACA,cAAU,MAAM;AAAA,EAClB;AACA,QAAM,MAAM,EAAE,UAAU,UAAU,iBAAiB,WAAW,QAAQ;AACtE,QAAMA,gBAAe,QAAQ,YAAY,UAAU,UAAU,GAAG;AAChE,SAAO;AACT;AACA,IAAM,cAAc,CAAC,gBAAgB,cAAc,gBAAgB,WAAW,IAAI;;;ADzBlF,IAAM,EAAE,cAAc,IAAI,aAAAC;AAC1B,IAAM,EAAE,iCAAiC,IAAI,qBAAAC;AAC7C,IAAMC,YAAW,CAAC,QAAQ;AAC1B,SAAS,uBAAuB,KAAKC,YAAWD,WAAU,YAAY;AACpE,QAAM,QAAQ;AAAA,IACZ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI,kBAAkB,IAAI;AAAA,IAC1BC;AAAA,IACA;AAAA,EACF;AACA,gBAAc,KAAK;AACnB,SAAO;AACT;AACA,IAAM,2BAA2B,CAAC,aAAa,sBAAsB;AACnE,QAAM,MAAM,YAAY,WAAW;AACnC,QAAM,8BAA8B,CAACA,WAAU,aAAa,sBAAsB,uBAAuB,KAAKA,WAAU,UAAU;AAClI,SAAO,OAAO,6BAA6B,GAAG;AAC9C,SAAO;AACT;AACA,IAAM,uBAAuB,CAAC,aAAa,sBAAsB,cAAc,yBAAyB,aAAa,iBAAiB,IAAI;;;AExB1I,SAAS,UAAU,MAAM,MAAM;AAC7B,MAAI,OAAO,GAAG,MAAM,IAAI,GAAG;AACzB,WAAO;AAAA,EACT;AACA,MAAI,OAAO,SAAS,YAAY,SAAS,QAAQ,OAAO,SAAS,YAAY,SAAS,MAAM;AAC1F,WAAO;AAAA,EACT;AACA,MAAI,gBAAgB,OAAO,gBAAgB,KAAK;AAC9C,QAAI,KAAK,SAAS,KAAK,KAAM,QAAO;AACpC,eAAW,CAAC,KAAK,KAAK,KAAK,MAAM;AAC/B,UAAI,CAAC,OAAO,GAAG,OAAO,KAAK,IAAI,GAAG,CAAC,GAAG;AACpC,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,MAAI,gBAAgB,OAAO,gBAAgB,KAAK;AAC9C,QAAI,KAAK,SAAS,KAAK,KAAM,QAAO;AACpC,eAAW,SAAS,MAAM;AACxB,UAAI,CAAC,KAAK,IAAI,KAAK,GAAG;AACpB,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,QAAM,QAAQ,OAAO,KAAK,IAAI;AAC9B,MAAI,MAAM,WAAW,OAAO,KAAK,IAAI,EAAE,QAAQ;AAC7C,WAAO;AAAA,EACT;AACA,aAAW,QAAQ,OAAO;AACxB,QAAI,CAAC,OAAO,UAAU,eAAe,KAAK,MAAM,IAAI,KAAK,CAAC,OAAO,GAAG,KAAK,IAAI,GAAG,KAAK,IAAI,CAAC,GAAG;AAC3F,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;;;AJ5BA,uBAA6B;AAE7B,IAAM,mBAAe,6BAAc,IAAI;AACvC,IAAM,aAAa,aAAa;AAEhC,IAAM,gBAAgB;AAAA,EAClB,UAAU,MAAM;AAAA,EAChB,UAAU,MAAM;AAAA,EAChB,UAAU,CAAC,aAAa,cAAc,QAAQ;AAAA,EAC9C,UAAU,MAAM;AAAA,EAChB,UAAU,MAAM;AAAA,EAChB,UAAU,MAAM;AAAA,EAChB,UAAU,CAAC,OAAO,wBAAwB,EAAE;AAAA,EAC5C,UAAU,CAAC,SAAS,gBAAgB,IAAI;AAAA,EACxC,UAAU,CAAC,cAAc,SAAS,4BAA4B,CAAC,eAAe,WAAW,QAAQ,gBAAgB,CAAC,eAAe,KAAK,eAAe,KAAK,YAAY,eAAe,KAAK,EAAE;AAAA,EAC5L,UAAU,MAAM;AAAA,EAChB,UAAU,CAAC,aAAa,cAAc,QAAQ;AAAA,EAC9C,UAAU,CAAC,OAAO,iBAAiB,EAAE;AACzC;AAEA,IAAM,sBAAsB,cAAc,UAAU,EAAE;AACtD,SAAS,SAASC,WAAU,YAAY;AACpC,QAAM,YAAQ,0BAAW,YAAY;AACrC,MAAI,UAAU,MAAM;AAChB,UAAM,IAAI,MAAM,mBAAmB;AAAA,EACvC;AACA,SAAO,uBAAuB,OAAOA,WAAU,UAAU;AAC7D;AACA,IAAM,cAAc,MAAM;AACtB,QAAM,YAAQ,0BAAW,YAAY;AACrC,MAAI,UAAU,MAAM;AAChB,UAAM,IAAI,MAAM,mBAAmB;AAAA,EACvC;AACA,aAAO,uBAAQ,OAAO;AAAA,IAClB,UAAU,MAAM;AAAA,IAChB,UAAU,MAAM;AAAA,IAChB,WAAW,MAAM;AAAA,IACjB,SAAS,MAAM;AAAA,EACnB,IAAI,CAAC,KAAK,CAAC;AACf;AAEA,IAAM,aAAa,CAAC,MAAO,EAAE,sBAAsB,SAAS;AAC5D,SAAS,MAAM,EAAE,UAAU,UAAU,WAAW,OAAAC,QAAO,GAAG,KAAK,GAAG;AAC9D,QAAM,gBAAgB,SAAS,UAAU;AACzC,QAAM,kBAAkB,GAAG,QAAQ,GAAG,MAAM,GAAG;AAC/C,SAAQ,cAAAC,QAAM,cAAc,OAAO,EAAE,WAAW,GAAG,CAAC,qBAAqB,WAAW,GAAG,eAAe,CAAC,GAAG,OAAO,EAAE,GAAGD,QAAO,cAAc,GAAG,GAAG,KAAK,GAAG,QAAQ;AACrK;AAEA,SAAS,YAAY,EAAE,YAAY,WAAW,eAAe,GAAG;AAC5D,MAAI,yCAAY,iBAAiB;AAC7B,WAAO;AAAA,EACX;AACA,SAAQ,cAAAC,QAAM;AAAA,IAAc;AAAA,IAAO,EAAE,UAAoB,WAAW,2BAA2B,gBAAgB,yGAAyG;AAAA,IACpN,cAAAA,QAAM,cAAc,KAAK,EAAE,MAAM,yBAAyB,QAAQ,UAAU,KAAK,uBAAuB,cAAc,yBAAyB,GAAG,YAAY;AAAA,EAAC;AACvK;AAEA,IAAM,WAAW,CAAC,EAAE,GAAG,GAAG,OAAO,aAAa,CAAC,GAAG,cAAc,MAAM,eAAe,CAAC,GAAG,iBAAiB,CAAC,GAAG,CAAC,GAAG,sBAAsB,GAAG,UAAU,WAAW,GAAG,KAAK,MAAM;AAC1K,QAAM,cAAU,sBAAO,IAAI;AAC3B,QAAM,CAAC,cAAc,eAAe,QAAI,wBAAS,EAAE,GAAG,GAAG,GAAG,GAAG,OAAO,GAAG,QAAQ,EAAE,CAAC;AACpF,QAAM,kBAAkB,GAAG,CAAC,gCAAgC,SAAS,CAAC;AACtE,+BAAU,MAAM;AACZ,QAAI,QAAQ,SAAS;AACjB,YAAM,WAAW,QAAQ,QAAQ,QAAQ;AACzC,sBAAgB;AAAA,QACZ,GAAG,SAAS;AAAA,QACZ,GAAG,SAAS;AAAA,QACZ,OAAO,SAAS;AAAA,QAChB,QAAQ,SAAS;AAAA,MACrB,CAAC;AAAA,IACL;AAAA,EACJ,GAAG,CAAC,KAAK,CAAC;AACV,MAAI,OAAO,UAAU,eAAe,CAAC,OAAO;AACxC,WAAO;AAAA,EACX;AACA,SAAQ,cAAAA,QAAM;AAAA,IAAc;AAAA,IAAK,EAAE,WAAW,aAAa,IAAI,aAAa,QAAQ,CAAC,IAAI,IAAI,aAAa,SAAS,CAAC,KAAK,WAAW,iBAAiB,YAAY,aAAa,QAAQ,YAAY,UAAU,GAAG,KAAK;AAAA,IAChN,eAAgB,cAAAA,QAAM,cAAc,QAAQ,EAAE,OAAO,aAAa,QAAQ,IAAI,eAAe,CAAC,GAAG,GAAG,CAAC,eAAe,CAAC,GAAG,GAAG,CAAC,eAAe,CAAC,GAAG,QAAQ,aAAa,SAAS,IAAI,eAAe,CAAC,GAAG,WAAW,2BAA2B,OAAO,cAAc,IAAI,qBAAqB,IAAI,oBAAoB,CAAC;AAAA,IACjT,cAAAA,QAAM,cAAc,QAAQ,EAAE,WAAW,yBAAyB,GAAG,aAAa,SAAS,GAAG,IAAI,SAAS,KAAK,SAAS,OAAO,WAAW,GAAG,KAAK;AAAA,IACnJ;AAAA,EAAQ;AAChB;AACA,IAAI,iBAAa,oBAAK,QAAQ;AAE9B,IAAM,gBAAgB,CAAC,UAAU;AAAA,EAC7B,OAAO,KAAK;AAAA,EACZ,QAAQ,KAAK;AACjB;AACA,IAAM,QAAQ,CAAC,KAAK,MAAM,GAAG,MAAM,MAAM,KAAK,IAAI,KAAK,IAAI,KAAK,GAAG,GAAG,GAAG;AACzE,IAAM,gBAAgB,CAAC,WAAW,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,YAAY;AAAA,EAC1D,GAAG,MAAM,SAAS,GAAG,OAAO,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,EAAE,CAAC,CAAC;AAAA,EAC/C,GAAG,MAAM,SAAS,GAAG,OAAO,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,EAAE,CAAC,CAAC;AACnD;AAGA,IAAM,sBAAsB,CAAC,OAAO,KAAK,QAAQ;AAC7C,MAAI,QAAQ,KAAK;AACb,WAAO,MAAM,KAAK,IAAI,QAAQ,GAAG,GAAG,GAAG,EAAE,IAAI;AAAA,EACjD,WACS,QAAQ,KAAK;AAClB,WAAO,CAAC,MAAM,KAAK,IAAI,QAAQ,GAAG,GAAG,GAAG,EAAE,IAAI;AAAA,EAClD;AACA,SAAO;AACX;AACA,IAAM,cAAc,CAAC,KAAK,WAAW;AACjC,QAAM,YAAY,oBAAoB,IAAI,GAAG,IAAI,OAAO,QAAQ,EAAE,IAAI;AACtE,QAAM,YAAY,oBAAoB,IAAI,GAAG,IAAI,OAAO,SAAS,EAAE,IAAI;AACvE,SAAO,CAAC,WAAW,SAAS;AAChC;AACA,IAAM,oBAAoB,CAAC,YAAS;AAjHpC;AAiHuC,wBAAQ,gBAAR,sCAA2B,iCAAQ;AAAA;AAC1E,IAAM,mBAAmB,CAAC,MAAM,UAAU;AAAA,EACtC,GAAG,KAAK,IAAI,KAAK,GAAG,KAAK,CAAC;AAAA,EAC1B,GAAG,KAAK,IAAI,KAAK,GAAG,KAAK,CAAC;AAAA,EAC1B,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,EAAE;AAAA,EAC7B,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,EAAE;AACjC;AACA,IAAM,YAAY,CAAC,EAAE,GAAG,GAAG,OAAO,OAAO,OAAO;AAAA,EAC5C;AAAA,EACA;AAAA,EACA,IAAI,IAAI;AAAA,EACR,IAAI,IAAI;AACZ;AACA,IAAM,YAAY,CAAC,EAAE,GAAG,GAAG,IAAI,GAAG,OAAO;AAAA,EACrC;AAAA,EACA;AAAA,EACA,OAAO,KAAK;AAAA,EACZ,QAAQ,KAAK;AACjB;AACA,IAAM,aAAa,CAAC,UAAU;AAAA,EAC1B,GAAI,KAAK,oBAAoB,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,EAC1C,OAAO,KAAK,SAAS;AAAA,EACrB,QAAQ,KAAK,UAAU;AAC3B;AACA,IAAM,mBAAmB,CAAC,OAAO,UAAU,UAAU,iBAAiB,UAAU,KAAK,GAAG,UAAU,KAAK,CAAC,CAAC;AACzG,IAAM,qBAAqB,CAAC,OAAO,UAAU;AACzC,QAAM,WAAW,KAAK,IAAI,GAAG,KAAK,IAAI,MAAM,IAAI,MAAM,OAAO,MAAM,IAAI,MAAM,KAAK,IAAI,KAAK,IAAI,MAAM,GAAG,MAAM,CAAC,CAAC;AAChH,QAAM,WAAW,KAAK,IAAI,GAAG,KAAK,IAAI,MAAM,IAAI,MAAM,QAAQ,MAAM,IAAI,MAAM,MAAM,IAAI,KAAK,IAAI,MAAM,GAAG,MAAM,CAAC,CAAC;AAClH,SAAO,KAAK,KAAK,WAAW,QAAQ;AACxC;AAEA,IAAM,eAAe,CAAC,QAAQ,UAAU,IAAI,KAAK,KAAK,UAAU,IAAI,MAAM,KAAK,UAAU,IAAI,CAAC,KAAK,UAAU,IAAI,CAAC;AAElH,IAAM,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,SAAS,CAAC;AAChD,IAAM,kBAAkB,OAAO,IAAI,WAAW;AAE9C,IAAM,uBAAuB,CAAC,SAAS,KAAK,QAAQ;AACpD,IAAM,UAAU,CAAC,IAAI,YAAY;AAC7B,MAAI,MAAwC;AACxC,YAAQ,KAAK,iBAAiB,OAAO,sCAAsC,EAAE,EAAE;AAAA,EACnF;AACJ;AACA,IAAM,uBAAuB,CAAC,UAAU,iBAAiB;AACzD,SAAS,eAAe,OAAO;AA5J/B;AA6JI,QAAM,UAAU,qBAAqB,KAAK,IAAI,MAAM,cAAc;AAElE,QAAM,WAAU,mBAAQ,iBAAR,wDAA2B,OAAM,MAAM;AACvD,QAAM,UAAU,CAAC,SAAS,UAAU,UAAU,EAAE,SAAS,iCAAQ,QAAQ,MAAK,iCAAQ,aAAa;AAEnG,SAAO,WAAW,CAAC,EAAC,iCAAQ,QAAQ;AACxC;AACA,IAAM,eAAe,CAAC,UAAU,aAAa;AAC7C,IAAM,mBAAmB,CAAC,OAAO,WAAW;AArK5C;AAsKI,QAAM,mBAAmB,aAAa,KAAK;AAC3C,QAAM,OAAO,mBAAmB,MAAM,WAAU,WAAM,YAAN,mBAAgB,GAAG;AACnE,QAAM,OAAO,mBAAmB,MAAM,WAAU,WAAM,YAAN,mBAAgB,GAAG;AACnE,SAAO;AAAA,IACH,GAAG,SAAQ,iCAAQ,SAAQ;AAAA,IAC3B,GAAG,SAAQ,iCAAQ,QAAO;AAAA,EAC9B;AACJ;AACA,IAAM,UAAU,MAAG;AA9KnB;AA8KsB,gBAAO,cAAc,iBAAe,4CAAW,cAAX,mBAAsB,QAAQ,WAAU;AAAA;AAElG,IAAM,WAAW,CAAC,EAAE,IAAI,MAAM,QAAQ,QAAQ,OAAO,YAAY,aAAa,cAAc,gBAAgB,qBAAqB,OAAAD,QAAO,WAAW,aAAa,mBAAmB,GAAI,MAAM;AACzL,SAAQ,cAAAC,QAAM;AAAA,IAAc,cAAAA,QAAM;AAAA,IAAU;AAAA,IACxC,cAAAA,QAAM,cAAc,QAAQ,EAAE,IAAQ,OAAOD,QAAO,GAAG,MAAM,MAAM,QAAQ,WAAW,yBAAyB,WAAsB,YAAyB,CAAC;AAAA,IAC/J,oBAAqB,cAAAC,QAAM,cAAc,QAAQ,EAAE,GAAG,MAAM,MAAM,QAAQ,eAAe,GAAG,aAAa,kBAAkB,WAAW,+BAA+B,CAAC;AAAA,IACtK,SAAS,UAAU,MAAM,KAAK,UAAU,MAAM,IAAK,cAAAA,QAAM,cAAc,YAAY,EAAE,GAAG,QAAQ,GAAG,QAAQ,OAAc,YAAwB,aAA0B,cAA4B,gBAAgC,oBAAyC,CAAC,IAAK;AAAA,EAAI;AAClS;AACA,SAAS,cAAc;AAEvB,IAAM,eAAe,CAAC,YAAY,gBAAgB;AAC9C,MAAI,OAAO,gBAAgB,eAAe,aAAa;AACnD,WAAO,QAAQ,WAAW;AAAA,EAC9B;AACA,SAAO,OAAO,eAAe,cAAc,oBAAoB,UAAU,MAAM;AACnF;AACA,SAAS,kBAAkB,IAAI,UAAU,SAAS;AAC9C,SAAO,YAAY,SACb,UACA,CAAC,UAAU;AACT,UAAM,OAAO,SAAS,EAAE,MAAM,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE;AACrD,QAAI,MAAM;AACN,cAAQ,OAAO,EAAE,GAAG,KAAK,CAAC;AAAA,IAC9B;AAAA,EACJ;AACR;AAEA,SAAS,cAAc,EAAE,SAAS,SAAS,SAAS,QAAS,GAAG;AAC5D,QAAM,UAAU,KAAK,IAAI,UAAU,OAAO,IAAI;AAC9C,QAAM,UAAU,UAAU,UAAU,UAAU,UAAU,UAAU;AAClE,QAAM,UAAU,KAAK,IAAI,UAAU,OAAO,IAAI;AAC9C,QAAM,UAAU,UAAU,UAAU,UAAU,UAAU,UAAU;AAClE,SAAO,CAAC,SAAS,SAAS,SAAS,OAAO;AAC9C;AACA,SAAS,oBAAoB,EAAE,SAAS,SAAS,SAAS,SAAS,gBAAgB,gBAAgB,gBAAgB,eAAgB,GAAG;AAGlI,QAAM,UAAU,UAAU,QAAQ,iBAAiB,QAAQ,iBAAiB,QAAQ,UAAU;AAC9F,QAAM,UAAU,UAAU,QAAQ,iBAAiB,QAAQ,iBAAiB,QAAQ,UAAU;AAC9F,QAAM,UAAU,KAAK,IAAI,UAAU,OAAO;AAC1C,QAAM,UAAU,KAAK,IAAI,UAAU,OAAO;AAC1C,SAAO,CAAC,SAAS,SAAS,SAAS,OAAO;AAC9C;AAEA,IAAI;AAAA,CACH,SAAUC,iBAAgB;AACvB,EAAAA,gBAAe,QAAQ,IAAI;AAC3B,EAAAA,gBAAe,OAAO,IAAI;AAC9B,GAAG,mBAAmB,iBAAiB,CAAC,EAAE;AAC1C,IAAI;AAAA,CACH,SAAUC,kBAAiB;AACxB,EAAAA,iBAAgB,MAAM,IAAI;AAC1B,EAAAA,iBAAgB,UAAU,IAAI;AAC9B,EAAAA,iBAAgB,YAAY,IAAI;AACpC,GAAG,oBAAoB,kBAAkB,CAAC,EAAE;AAC5C,IAAI;AAAA,CACH,SAAUC,gBAAe;AACtB,EAAAA,eAAc,SAAS,IAAI;AAC3B,EAAAA,eAAc,MAAM,IAAI;AAC5B,GAAG,kBAAkB,gBAAgB,CAAC,EAAE;AAExC,IAAI;AAAA,CACH,SAAUC,qBAAoB;AAC3B,EAAAA,oBAAmB,QAAQ,IAAI;AAC/B,EAAAA,oBAAmB,UAAU,IAAI;AACjC,EAAAA,oBAAmB,MAAM,IAAI;AAC7B,EAAAA,oBAAmB,YAAY,IAAI;AACnC,EAAAA,oBAAmB,cAAc,IAAI;AACzC,GAAG,uBAAuB,qBAAqB,CAAC,EAAE;AAClD,IAAI;AAAA,CACH,SAAUC,aAAY;AACnB,EAAAA,YAAW,OAAO,IAAI;AACtB,EAAAA,YAAW,aAAa,IAAI;AAChC,GAAG,eAAe,aAAa,CAAC,EAAE;AAElC,IAAI;AAAA,CACH,SAAUC,WAAU;AACjB,EAAAA,UAAS,MAAM,IAAI;AACnB,EAAAA,UAAS,KAAK,IAAI;AAClB,EAAAA,UAAS,OAAO,IAAI;AACpB,EAAAA,UAAS,QAAQ,IAAI;AACzB,GAAG,aAAa,WAAW,CAAC,EAAE;AAE9B,SAAS,WAAW,EAAE,KAAK,IAAI,IAAI,IAAI,GAAG,GAAG;AACzC,MAAI,QAAQ,SAAS,QAAQ,QAAQ,SAAS,OAAO;AACjD,WAAO,CAAC,OAAO,KAAK,KAAK,EAAE;AAAA,EAC/B;AACA,SAAO,CAAC,IAAI,OAAO,KAAK,GAAG;AAC/B;AACA,SAAS,oBAAoB,EAAE,SAAS,SAAS,iBAAiB,SAAS,QAAQ,SAAS,SAAS,iBAAiB,SAAS,IAAK,GAAG;AACnI,QAAM,CAAC,gBAAgB,cAAc,IAAI,WAAW;AAAA,IAChD,KAAK;AAAA,IACL,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACR,CAAC;AACD,QAAM,CAAC,gBAAgB,cAAc,IAAI,WAAW;AAAA,IAChD,KAAK;AAAA,IACL,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACR,CAAC;AACD,QAAM,CAAC,QAAQ,QAAQ,SAAS,OAAO,IAAI,oBAAoB;AAAA,IAC3D;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;AACD,SAAO;AAAA,IACH,IAAI,OAAO,IAAI,OAAO,KAAK,cAAc,IAAI,cAAc,IAAI,cAAc,IAAI,cAAc,IAAI,OAAO,IAAI,OAAO;AAAA,IACrH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AACA,IAAM,uBAAmB,oBAAK,CAAC,EAAE,SAAS,SAAS,SAAS,SAAS,iBAAiB,SAAS,QAAQ,iBAAiB,SAAS,KAAK,OAAO,YAAY,aAAa,cAAc,gBAAgB,qBAAqB,OAAAP,QAAO,WAAW,aAAa,iBAAkB,MAAM;AAC5Q,QAAM,CAAC,MAAM,QAAQ,MAAM,IAAI,oBAAoB;AAAA,IAC/C;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;AACD,SAAQ,cAAAC,QAAM,cAAc,UAAU,EAAE,MAAY,QAAgB,QAAgB,OAAc,YAAwB,aAA0B,cAA4B,gBAAgC,qBAA0C,OAAOD,QAAO,WAAsB,aAA0B,iBAAmC,CAAC;AAChW,CAAC;AACD,iBAAiB,cAAc;AAE/B,IAAM,mBAAmB;AAAA,EACrB,CAAC,SAAS,IAAI,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE;AAAA,EAC/B,CAAC,SAAS,KAAK,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,EAC/B,CAAC,SAAS,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,GAAG;AAAA,EAC9B,CAAC,SAAS,MAAM,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE;AACpC;AACA,IAAM,eAAe,CAAC,EAAE,QAAQ,iBAAiB,SAAS,QAAQ,OAAQ,MAAM;AAC5E,MAAI,mBAAmB,SAAS,QAAQ,mBAAmB,SAAS,OAAO;AACvE,WAAO,OAAO,IAAI,OAAO,IAAI,EAAE,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,GAAG,EAAE;AAAA,EAChE;AACA,SAAO,OAAO,IAAI,OAAO,IAAI,EAAE,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,GAAG,GAAG;AAChE;AACA,IAAM,WAAW,CAAC,GAAG,MAAM,KAAK,KAAK,KAAK,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,IAAI,KAAK,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;AAGpF,SAAS,UAAU,EAAE,QAAQ,iBAAiB,SAAS,QAAQ,QAAQ,iBAAiB,SAAS,KAAK,QAAQ,OAAQ,GAAG;AACrH,QAAM,YAAY,iBAAiB,cAAc;AACjD,QAAM,YAAY,iBAAiB,cAAc;AACjD,QAAM,eAAe,EAAE,GAAG,OAAO,IAAI,UAAU,IAAI,QAAQ,GAAG,OAAO,IAAI,UAAU,IAAI,OAAO;AAC9F,QAAM,eAAe,EAAE,GAAG,OAAO,IAAI,UAAU,IAAI,QAAQ,GAAG,OAAO,IAAI,UAAU,IAAI,OAAO;AAC9F,QAAM,MAAM,aAAa;AAAA,IACrB,QAAQ;AAAA,IACR;AAAA,IACA,QAAQ;AAAA,EACZ,CAAC;AACD,QAAM,cAAc,IAAI,MAAM,IAAI,MAAM;AACxC,QAAM,UAAU,IAAI,WAAW;AAC/B,MAAI,SAAS,CAAC;AACd,MAAI,SAAS;AACb,QAAM,kBAAkB,EAAE,GAAG,GAAG,GAAG,EAAE;AACrC,QAAM,kBAAkB,EAAE,GAAG,GAAG,GAAG,EAAE;AACrC,QAAM,CAAC,gBAAgB,gBAAgB,gBAAgB,cAAc,IAAI,cAAc;AAAA,IACnF,SAAS,OAAO;AAAA,IAChB,SAAS,OAAO;AAAA,IAChB,SAAS,OAAO;AAAA,IAChB,SAAS,OAAO;AAAA,EACpB,CAAC;AAED,MAAI,UAAU,WAAW,IAAI,UAAU,WAAW,MAAM,IAAI;AACxD,cAAU,OAAO,KAAK;AACtB,cAAU,OAAO,KAAK;AAItB,UAAM,gBAAgB;AAAA,MAClB,EAAE,GAAG,SAAS,GAAG,aAAa,EAAE;AAAA,MAChC,EAAE,GAAG,SAAS,GAAG,aAAa,EAAE;AAAA,IACpC;AAIA,UAAM,kBAAkB;AAAA,MACpB,EAAE,GAAG,aAAa,GAAG,GAAG,QAAQ;AAAA,MAChC,EAAE,GAAG,aAAa,GAAG,GAAG,QAAQ;AAAA,IACpC;AACA,QAAI,UAAU,WAAW,MAAM,SAAS;AACpC,eAAS,gBAAgB,MAAM,gBAAgB;AAAA,IACnD,OACK;AACD,eAAS,gBAAgB,MAAM,kBAAkB;AAAA,IACrD;AAAA,EACJ,OACK;AAED,UAAM,eAAe,CAAC,EAAE,GAAG,aAAa,GAAG,GAAG,aAAa,EAAE,CAAC;AAC9D,UAAM,eAAe,CAAC,EAAE,GAAG,aAAa,GAAG,GAAG,aAAa,EAAE,CAAC;AAE9D,QAAI,gBAAgB,KAAK;AACrB,eAAS,UAAU,MAAM,UAAU,eAAe;AAAA,IACtD,OACK;AACD,eAAS,UAAU,MAAM,UAAU,eAAe;AAAA,IACtD;AACA,QAAI,mBAAmB,gBAAgB;AACnC,YAAM,OAAO,KAAK,IAAI,OAAO,WAAW,IAAI,OAAO,WAAW,CAAC;AAE/D,UAAI,QAAQ,QAAQ;AAChB,cAAM,YAAY,KAAK,IAAI,SAAS,GAAG,SAAS,IAAI;AACpD,YAAI,UAAU,WAAW,MAAM,SAAS;AACpC,0BAAgB,WAAW,KAAK,aAAa,WAAW,IAAI,OAAO,WAAW,IAAI,KAAK,KAAK;AAAA,QAChG,OACK;AACD,0BAAgB,WAAW,KAAK,aAAa,WAAW,IAAI,OAAO,WAAW,IAAI,KAAK,KAAK;AAAA,QAChG;AAAA,MACJ;AAAA,IACJ;AAEA,QAAI,mBAAmB,gBAAgB;AACnC,YAAM,sBAAsB,gBAAgB,MAAM,MAAM;AACxD,YAAM,YAAY,UAAU,WAAW,MAAM,UAAU,mBAAmB;AAC1E,YAAM,qBAAqB,aAAa,mBAAmB,IAAI,aAAa,mBAAmB;AAC/F,YAAM,qBAAqB,aAAa,mBAAmB,IAAI,aAAa,mBAAmB;AAC/F,YAAM,mBAAoB,UAAU,WAAW,MAAM,MAAO,CAAC,aAAa,sBAAwB,aAAa,uBAC1G,UAAU,WAAW,MAAM,MAAO,CAAC,aAAa,sBAAwB,aAAa;AAC1F,UAAI,kBAAkB;AAClB,iBAAS,gBAAgB,MAAM,eAAe;AAAA,MAClD;AAAA,IACJ;AACA,UAAM,iBAAiB,EAAE,GAAG,aAAa,IAAI,gBAAgB,GAAG,GAAG,aAAa,IAAI,gBAAgB,EAAE;AACtG,UAAM,iBAAiB,EAAE,GAAG,aAAa,IAAI,gBAAgB,GAAG,GAAG,aAAa,IAAI,gBAAgB,EAAE;AACtG,UAAM,eAAe,KAAK,IAAI,KAAK,IAAI,eAAe,IAAI,OAAO,CAAC,EAAE,CAAC,GAAG,KAAK,IAAI,eAAe,IAAI,OAAO,CAAC,EAAE,CAAC,CAAC;AAChH,UAAM,eAAe,KAAK,IAAI,KAAK,IAAI,eAAe,IAAI,OAAO,CAAC,EAAE,CAAC,GAAG,KAAK,IAAI,eAAe,IAAI,OAAO,CAAC,EAAE,CAAC,CAAC;AAEhH,QAAI,gBAAgB,cAAc;AAC9B,iBAAW,eAAe,IAAI,eAAe,KAAK;AAClD,gBAAU,OAAO,CAAC,EAAE;AAAA,IACxB,OACK;AACD,gBAAU,OAAO,CAAC,EAAE;AACpB,iBAAW,eAAe,IAAI,eAAe,KAAK;AAAA,IACtD;AAAA,EACJ;AACA,QAAM,aAAa;AAAA,IACf;AAAA,IACA,EAAE,GAAG,aAAa,IAAI,gBAAgB,GAAG,GAAG,aAAa,IAAI,gBAAgB,EAAE;AAAA,IAC/E,GAAG;AAAA,IACH,EAAE,GAAG,aAAa,IAAI,gBAAgB,GAAG,GAAG,aAAa,IAAI,gBAAgB,EAAE;AAAA,IAC/E;AAAA,EACJ;AACA,SAAO,CAAC,YAAY,SAAS,SAAS,gBAAgB,cAAc;AACxE;AACA,SAAS,QAAQ,GAAG,GAAG,GAAG,MAAM;AAC5B,QAAM,WAAW,KAAK,IAAI,SAAS,GAAG,CAAC,IAAI,GAAG,SAAS,GAAG,CAAC,IAAI,GAAG,IAAI;AACtE,QAAM,EAAE,GAAG,EAAE,IAAI;AAEjB,MAAK,EAAE,MAAM,KAAK,MAAM,EAAE,KAAO,EAAE,MAAM,KAAK,MAAM,EAAE,GAAI;AACtD,WAAO,IAAI,CAAC,IAAI,CAAC;AAAA,EACrB;AAEA,MAAI,EAAE,MAAM,GAAG;AACX,UAAMQ,QAAO,EAAE,IAAI,EAAE,IAAI,KAAK;AAC9B,UAAMC,QAAO,EAAE,IAAI,EAAE,IAAI,IAAI;AAC7B,WAAO,KAAK,IAAI,WAAWD,KAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,WAAWC,KAAI;AAAA,EAC/E;AACA,QAAM,OAAO,EAAE,IAAI,EAAE,IAAI,IAAI;AAC7B,QAAM,OAAO,EAAE,IAAI,EAAE,IAAI,KAAK;AAC9B,SAAO,KAAK,CAAC,IAAI,IAAI,WAAW,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,WAAW,IAAI,IAAI,CAAC;AAC/E;AACA,SAAS,kBAAkB,EAAE,SAAS,SAAS,iBAAiB,SAAS,QAAQ,SAAS,SAAS,iBAAiB,SAAS,KAAK,eAAe,GAAG,SAAS,SAAS,SAAS,GAAI,GAAG;AAClL,QAAM,CAAC,QAAQ,QAAQ,QAAQ,SAAS,OAAO,IAAI,UAAU;AAAA,IACzD,QAAQ,EAAE,GAAG,SAAS,GAAG,QAAQ;AAAA,IACjC;AAAA,IACA,QAAQ,EAAE,GAAG,SAAS,GAAG,QAAQ;AAAA,IACjC;AAAA,IACA,QAAQ,EAAE,GAAG,SAAS,GAAG,QAAQ;AAAA,IACjC;AAAA,EACJ,CAAC;AACD,QAAM,OAAO,OAAO,OAAO,CAAC,KAAK,GAAG,MAAM;AACtC,QAAI,UAAU;AACd,QAAI,IAAI,KAAK,IAAI,OAAO,SAAS,GAAG;AAChC,gBAAU,QAAQ,OAAO,IAAI,CAAC,GAAG,GAAG,OAAO,IAAI,CAAC,GAAG,YAAY;AAAA,IACnE,OACK;AACD,gBAAU,GAAG,MAAM,IAAI,MAAM,GAAG,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IACjD;AACA,WAAO;AACP,WAAO;AAAA,EACX,GAAG,EAAE;AACL,SAAO,CAAC,MAAM,QAAQ,QAAQ,SAAS,OAAO;AAClD;AACA,IAAM,qBAAiB,oBAAK,CAAC,EAAE,SAAS,SAAS,SAAS,SAAS,OAAO,YAAY,aAAa,cAAc,gBAAgB,qBAAqB,OAAAT,QAAO,iBAAiB,SAAS,QAAQ,iBAAiB,SAAS,KAAK,WAAW,aAAa,aAAa,iBAAkB,MAAM;AACvR,QAAM,CAAC,MAAM,QAAQ,MAAM,IAAI,kBAAkB;AAAA,IAC7C;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,cAAc,2CAAa;AAAA,IAC3B,QAAQ,2CAAa;AAAA,EACzB,CAAC;AACD,SAAQ,cAAAC,QAAM,cAAc,UAAU,EAAE,MAAY,QAAgB,QAAgB,OAAc,YAAwB,aAA0B,cAA4B,gBAAgC,qBAA0C,OAAOD,QAAO,WAAsB,aAA0B,iBAAmC,CAAC;AAChW,CAAC;AACD,eAAe,cAAc;AAE7B,IAAM,eAAW,oBAAK,CAAC,UAAO;AApe9B;AAoekC,uBAAAC,QAAM,cAAc,gBAAgB,EAAE,GAAG,OAAO,iBAAa,uBAAQ,MAAG;AApe1G,QAAAS;AAoe8G,aAAE,cAAc,GAAG,SAAQA,MAAA,MAAM,gBAAN,gBAAAA,IAAmB,OAAO;AAAA,KAAI,EAAC,WAAM,gBAAN,mBAAmB,MAAM,CAAC,EAAE,CAAC;AAAA,CAAE;AACvM,SAAS,cAAc;AAEvB,SAAS,gBAAgB,EAAE,SAAS,SAAS,SAAS,QAAS,GAAG;AAC9D,QAAM,CAAC,QAAQ,QAAQ,SAAS,OAAO,IAAI,cAAc;AAAA,IACrD;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;AACD,SAAO,CAAC,KAAK,OAAO,IAAI,OAAO,KAAK,OAAO,IAAI,OAAO,IAAI,QAAQ,QAAQ,SAAS,OAAO;AAC9F;AACA,IAAM,mBAAe,oBAAK,CAAC,EAAE,SAAS,SAAS,SAAS,SAAS,OAAO,YAAY,aAAa,cAAc,gBAAgB,qBAAqB,OAAAV,QAAO,WAAW,aAAa,iBAAkB,MAAM;AACvM,QAAM,CAAC,MAAM,QAAQ,MAAM,IAAI,gBAAgB,EAAE,SAAS,SAAS,SAAS,QAAQ,CAAC;AACrF,SAAQ,cAAAC,QAAM,cAAc,UAAU,EAAE,MAAY,QAAgB,QAAgB,OAAc,YAAwB,aAA0B,cAA4B,gBAAgC,qBAA0C,OAAOD,QAAO,WAAsB,aAA0B,iBAAmC,CAAC;AAChW,CAAC;AACD,aAAa,cAAc;AAE3B,SAAS,uBAAuBW,WAAU,WAAW;AACjD,MAAIA,aAAY,GAAG;AACf,WAAO,MAAMA;AAAA,EACjB;AACA,SAAO,YAAY,KAAK,KAAK,KAAK,CAACA,SAAQ;AAC/C;AACA,SAAS,wBAAwB,EAAE,KAAK,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG;AACzD,UAAQ,KAAK;AAAA,IACT,KAAK,SAAS;AACV,aAAO,CAAC,KAAK,uBAAuB,KAAK,IAAI,CAAC,GAAG,EAAE;AAAA,IACvD,KAAK,SAAS;AACV,aAAO,CAAC,KAAK,uBAAuB,KAAK,IAAI,CAAC,GAAG,EAAE;AAAA,IACvD,KAAK,SAAS;AACV,aAAO,CAAC,IAAI,KAAK,uBAAuB,KAAK,IAAI,CAAC,CAAC;AAAA,IACvD,KAAK,SAAS;AACV,aAAO,CAAC,IAAI,KAAK,uBAAuB,KAAK,IAAI,CAAC,CAAC;AAAA,EAC3D;AACJ;AACA,SAAS,cAAc,EAAE,SAAS,SAAS,iBAAiB,SAAS,QAAQ,SAAS,SAAS,iBAAiB,SAAS,KAAK,YAAY,KAAM,GAAG;AAC/I,QAAM,CAAC,gBAAgB,cAAc,IAAI,wBAAwB;AAAA,IAC7D,KAAK;AAAA,IACL,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,GAAG;AAAA,EACP,CAAC;AACD,QAAM,CAAC,gBAAgB,cAAc,IAAI,wBAAwB;AAAA,IAC7D,KAAK;AAAA,IACL,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,IACJ,GAAG;AAAA,EACP,CAAC;AACD,QAAM,CAAC,QAAQ,QAAQ,SAAS,OAAO,IAAI,oBAAoB;AAAA,IAC3D;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;AACD,SAAO;AAAA,IACH,IAAI,OAAO,IAAI,OAAO,KAAK,cAAc,IAAI,cAAc,IAAI,cAAc,IAAI,cAAc,IAAI,OAAO,IAAI,OAAO;AAAA,IACrH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AACA,IAAM,iBAAa,oBAAK,CAAC,EAAE,SAAS,SAAS,SAAS,SAAS,iBAAiB,SAAS,QAAQ,iBAAiB,SAAS,KAAK,OAAO,YAAY,aAAa,cAAc,gBAAgB,qBAAqB,OAAAX,QAAO,WAAW,aAAa,aAAa,iBAAkB,MAAM;AACnR,QAAM,CAAC,MAAM,QAAQ,MAAM,IAAI,cAAc;AAAA,IACzC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,WAAW,2CAAa;AAAA,EAC5B,CAAC;AACD,SAAQ,cAAAC,QAAM,cAAc,UAAU,EAAE,MAAY,QAAgB,QAAgB,OAAc,YAAwB,aAA0B,cAA4B,gBAAgC,qBAA0C,OAAOD,QAAO,WAAsB,aAA0B,iBAAmC,CAAC;AAChW,CAAC;AACD,WAAW,cAAc;AAEzB,IAAM,oBAAgB,6BAAc,IAAI;AACxC,IAAM,WAAW,cAAc;AAC/B,cAAc;AACd,IAAM,YAAY,MAAM;AACpB,QAAM,aAAS,0BAAW,aAAa;AACvC,SAAO;AACX;AAEA,IAAM,SAAS,CAAC,YAAY,QAAQ,WAAW,YAAY,WAAW,YAAY;AAClF,IAAM,SAAS,CAAC,YAAY,QAAQ,WAAW,EAAE,YAAY,YAAY,EAAE,YAAY;AACvF,IAAM,cAAc,CAAC,MAAM,OAAO,UAAU;AACxC,MAAI,CAAC,OAAO,IAAI,GAAG;AACf,WAAO,CAAC;AAAA,EACZ;AACA,QAAM,aAAa,MAAM,OAAO,CAAC,MAAM,EAAE,WAAW,KAAK,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,MAAM;AAChF,SAAO,MAAM,OAAO,CAAC,MAAM,WAAW,SAAS,EAAE,EAAE,CAAC;AACxD;AACA,IAAM,cAAc,CAAC,MAAM,OAAO,UAAU;AACxC,MAAI,CAAC,OAAO,IAAI,GAAG;AACf,WAAO,CAAC;AAAA,EACZ;AACA,QAAM,cAAc,MAAM,OAAO,CAAC,MAAM,EAAE,WAAW,KAAK,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,MAAM;AACjF,SAAO,MAAM,OAAO,CAAC,MAAM,YAAY,SAAS,EAAE,EAAE,CAAC;AACzD;AACA,IAAM,YAAY,CAAC,EAAE,QAAQ,cAAc,QAAQ,aAAa,MAAM,mBAAmB,MAAM,GAAG,gBAAgB,EAAE,IAAI,MAAM,GAAG,gBAAgB,EAAE;AACnJ,IAAM,cAAc,CAAC,QAAQ,SAAS;AAClC,MAAI,OAAO,WAAW,aAAa;AAC/B,WAAO;AAAA,EACX;AACA,MAAI,OAAO,WAAW,UAAU;AAC5B,WAAO;AAAA,EACX;AACA,QAAM,WAAW,OAAO,GAAG,IAAI,OAAO;AACtC,SAAO,GAAG,QAAQ,GAAG,OAAO,KAAK,MAAM,EAClC,KAAK,EACL,IAAI,CAAC,QAAQ,GAAG,GAAG,IAAI,OAAO,GAAG,CAAC,EAAE,EACpC,KAAK,GAAG,CAAC;AAClB;AACA,IAAM,mBAAmB,CAAC,MAAM,UAAU;AACtC,SAAO,MAAM,KAAK,CAAC,OAAO,GAAG,WAAW,KAAK,UACzC,GAAG,WAAW,KAAK,WAClB,GAAG,iBAAiB,KAAK,gBAAiB,CAAC,GAAG,gBAAgB,CAAC,KAAK,kBACpE,GAAG,iBAAiB,KAAK,gBAAiB,CAAC,GAAG,gBAAgB,CAAC,KAAK,aAAc;AAC3F;AACA,IAAM,UAAU,CAAC,YAAY,UAAU;AACnC,MAAI,CAAC,WAAW,UAAU,CAAC,WAAW,QAAQ;AAC1C,YAAQ,OAAO,cAAc,UAAU,EAAE,CAAC;AAC1C,WAAO;AAAA,EACX;AACA,MAAI;AACJ,MAAI,OAAO,UAAU,GAAG;AACpB,WAAO,EAAE,GAAG,WAAW;AAAA,EAC3B,OACK;AACD,WAAO;AAAA,MACH,GAAG;AAAA,MACH,IAAI,UAAU,UAAU;AAAA,IAC5B;AAAA,EACJ;AACA,MAAI,iBAAiB,MAAM,KAAK,GAAG;AAC/B,WAAO;AAAA,EACX;AACA,SAAO,MAAM,OAAO,IAAI;AAC5B;AACA,IAAM,gBAAgB,CAAC,SAAS,eAAe,OAAO,UAAU,EAAE,iBAAiB,KAAK,MAAM;AAC1F,QAAM,EAAE,IAAI,WAAW,GAAG,KAAK,IAAI;AACnC,MAAI,CAAC,cAAc,UAAU,CAAC,cAAc,QAAQ;AAChD,YAAQ,OAAO,cAAc,UAAU,EAAE,CAAC;AAC1C,WAAO;AAAA,EACX;AACA,QAAM,YAAY,MAAM,KAAK,CAAC,MAAM,EAAE,OAAO,SAAS;AACtD,MAAI,CAAC,WAAW;AACZ,YAAQ,OAAO,cAAc,UAAU,EAAE,SAAS,CAAC;AACnD,WAAO;AAAA,EACX;AAEA,QAAM,OAAO;AAAA,IACT,GAAG;AAAA,IACH,IAAI,QAAQ,kBAAkB,UAAU,aAAa,IAAI;AAAA,IACzD,QAAQ,cAAc;AAAA,IACtB,QAAQ,cAAc;AAAA,IACtB,cAAc,cAAc;AAAA,IAC5B,cAAc,cAAc;AAAA,EAChC;AACA,SAAO,MAAM,OAAO,CAAC,MAAM,EAAE,OAAO,SAAS,EAAE,OAAO,IAAI;AAC9D;AAKA,IAAM,aAAa,CAAC,SAAS,eAAe,OAAO,UAAU,EAAE,iBAAiB,KAAK,MAAM;AACvF,UAAQ,KAAK,+HAA+H;AAC5I,SAAO,cAAc,SAAS,eAAe,OAAO,OAAO;AAC/D;AACA,IAAM,uBAAuB,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,IAAI,IAAI,MAAM,GAAG,YAAY,CAAC,OAAO,KAAK,MAAM;AACrF,QAAM,WAAW;AAAA,IACb,IAAI,IAAI,MAAM;AAAA,IACd,IAAI,IAAI,MAAM;AAAA,EAClB;AACA,MAAI,YAAY;AACZ,WAAO;AAAA,MACH,GAAG,QAAQ,KAAK,MAAM,SAAS,IAAI,KAAK;AAAA,MACxC,GAAG,QAAQ,KAAK,MAAM,SAAS,IAAI,KAAK;AAAA,IAC5C;AAAA,EACJ;AACA,SAAO;AACX;AACA,IAAM,uBAAuB,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,IAAI,IAAI,MAAM,MAAM;AACzD,SAAO;AAAA,IACH,GAAG,IAAI,SAAS;AAAA,IAChB,GAAG,IAAI,SAAS;AAAA,EACpB;AACJ;AACA,IAAM,4BAA4B,CAAC,MAAM,aAAa,CAAC,GAAG,CAAC,MAAM;AAC7D,MAAI,CAAC,MAAM;AACP,WAAO;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,kBAAkB;AAAA,QACd,GAAG;AAAA,QACH,GAAG;AAAA,MACP;AAAA,IACJ;AAAA,EACJ;AACA,QAAM,WAAW,KAAK,SAAS,KAAK,WAAW,CAAC;AAChD,QAAM,WAAW,KAAK,UAAU,KAAK,WAAW,CAAC;AACjD,QAAM,WAAW;AAAA,IACb,GAAG,KAAK,SAAS,IAAI;AAAA,IACrB,GAAG,KAAK,SAAS,IAAI;AAAA,EACzB;AACA,SAAO;AAAA,IACH,GAAG;AAAA,IACH,kBAAkB,KAAK,mBACjB;AAAA,MACE,GAAG,KAAK,iBAAiB,IAAI;AAAA,MAC7B,GAAG,KAAK,iBAAiB,IAAI;AAAA,IACjC,IACE;AAAA,EACV;AACJ;AACA,IAAM,iBAAiB,CAAC,OAAO,aAAa,CAAC,GAAG,CAAC,MAAM;AACnD,MAAI,MAAM,WAAW,GAAG;AACpB,WAAO,EAAE,GAAG,GAAG,GAAG,GAAG,OAAO,GAAG,QAAQ,EAAE;AAAA,EAC7C;AACA,QAAM,MAAM,MAAM,OAAO,CAAC,SAAS,SAAS;AACxC,UAAM,EAAE,GAAG,EAAE,IAAI,0BAA0B,MAAM,UAAU,EAAE;AAC7D,WAAO,iBAAiB,SAAS,UAAU;AAAA,MACvC;AAAA,MACA;AAAA,MACA,OAAO,KAAK,SAAS;AAAA,MACrB,QAAQ,KAAK,UAAU;AAAA,IAC3B,CAAC,CAAC;AAAA,EACN,GAAG,EAAE,GAAG,UAAU,GAAG,UAAU,IAAI,WAAW,IAAI,UAAU,CAAC;AAC7D,SAAO,UAAU,GAAG;AACxB;AAEA,IAAM,iBAAiB,CAAC,OAAO,aAAa,CAAC,GAAG,CAAC,MAAM;AACnD,UAAQ,KAAK,uIAAuI;AACpJ,SAAO,eAAe,OAAO,UAAU;AAC3C;AACA,IAAM,iBAAiB,CAAC,eAAe,MAAM,CAAC,IAAI,IAAI,MAAM,IAAI,CAAC,GAAG,GAAG,CAAC,GAAG,YAAY,OAEvF,4BAA4B,OAAO,aAAa,CAAC,GAAG,CAAC,MAAM;AACvD,QAAM,WAAW;AAAA,IACb,IAAI,KAAK,IAAI,MAAM;AAAA,IACnB,IAAI,KAAK,IAAI,MAAM;AAAA,IACnB,OAAO,KAAK,QAAQ;AAAA,IACpB,QAAQ,KAAK,SAAS;AAAA,EAC1B;AACA,QAAM,eAAe,CAAC;AACtB,gBAAc,QAAQ,CAAC,SAAS;AAC5B,UAAM,EAAE,OAAO,QAAQ,aAAa,MAAM,SAAS,MAAM,IAAI;AAC7D,QAAK,6BAA6B,CAAC,cAAe,QAAQ;AACtD,aAAO;AAAA,IACX;AACA,UAAM,EAAE,iBAAiB,IAAI,0BAA0B,MAAM,UAAU;AACvE,UAAM,WAAW;AAAA,MACb,GAAG,iBAAiB;AAAA,MACpB,GAAG,iBAAiB;AAAA,MACpB,OAAO,SAAS;AAAA,MAChB,QAAQ,UAAU;AAAA,IACtB;AACA,UAAM,kBAAkB,mBAAmB,UAAU,QAAQ;AAC7D,UAAM,iBAAiB,OAAO,UAAU,eAAe,OAAO,WAAW,eAAe,UAAU,QAAQ,WAAW;AACrH,UAAM,mBAAmB,aAAa,kBAAkB;AACxD,UAAM,QAAQ,SAAS,MAAM,UAAU;AACvC,UAAM,YAAY,kBAAkB,oBAAoB,mBAAmB;AAC3E,QAAI,aAAa,KAAK,UAAU;AAC5B,mBAAa,KAAK,IAAI;AAAA,IAC1B;AAAA,EACJ,CAAC;AACD,SAAO;AACX;AACA,IAAM,oBAAoB,CAAC,OAAO,UAAU;AACxC,QAAM,UAAU,MAAM,IAAI,CAAC,SAAS,KAAK,EAAE;AAC3C,SAAO,MAAM,OAAO,CAAC,SAAS,QAAQ,SAAS,KAAK,MAAM,KAAK,QAAQ,SAAS,KAAK,MAAM,CAAC;AAChG;AAEA,IAAM,wBAAwB,CAAC,QAAQ,OAAO,QAAQ,SAAS,SAAS,UAAU,QAAQ;AACtF,QAAM,EAAE,GAAG,GAAG,KAAK,IAAI,qBAAqB,QAAQ,OAAO,QAAQ,SAAS,SAAS,OAAO;AAC5F,UAAQ,KAAK,uSAAuS;AACpT,SAAO,CAAC,GAAG,GAAG,IAAI;AACtB;AACA,IAAM,uBAAuB,CAAC,QAAQ,OAAO,QAAQ,SAAS,SAAS,UAAU,QAAQ;AACrF,QAAM,QAAQ,SAAS,OAAO,SAAS,IAAI;AAC3C,QAAM,QAAQ,UAAU,OAAO,UAAU,IAAI;AAC7C,QAAM,OAAO,KAAK,IAAI,OAAO,KAAK;AAClC,QAAM,cAAc,MAAM,MAAM,SAAS,OAAO;AAChD,QAAM,gBAAgB,OAAO,IAAI,OAAO,QAAQ;AAChD,QAAM,gBAAgB,OAAO,IAAI,OAAO,SAAS;AACjD,QAAM,IAAI,QAAQ,IAAI,gBAAgB;AACtC,QAAM,IAAI,SAAS,IAAI,gBAAgB;AACvC,SAAO,EAAE,GAAG,GAAG,MAAM,YAAY;AACrC;AACA,IAAM,kBAAkB,CAAC,WAAW,WAAW,MAAM;AACjD,SAAO,UAAU,WAAW,EAAE,SAAS,QAAQ;AACnD;AAIA,SAAS,WAAW,MAAM,cAAc,MAAM,eAAe;AACzD,UAAQ,aAAa,IAAI,KAAK,CAAC,GAAG,OAAO,CAAC,KAAK,MAAM;AAtxBzD;AAuxBQ,QAAI,GAAG,KAAK,EAAE,IAAI,EAAE,EAAE,IAAI,IAAI,OAAO,eAAe;AAChD,UAAI,KAAK;AAAA,QACL,IAAI,EAAE,MAAM;AAAA,QACZ;AAAA,QACA,QAAQ,KAAK;AAAA,QACb,MAAI,UAAK,qBAAL,mBAAuB,MAAK,KAAK,EAAE,IAAI,EAAE,QAAQ;AAAA,QACrD,MAAI,UAAK,qBAAL,mBAAuB,MAAK,KAAK,EAAE,IAAI,EAAE,SAAS;AAAA,MAC1D,CAAC;AAAA,IACL;AACA,WAAO;AAAA,EACX,GAAG,CAAC,CAAC;AACT;AACA,SAAS,iBAAiB,OAAO,KAAK,KAAK,kBAAkB,SAAS,WAAW;AAG7E,QAAM,EAAE,GAAG,EAAE,IAAI,iBAAiB,KAAK;AACvC,QAAM,WAAW,IAAI,kBAAkB,GAAG,CAAC;AAC3C,QAAM,cAAc,SAAS,KAAK,CAAC,OAAO,GAAG,UAAU,SAAS,oBAAoB,CAAC;AACrF,MAAI,aAAa;AACb,UAAM,eAAe,YAAY,aAAa,aAAa;AAC3D,QAAI,cAAc;AACd,YAAM,aAAa,cAAc,QAAW,WAAW;AACvD,YAAM,WAAW,YAAY,aAAa,eAAe;AACzD,YAAM,oBAAoB,UAAU,EAAE,QAAQ,cAAc,IAAI,UAAU,MAAM,WAAW,CAAC;AAC5F,UAAI,mBAAmB;AACnB,cAAM,SAAS,QAAQ,KAAK,CAAC,MAAM,EAAE,WAAW,gBAAgB,EAAE,SAAS,cAAc,EAAE,OAAO,QAAQ;AAC1G,eAAO;AAAA,UACH,QAAQ;AAAA,YACJ,IAAI;AAAA,YACJ,MAAM;AAAA,YACN,QAAQ;AAAA,YACR,IAAG,iCAAQ,MAAK,IAAI;AAAA,YACpB,IAAG,iCAAQ,MAAK,IAAI;AAAA,UACxB;AAAA,UACA;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAEA,MAAI,iBAAiB,CAAC;AACtB,MAAI,cAAc;AAClB,UAAQ,QAAQ,CAAC,WAAW;AACxB,UAAMW,YAAW,KAAK,MAAM,OAAO,IAAI,IAAI,MAAM,KAAK,OAAO,IAAI,IAAI,MAAM,CAAC;AAC5E,QAAIA,aAAY,kBAAkB;AAC9B,YAAM,oBAAoB,UAAU,MAAM;AAC1C,UAAIA,aAAY,aAAa;AACzB,YAAIA,YAAW,aAAa;AACxB,2BAAiB,CAAC,EAAE,QAAQ,kBAAkB,CAAC;AAAA,QACnD,WACSA,cAAa,aAAa;AAE/B,yBAAe,KAAK;AAAA,YAChB;AAAA,YACA;AAAA,UACJ,CAAC;AAAA,QACL;AACA,sBAAcA;AAAA,MAClB;AAAA,IACJ;AAAA,EACJ,CAAC;AACD,MAAI,CAAC,eAAe,QAAQ;AACxB,WAAO,EAAE,QAAQ,MAAM,mBAAmB,cAAc,EAAE;AAAA,EAC9D;AACA,MAAI,eAAe,WAAW,GAAG;AAC7B,WAAO,eAAe,CAAC;AAAA,EAC3B;AACA,QAAM,iBAAiB,eAAe,KAAK,CAAC,EAAE,kBAAkB,MAAM,kBAAkB,OAAO;AAC/F,QAAM,kBAAkB,eAAe,KAAK,CAAC,EAAE,OAAO,MAAM,OAAO,SAAS,QAAQ;AAEpF,SAAQ,eAAe,KAAK,CAAC,EAAE,QAAQ,kBAAkB,MAAM,kBAAkB,OAAO,SAAS,WAAY,iBAAiB,kBAAkB,UAAU,IAAK,KAAK,eAAe,CAAC;AACxL;AACA,IAAM,iBAAiB,EAAE,QAAQ,MAAM,QAAQ,MAAM,cAAc,MAAM,cAAc,KAAK;AAC5F,IAAM,gBAAgB,OAAO;AAAA,EACzB,eAAe;AAAA,EACf,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,WAAW;AACf;AAEA,SAAS,cAAc,QAAQ,gBAAgB,YAAY,cAAc,UAAU,mBAAmB,KAAK;AACvG,QAAM,WAAW,aAAa;AAC9B,QAAM,gBAAgB,IAAI,cAAc,gCAAgC,iCAAQ,MAAM,IAAI,iCAAQ,EAAE,IAAI,iCAAQ,IAAI,IAAI;AACxH,QAAM,SAAS;AAAA,IACX,GAAG,cAAc;AAAA,IACjB,eAAe;AAAA,EACnB;AACA,MAAI,eAAe;AACf,UAAM,aAAa,cAAc,QAAW,aAAa;AACzD,UAAM,eAAe,cAAc,aAAa,aAAa;AAC7D,UAAM,WAAW,cAAc,aAAa,eAAe;AAC3D,UAAM,cAAc,cAAc,UAAU,SAAS,aAAa;AAClE,UAAM,iBAAiB,cAAc,UAAU,SAAS,gBAAgB;AACxE,UAAM,aAAa;AAAA,MACf,QAAQ,WAAW,eAAe;AAAA,MAClC,cAAc,WAAW,WAAW;AAAA,MACpC,QAAQ,WAAW,aAAa;AAAA,MAChC,cAAc,WAAW,eAAe;AAAA,IAC5C;AACA,WAAO,aAAa;AACpB,UAAM,gBAAgB,eAAe;AAErC,UAAM,UAAU,kBACX,mBAAmB,eAAe,SAC5B,YAAY,eAAe,YAAc,CAAC,YAAY,eAAe,WACtE,iBAAiB,cAAc,aAAa;AACtD,QAAI,SAAS;AACT,aAAO,YAAY;AAAA,QACf,QAAQ;AAAA,QACR;AAAA,QACA,MAAM;AAAA,MACV;AACA,aAAO,UAAU,kBAAkB,UAAU;AAAA,IACjD;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,gBAAgB,EAAE,OAAO,QAAQ,UAAU,WAAW,GAAG;AAC9D,SAAO,MAAM,OAAO,CAAC,KAAK,SAAS;AAC/B,QAAI,KAAK,eAAe,GAAG;AACvB,YAAM,EAAE,aAAa,IAAI,KAAK,eAAe;AAC7C,UAAI,gBAAgB,CAAC;AACrB,UAAI,gBAAgB,CAAC;AACrB,UAAI,cAAc;AACd,wBAAgB,WAAW,MAAM,cAAc,UAAU,GAAG,MAAM,IAAI,QAAQ,IAAI,UAAU,EAAE;AAC9F,wBAAgB,WAAW,MAAM,cAAc,UAAU,GAAG,MAAM,IAAI,QAAQ,IAAI,UAAU,EAAE;AAAA,MAClG;AACA,UAAI,KAAK,GAAG,eAAe,GAAG,aAAa;AAAA,IAC/C;AACA,WAAO;AAAA,EACX,GAAG,CAAC,CAAC;AACT;AACA,SAAS,cAAc,iBAAiB,eAAe;AACnD,MAAI,iBAAiB;AACjB,WAAO;AAAA,EACX,WACS,+CAAe,UAAU,SAAS,WAAW;AAClD,WAAO;AAAA,EACX,WACS,+CAAe,UAAU,SAAS,WAAW;AAClD,WAAO;AAAA,EACX;AACA,SAAO;AACX;AACA,SAAS,kBAAkB,eAAe;AACtC,iDAAe,UAAU,OAAO,SAAS,cAAc,4BAA4B;AACvF;AACA,SAAS,oBAAoB,0BAA0B,eAAe;AAClE,MAAI,mBAAmB;AACvB,MAAI,eAAe;AACf,uBAAmB;AAAA,EACvB,WACS,4BAA4B,CAAC,eAAe;AACjD,uBAAmB;AAAA,EACvB;AACA,SAAO;AACX;AAEA,SAAS,kBAAkB,EAAE,OAAO,UAAU,QAAQ,WAAW,UAAU,UAAU,UAAU,mBAAmB,iBAAiB,eAAgB,GAAG;AAElJ,QAAM,MAAM,kBAAkB,MAAM,MAAM;AAC1C,QAAM,EAAE,gBAAgB,SAAS,kBAAkB,kBAAkB,gBAAgB,OAAO,UAAU,iBAAkB,IAAI,SAAS;AACrI,MAAI,YAAY;AAChB,MAAI;AACJ,QAAM,EAAE,GAAG,EAAE,IAAI,iBAAiB,KAAK;AACvC,QAAM,gBAAgB,2BAAK,iBAAiB,GAAG;AAC/C,QAAM,aAAa,cAAc,iBAAiB,aAAa;AAC/D,QAAM,kBAAkB,mCAAS;AACjC,MAAI,CAAC,mBAAmB,CAAC,YAAY;AACjC;AAAA,EACJ;AACA,MAAI;AACJ,MAAI,qBAAqB,iBAAiB,OAAO,eAAe;AAChE,MAAI,iBAAiB;AACrB,MAAI,aAAa;AACjB,MAAI,UAAU;AACd,MAAI,gBAAgB;AACpB,QAAM,eAAe,gBAAgB;AAAA,IACjC,OAAO,SAAS;AAAA,IAChB;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;AAED,QAAM,UAAU,MAAM;AAClB,QAAI,CAAC,kBAAkB;AACnB;AAAA,IACJ;AACA,UAAM,CAAC,WAAW,SAAS,IAAI,YAAY,oBAAoB,eAAe;AAC9E,UAAM,EAAE,GAAG,WAAW,GAAG,UAAU,CAAC;AACpC,gBAAY,sBAAsB,OAAO;AAAA,EAC7C;AACA,WAAS;AAAA,IACL;AAAA,IACA,kBAAkB;AAAA;AAAA,IAElB,kBAAkB;AAAA,IAClB,oBAAoB;AAAA,IACpB,sBAAsB;AAAA,IACtB,uBAAuB;AAAA,MACnB;AAAA,MACA;AAAA,MACA,MAAM;AAAA,IACV;AAAA,IACA,qBAAqB;AAAA,EACzB,CAAC;AACD,mDAAiB,OAAO,EAAE,QAAQ,UAAU,WAAW;AACvD,WAAS,cAAcC,QAAO;AAC1B,UAAM,EAAE,UAAU,IAAI,SAAS;AAC/B,yBAAqB,iBAAiBA,QAAO,eAAe;AAC5D,UAAM,EAAE,QAAQ,kBAAkB,IAAI,iBAAiBA,QAAO,KAAK,qBAAqB,oBAAoB,WAAW,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,kBAAkB,cAAc,CAACC,YAAW,cAAcA,SAAQ,gBAAgB,QAAQ,UAAU,WAAW,WAAW,UAAU,mBAAmB,GAAG,CAAC;AAClS,oBAAgB;AAChB,QAAI,CAAC,gBAAgB;AACjB,cAAQ;AACR,uBAAiB;AAAA,IACrB;AACA,oBAAgB,kBAAkB;AAClC,iBAAa,kBAAkB;AAC/B,cAAU,kBAAkB;AAC5B,aAAS;AAAA,MACL,oBAAoB,iBAAiB,UAC/B,qBAAqB;AAAA,QACnB,GAAG,cAAc;AAAA,QACjB,GAAG,cAAc;AAAA,MACrB,GAAG,SAAS,IACV;AAAA,MACN,kBAAkB,oBAAoB,CAAC,CAAC,eAAe,OAAO;AAAA,MAC9D,qBAAqB,kBAAkB;AAAA,IAC3C,CAAC;AACD,QAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,eAAe;AAC9C,aAAO,kBAAkB,gBAAgB;AAAA,IAC7C;AACA,QAAI,WAAW,WAAW,WAAW,UAAU,eAAe;AAC1D,wBAAkB,gBAAgB;AAClC,yBAAmB;AAEnB,oBAAc,UAAU,IAAI,cAAc,+BAA+B;AACzE,oBAAc,UAAU,OAAO,SAAS,OAAO;AAC/C,oBAAc,UAAU,OAAO,4BAA4B,OAAO;AAAA,IACtE;AAAA,EACJ;AACA,WAAS,YAAYD,QAAO;AAxgChC;AAygCQ,SAAK,iBAAiB,kBAAkB,cAAc,SAAS;AAC3D,6CAAY;AAAA,IAChB;AAGA,yBAAS,GAAE,iBAAX,4BAA0BA;AAC1B,QAAI,iBAAiB;AACjB,uDAAiBA;AAAA,IACrB;AACA,sBAAkB,gBAAgB;AAClC,qBAAiB;AACjB,yBAAqB,SAAS;AAC9B,qBAAiB;AACjB,cAAU;AACV,iBAAa;AACb,oBAAgB;AAChB,QAAI,oBAAoB,aAAa,aAAa;AAClD,QAAI,oBAAoB,WAAW,WAAW;AAC9C,QAAI,oBAAoB,aAAa,aAAa;AAClD,QAAI,oBAAoB,YAAY,WAAW;AAAA,EACnD;AACA,MAAI,iBAAiB,aAAa,aAAa;AAC/C,MAAI,iBAAiB,WAAW,WAAW;AAC3C,MAAI,iBAAiB,aAAa,aAAa;AAC/C,MAAI,iBAAiB,YAAY,WAAW;AAChD;AAEA,IAAM,cAAc,MAAM;AAC1B,IAAM,aAAa,CAAC,OAAO;AAAA,EACvB,uBAAuB,EAAE;AAAA,EACzB,gBAAgB,EAAE;AAAA,EAClB,gBAAgB,EAAE;AACtB;AACA,IAAM,qBAAqB,CAAC,QAAQ,UAAU,SAAS,CAAC,UAAU;AAC9D,QAAM,EAAE,uBAAuB,aAAa,qBAAqB,WAAW,4BAA4B,YAAa,IAAI;AACzH,SAAO;AAAA,IACH,aAAa,2CAAa,YAAW,WAAU,2CAAa,cAAa,aAAY,2CAAa,UAAS,SACtG,uCAAW,YAAW,WAAU,uCAAW,cAAa,aAAY,uCAAW,UAAS;AAAA,IAC7F,kBAAiB,2CAAa,YAAW,WAAU,2CAAa,cAAa,aAAY,2CAAa,UAAS;AAAA,EACnH;AACJ;AACA,IAAM,aAAS,0BAAW,CAAC,EAAE,OAAO,UAAU,WAAW,SAAS,KAAK,mBAAmB,gBAAgB,MAAM,qBAAqB,MAAM,mBAAmB,MAAM,IAAI,WAAW,UAAU,WAAW,aAAa,cAAc,GAAG,KAAK,GAAG,QAAQ;AAljCtP;AAmjCI,QAAM,WAAW,MAAM;AACvB,QAAM,WAAW,SAAS;AAC1B,QAAM,QAAQ,YAAY;AAC1B,QAAM,SAAS,UAAU;AACzB,QAAM,EAAE,gBAAgB,eAAe,IAAI,SAAS,YAAY,SAAO;AACvE,QAAM,EAAE,YAAY,gBAAgB,IAAI,SAAS,mBAAmB,QAAQ,UAAU,IAAI,GAAG,SAAO;AACpG,MAAI,CAAC,QAAQ;AACT,sBAAM,SAAS,GAAE,YAAjB,4BAA2B,OAAO,cAAc,UAAU,EAAE;AAAA,EAChE;AACA,QAAM,oBAAoB,CAAC,WAAW;AAClC,UAAM,EAAE,oBAAoB,WAAW,iBAAiB,gBAAgB,IAAI,MAAM,SAAS;AAC3F,UAAM,aAAa;AAAA,MACf,GAAG;AAAA,MACH,GAAG;AAAA,IACP;AACA,QAAI,iBAAiB;AACjB,YAAM,EAAE,OAAO,SAAS,IAAI,MAAM,SAAS;AAC3C,eAAS,QAAQ,YAAY,KAAK,CAAC;AAAA,IACvC;AACA,uDAAkB;AAClB,2CAAY;AAAA,EAChB;AACA,QAAM,gBAAgB,CAAC,UAAU;AAC7B,QAAI,CAAC,QAAQ;AACT;AAAA,IACJ;AACA,UAAM,mBAAmB,aAAa,KAAK;AAC3C,QAAI,uBAAwB,oBAAoB,MAAM,WAAW,KAAM,CAAC,mBAAmB;AACvF,wBAAkB;AAAA,QACd;AAAA,QACA;AAAA,QACA;AAAA,QACA,WAAW;AAAA,QACX;AAAA,QACA,UAAU,MAAM;AAAA,QAChB,UAAU,MAAM;AAAA,QAChB,mBAAmB,qBAAqB,MAAM,SAAS,EAAE,qBAAqB;AAAA,MAClF,CAAC;AAAA,IACL;AACA,QAAI,kBAAkB;AAClB,iDAAc;AAAA,IAClB,OACK;AACD,mDAAe;AAAA,IACnB;AAAA,EACJ;AACA,QAAM,UAAU,CAAC,UAAU;AACvB,UAAM,EAAE,qBAAqB,mBAAmB,4BAA4B,gBAAgB,mBAAmB,uBAAwB,IAAI,MAAM,SAAS;AAC1J,QAAI,CAAC,UAAW,CAAC,8BAA8B,CAAC,oBAAqB;AACjE;AAAA,IACJ;AACA,QAAI,CAAC,4BAA4B;AAC7B,iEAAsB,OAAO,EAAE,QAAQ,UAAU,YAAY,KAAK;AAClE,YAAM,SAAS,EAAE,4BAA4B,EAAE,QAAQ,MAAM,SAAS,EAAE,CAAC;AACzE;AAAA,IACJ;AACA,UAAM,MAAM,kBAAkB,MAAM,MAAM;AAC1C,UAAM,2BAA2B,qBAAqB,0BAA0B;AAChF,UAAM,EAAE,YAAY,QAAQ,IAAI,cAAc;AAAA,MAC1C;AAAA,MACA,IAAI;AAAA,MACJ;AAAA,IACJ,GAAG,gBAAgB,2BAA2B,QAAQ,2BAA2B,YAAY,MAAM,2BAA2B,MAAM,0BAA0B,GAAG;AACjK,QAAI,SAAS;AACT,wBAAkB,UAAU;AAAA,IAChC;AACA,2DAAoB;AACpB,UAAM,SAAS,EAAE,4BAA4B,KAAK,CAAC;AAAA,EACvD;AACA,SAAQ,cAAAX,QAAM,cAAc,OAAO,EAAE,iBAAiB,UAAU,eAAe,QAAQ,kBAAkB,UAAU,WAAW,GAAG,MAAM,IAAI,QAAQ,IAAI,IAAI,IAAI,WAAW,GAAG;AAAA,IACrK;AAAA,IACA,sBAAsB,QAAQ;AAAA,IAC9B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,MACI,QAAQ,CAAC;AAAA,MACT,QAAQ;AAAA,MACR,aAAa;AAAA,MACb,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,YAAY;AAAA;AAAA,MAEZ,qBAAqB,kBAAmB,sBAAsB,CAAC,cAAgB,oBAAoB;AAAA,IACvG;AAAA,EACJ,CAAC,GAAG,aAAa,eAAe,cAAc,eAAe,SAAS,iBAAiB,UAAU,QAAW,KAAU,GAAG,KAAK,GAAG,QAAQ;AACjJ,CAAC;AACD,OAAO,cAAc;AACrB,IAAI,eAAW,oBAAK,MAAM;AAE1B,IAAM,cAAc,CAAC,EAAE,MAAM,eAAe,iBAAiB,SAAS,KAAK,iBAAiB,SAAS,OAAQ,MAAM;AAC/G,SAAQ,cAAAA,QAAM;AAAA,IAAc,cAAAA,QAAM;AAAA,IAAU;AAAA,IACxC,cAAAA,QAAM,cAAc,UAAU,EAAE,MAAM,UAAU,UAAU,gBAAgB,cAA6B,CAAC;AAAA,IACxG,6BAAM;AAAA,IACN,cAAAA,QAAM,cAAc,UAAU,EAAE,MAAM,UAAU,UAAU,gBAAgB,cAA6B,CAAC;AAAA,EAAC;AACjH;AACA,YAAY,cAAc;AAC1B,IAAI,oBAAgB,oBAAK,WAAW;AAEpC,IAAM,YAAY,CAAC,EAAE,MAAM,eAAe,iBAAiB,SAAS,OAAO,MAAO,cAAAA,QAAM;AAAA,EAAc,cAAAA,QAAM;AAAA,EAAU;AAAA,EAClH,6BAAM;AAAA,EACN,cAAAA,QAAM,cAAc,UAAU,EAAE,MAAM,UAAU,UAAU,gBAAgB,cAA6B,CAAC;AAAC;AAC7G,UAAU,cAAc;AACxB,IAAI,kBAAc,oBAAK,SAAS;AAEhC,IAAM,aAAa,CAAC,EAAE,MAAM,eAAe,iBAAiB,SAAS,IAAI,MAAO,cAAAA,QAAM;AAAA,EAAc,cAAAA,QAAM;AAAA,EAAU;AAAA,EAChH,cAAAA,QAAM,cAAc,UAAU,EAAE,MAAM,UAAU,UAAU,gBAAgB,cAA6B,CAAC;AAAA,EACxG,6BAAM;AAAK;AACf,WAAW,cAAc;AACzB,IAAI,mBAAe,oBAAK,UAAU;AAElC,IAAM,YAAY,MAAM;AACxB,UAAU,cAAc;AAExB,IAAM,aAAa,CAAC,OAAO;AAAA,EACvB,eAAe,EAAE,SAAS,EAAE,OAAO,CAAC,MAAM,EAAE,QAAQ;AAAA,EACpD,eAAe,EAAE,MAAM,OAAO,CAAC,MAAM,EAAE,QAAQ,EAAE,IAAI,CAAC,OAAO,EAAE,GAAG,EAAE,EAAE;AAC1E;AACA,IAAM,WAAW,CAAC,QAAQ,IAAI;AAC9B,SAAS,SAAS,GAAG,GAAG;AACpB,SAAQ,UAAQ,EAAE,cAAc,IAAI,QAAQ,GAAG,EAAE,cAAc,IAAI,QAAQ,CAAC,KACxE,UAAQ,EAAE,cAAc,IAAI,QAAQ,GAAG,EAAE,cAAc,IAAI,QAAQ,CAAC;AAC5E;AAGA,IAAM,wBAAoB,oBAAK,CAAC,EAAE,kBAAkB,MAAM;AACtD,QAAM,QAAQ,YAAY;AAC1B,QAAM,EAAE,eAAe,cAAc,IAAI,SAAS,YAAY,QAAQ;AACtE,+BAAU,MAAM;AACZ,UAAM,SAAS,EAAE,OAAO,eAAe,OAAO,cAAc;AAC5D,2DAAoB;AACpB,UAAM,SAAS,EAAE,kBAAkB,QAAQ,CAAC,OAAO,GAAG,MAAM,CAAC;AAAA,EACjE,GAAG,CAAC,eAAe,eAAe,iBAAiB,CAAC;AACpD,SAAO;AACX,CAAC;AACD,kBAAkB,cAAc;AAChC,IAAM,iBAAiB,CAAC,MAAM,CAAC,CAAC,EAAE;AAClC,SAAS,UAAU,EAAE,kBAAkB,GAAG;AACtC,QAAM,0BAA0B,SAAS,cAAc;AACvD,MAAI,qBAAqB,yBAAyB;AAC9C,WAAO,cAAAA,QAAM,cAAc,mBAAmB,EAAE,kBAAqC,CAAC;AAAA,EAC1F;AACA,SAAO;AACX;AAEA,IAAM,aAAa,CAAC,OAAO;AAAA,EACvB,UAAU,EAAE;AAAA,EACZ,UAAU,EAAE;AAAA,EACZ,yBAAyB,EAAE;AAAA,EAC3B,YAAY,EAAE;AAAA,EACd,YAAY,EAAE;AAAA,EACd,oBAAoB,EAAE;AAAA,EACtB,eAAe,EAAE;AAAA,EACjB,OAAO,EAAE;AACb;AACA,SAAS,gBAAgB,OAAO,eAAe;AAC3C,+BAAU,MAAM;AACZ,QAAI,OAAO,UAAU,aAAa;AAC9B,oBAAc,KAAK;AAAA,IACvB;AAAA,EACJ,GAAG,CAAC,KAAK,CAAC;AACd;AAEA,SAAS,sBAAsB,KAAK,OAAO,UAAU;AACjD,+BAAU,MAAM;AACZ,QAAI,OAAO,UAAU,aAAa;AAC9B,eAAS,EAAE,CAAC,GAAG,GAAG,MAAM,CAAC;AAAA,IAC7B;AAAA,EACJ,GAAG,CAAC,KAAK,CAAC;AACd;AACA,IAAM,eAAe,CAAC,EAAE,OAAO,OAAO,cAAc,cAAc,WAAW,gBAAgB,cAAc,qBAAqB,mBAAmB,gBAAgB,kBAAkB,gBAAgB,gBAAgB,gBAAgB,sBAAsB,SAAS,SAAS,YAAY,eAAe,eAAe,oBAAoB,gBAAgB,UAAU,YAAY,iBAAiB,gBAAgB,oBAAoB,SAAAa,UAAS,gBAAgB,eAAe,eAAe,YAAY,iBAAiB,gBAAgB,iBAAiB,sBAAsB,qBAAqB,gBAAgB,YAAY,MAAM,kBAAkB,mBAAmB,SAAS,kBAAkB,mBAAmB,kBAAmB,MAAM;AAChtB,QAAM,EAAE,UAAU,UAAU,yBAAyB,YAAY,YAAY,oBAAoB,eAAe,MAAO,IAAI,SAAS,YAAY,SAAO;AACvJ,QAAM,QAAQ,YAAY;AAC1B,+BAAU,MAAM;AACZ,UAAM,oBAAoB,6CAAc,IAAI,CAAC,OAAO,EAAE,GAAG,GAAG,GAAG,mBAAmB;AAClF,4BAAwB,cAAc,iBAAiB;AACvD,WAAO,MAAM;AACT,YAAM;AAAA,IACV;AAAA,EACJ,GAAG,CAAC,CAAC;AACL,wBAAsB,sBAAsB,oBAAoB,MAAM,QAAQ;AAC9E,wBAAsB,kBAAkB,gBAAgB,MAAM,QAAQ;AACtE,wBAAsB,aAAa,WAAW,MAAM,QAAQ;AAC5D,wBAAsB,kBAAkB,gBAAgB,MAAM,QAAQ;AACtE,wBAAsB,gBAAgB,cAAc,MAAM,QAAQ;AAClE,wBAAsB,uBAAuB,qBAAqB,MAAM,QAAQ;AAChF,wBAAsB,qBAAqB,mBAAmB,MAAM,QAAQ;AAC5E,wBAAsB,kBAAkB,gBAAgB,MAAM,QAAQ;AACtE,wBAAsB,oBAAoB,kBAAkB,MAAM,QAAQ;AAC1E,wBAAsB,kBAAkB,gBAAgB,MAAM,QAAQ;AACtE,wBAAsB,kBAAkB,gBAAgB,MAAM,QAAQ;AACtE,wBAAsB,kBAAkB,gBAAgB,MAAM,QAAQ;AACtE,wBAAsB,sBAAsB,oBAAoB,MAAM,QAAQ;AAC9E,wBAAsB,wBAAwB,sBAAsB,MAAM,QAAQ;AAClF,wBAAsB,cAAc,YAAY,MAAM,QAAQ;AAC9D,wBAAsB,YAAY,UAAU,MAAM,QAAQ;AAC1D,wBAAsB,iBAAiB,eAAe,MAAM,QAAQ;AACpE,wBAAsB,iBAAiB,eAAe,MAAM,QAAQ;AACpE,wBAAsB,kBAAkB,gBAAgB,MAAM,QAAQ;AACtE,wBAAsB,iBAAiBA,UAAS,MAAM,QAAQ;AAC9D,wBAAsB,wBAAwB,gBAAgB,MAAM,QAAQ;AAC5E,wBAAsB,iBAAiB,eAAe,MAAM,QAAQ;AACpE,wBAAsB,iBAAiB,eAAe,MAAM,QAAQ;AACpE,wBAAsB,cAAc,YAAY,MAAM,QAAQ;AAC9D,wBAAsB,mBAAmB,iBAAiB,MAAM,QAAQ;AACxE,wBAAsB,kBAAkB,gBAAgB,MAAM,QAAQ;AACtE,wBAAsB,mBAAmB,iBAAiB,MAAM,QAAQ;AACxE,wBAAsB,wBAAwB,sBAAsB,MAAM,QAAQ;AAClF,wBAAsB,uBAAuB,qBAAqB,MAAM,QAAQ;AAChF,wBAAsB,kBAAkB,gBAAgB,MAAM,QAAQ;AACtE,wBAAsB,cAAc,YAAY,MAAM,QAAQ;AAC9D,wBAAsB,QAAQ,MAAM,MAAM,QAAQ;AAClD,wBAAsB,oBAAoB,kBAAkB,MAAM,QAAQ;AAC1E,wBAAsB,qBAAqB,mBAAmB,MAAM,QAAQ;AAC5E,wBAAsB,WAAW,SAAS,MAAM,QAAQ;AACxD,wBAAsB,oBAAoB,kBAAkB,MAAM,QAAQ;AAC1E,wBAAsB,qBAAqB,mBAAmB,MAAM,QAAQ;AAC5E,wBAAsB,qBAAqB,mBAAmB,MAAM,QAAQ;AAC5E,kBAAgB,OAAO,QAAQ;AAC/B,kBAAgB,OAAO,QAAQ;AAC/B,kBAAgB,SAAS,UAAU;AACnC,kBAAgB,SAAS,UAAU;AACnC,kBAAgB,iBAAiB,kBAAkB;AACnD,kBAAgB,YAAY,aAAa;AACzC,SAAO;AACX;AAEA,IAAM,QAAQ,EAAE,SAAS,OAAO;AAChC,IAAM,gBAAgB;AAAA,EAClB,UAAU;AAAA,EACV,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,UAAU;AAAA,EACV,MAAM;AAAA,EACN,UAAU;AACd;AACA,IAAM,qBAAqB;AAC3B,IAAM,qBAAqB;AAC3B,IAAM,oBAAoB;AAC1B,IAAM,aAAa,CAAC,MAAM,EAAE;AAC5B,SAAS,gBAAgB,EAAE,KAAK,GAAG;AAC/B,QAAM,kBAAkB,SAAS,UAAU;AAC3C,SAAQ,cAAAb,QAAM,cAAc,OAAO,EAAE,IAAI,GAAG,iBAAiB,IAAI,IAAI,IAAI,aAAa,aAAa,eAAe,QAAQ,OAAO,cAAc,GAAG,eAAe;AACrK;AACA,SAAS,iBAAiB,EAAE,MAAM,oBAAoB,GAAG;AACrD,SAAQ,cAAAA,QAAM;AAAA,IAAc,cAAAA,QAAM;AAAA,IAAU;AAAA,IACxC,cAAAA,QAAM;AAAA,MAAc;AAAA,MAAO,EAAE,IAAI,GAAG,kBAAkB,IAAI,IAAI,IAAI,MAAa;AAAA,MAC3E;AAAA,MACA,CAAC,uBAAuB;AAAA,MACxB;AAAA,MACA;AAAA,IAAG;AAAA,IACP,cAAAA,QAAM,cAAc,OAAO,EAAE,IAAI,GAAG,kBAAkB,IAAI,IAAI,IAAI,MAAa,GAAG,qGAAqG;AAAA,IACvL,CAAC,uBAAuB,cAAAA,QAAM,cAAc,iBAAiB,EAAE,KAAW,CAAC;AAAA,EAAC;AACpF;AAMA,IAAI,cAAc,CAAC,UAAU,MAAM,UAAU,EAAE,4BAA4B,KAAK,MAAM;AAClF,QAAM,CAAC,YAAY,aAAa,QAAI,wBAAS,KAAK;AAElD,QAAM,sBAAkB,sBAAO,KAAK;AAEpC,QAAM,kBAAc,sBAAO,oBAAI,IAAI,CAAC,CAAC,CAAC;AAOtC,QAAM,CAAC,UAAU,WAAW,QAAI,uBAAQ,MAAM;AAC1C,QAAI,YAAY,MAAM;AAClB,YAAM,aAAa,MAAM,QAAQ,OAAO,IAAI,UAAU,CAAC,OAAO;AAC9D,YAAM,OAAO,WAAW,OAAO,CAAC,OAAO,OAAO,OAAO,QAAQ,EAAE,IAAI,CAAC,OAAO,GAAG,MAAM,GAAG,CAAC;AACxF,YAAM,WAAW,KAAK,OAAO,CAAC,KAAK,SAAS,IAAI,OAAO,GAAG,IAAI,GAAG,CAAC,CAAC;AACnE,aAAO,CAAC,MAAM,QAAQ;AAAA,IAC1B;AACA,WAAO,CAAC,CAAC,GAAG,CAAC,CAAC;AAAA,EAClB,GAAG,CAAC,OAAO,CAAC;AACZ,+BAAU,MAAM;AACZ,UAAM,MAAM,OAAO,aAAa,cAAc,WAAW;AACzD,UAAM,UAAS,mCAAS,WAAU;AAClC,QAAI,YAAY,MAAM;AAClB,YAAM,cAAc,CAAC,UAAU;AAC3B,wBAAgB,UAAU,MAAM,WAAW,MAAM,WAAW,MAAM;AAClE,cAAM,iBAAiB,CAAC,gBAAgB,WAAY,gBAAgB,WAAW,CAAC,QAAQ,+BACpF,eAAe,KAAK;AACxB,YAAI,eAAe;AACf,iBAAO;AAAA,QACX;AACA,cAAM,YAAY,aAAa,MAAM,MAAM,WAAW;AACtD,oBAAY,QAAQ,IAAI,MAAM,SAAS,CAAC;AACxC,YAAI,cAAc,UAAU,YAAY,SAAS,KAAK,GAAG;AACrD,gBAAM,eAAe;AACrB,wBAAc,IAAI;AAAA,QACtB;AAAA,MACJ;AACA,YAAM,YAAY,CAAC,UAAU;AACzB,cAAM,iBAAiB,CAAC,gBAAgB,WAAY,gBAAgB,WAAW,CAAC,QAAQ,+BACpF,eAAe,KAAK;AACxB,YAAI,eAAe;AACf,iBAAO;AAAA,QACX;AACA,cAAM,YAAY,aAAa,MAAM,MAAM,WAAW;AACtD,YAAI,cAAc,UAAU,YAAY,SAAS,IAAI,GAAG;AACpD,wBAAc,KAAK;AACnB,sBAAY,QAAQ,MAAM;AAAA,QAC9B,OACK;AACD,sBAAY,QAAQ,OAAO,MAAM,SAAS,CAAC;AAAA,QAC/C;AAEA,YAAI,MAAM,QAAQ,QAAQ;AACtB,sBAAY,QAAQ,MAAM;AAAA,QAC9B;AACA,wBAAgB,UAAU;AAAA,MAC9B;AACA,YAAM,eAAe,MAAM;AACvB,oBAAY,QAAQ,MAAM;AAC1B,sBAAc,KAAK;AAAA,MACvB;AACA,uCAAQ,iBAAiB,WAAW;AACpC,uCAAQ,iBAAiB,SAAS;AAClC,aAAO,iBAAiB,QAAQ,YAAY;AAC5C,aAAO,MAAM;AACT,yCAAQ,oBAAoB,WAAW;AACvC,yCAAQ,oBAAoB,SAAS;AACrC,eAAO,oBAAoB,QAAQ,YAAY;AAAA,MACnD;AAAA,IACJ;AAAA,EACJ,GAAG,CAAC,SAAS,aAAa,CAAC;AAC3B,SAAO;AACX;AAEA,SAAS,cAAc,UAAU,aAAa,MAAM;AAChD,SAAQ,SAIH,OAAO,CAAC,SAAS,QAAQ,KAAK,WAAW,YAAY,IAAI,EAGzD,KAAK,CAAC,SAAS,KAAK,MAAM,CAAC,MAAM,YAAY,IAAI,CAAC,CAAC,CAAC;AAC7D;AACA,SAAS,aAAa,WAAW,aAAa;AAC1C,SAAO,YAAY,SAAS,SAAS,IAAI,SAAS;AACtD;AAEA,SAAS,qBAAqB,MAAM,eAAe,QAAQ,YAAY;AAn5CvE;AAo5CI,QAAM,WAAW,KAAK,cAAc,KAAK;AACzC,MAAI,CAAC,UAAU;AACX,WAAO;AAAA,EACX;AACA,QAAM,aAAa,cAAc,IAAI,QAAQ;AAC7C,QAAM,qBAAqB,0BAA0B,YAAY,UAAU;AAC3E,SAAO,qBAAqB,YAAY,eAAe;AAAA,IACnD,IAAI,OAAO,KAAK,KAAK,mBAAmB;AAAA,IACxC,IAAI,OAAO,KAAK,KAAK,mBAAmB;AAAA,IACxC,MAAI,gBAAW,eAAe,MAA1B,mBAA6B,MAAK,MAAM,OAAO,KAAK,OAAK,gBAAW,eAAe,MAA1B,mBAA6B,MAAK,IAAI,OAAO,KAAK;AAAA,EACnH,GAAG,UAAU;AACjB;AACA,SAAS,4BAA4B,eAAe,YAAY,aAAa;AACzE,gBAAc,QAAQ,CAAC,SAAS;AAj6CpC;AAk6CQ,UAAM,WAAW,KAAK,cAAc,KAAK;AACzC,QAAI,YAAY,CAAC,cAAc,IAAI,QAAQ,GAAG;AAC1C,YAAM,IAAI,MAAM,eAAe,QAAQ,YAAY;AAAA,IACvD;AACA,QAAI,aAAY,2CAAc,KAAK,MAAK;AACpC,YAAM,EAAE,GAAG,GAAG,EAAE,IAAI,qBAAqB,MAAM,eAAe;AAAA,QAC1D,GAAG,KAAK;AAAA,QACR,KAAG,UAAK,eAAe,MAApB,mBAAuB,MAAK;AAAA,MACnC,GAAG,UAAU;AACb,WAAK,mBAAmB;AAAA,QACpB;AAAA,QACA;AAAA,MACJ;AACA,WAAK,eAAe,EAAE,IAAI;AAC1B,UAAI,2CAAc,KAAK,KAAK;AACxB,aAAK,eAAe,EAAE,WAAW;AAAA,MACrC;AAAA,IACJ;AAAA,EACJ,CAAC;AACL;AACA,SAAS,oBAAoB,OAAO,eAAe,YAAY,sBAAsB;AACjF,QAAM,oBAAoB,oBAAI,IAAI;AAClC,QAAM,cAAc,CAAC;AACrB,QAAM,gBAAgB,uBAAuB,MAAO;AACpD,QAAM,QAAQ,CAAC,SAAS;AA17C5B;AA27CQ,UAAM,KAAK,UAAU,KAAK,MAAM,IAAI,KAAK,SAAS,MAAM,KAAK,WAAW,gBAAgB;AACxF,UAAM,gBAAgB,cAAc,IAAI,KAAK,EAAE;AAC/C,UAAM,YAAY;AAAA,MACd,GAAG;AAAA,MACH,kBAAkB;AAAA,QACd,GAAG,KAAK,SAAS;AAAA,QACjB,GAAG,KAAK,SAAS;AAAA,MACrB;AAAA,IACJ;AACA,UAAM,WAAW,KAAK,cAAc,KAAK;AACzC,QAAI,UAAU;AACV,kBAAY,QAAQ,IAAI;AAAA,IAC5B;AACA,UAAM,qBAAoB,+CAAe,UAAQ,+CAAe,UAAS,KAAK;AAC9E,WAAO,eAAe,WAAW,iBAAiB;AAAA,MAC9C,YAAY;AAAA,MACZ,OAAO;AAAA,QACH,cAAc,oBAAoB,UAAY,oDAAgB,qBAAhB,mBAAkC;AAAA,QAChF;AAAA,MACJ;AAAA,IACJ,CAAC;AACD,sBAAkB,IAAI,KAAK,IAAI,SAAS;AAAA,EAC5C,CAAC;AACD,8BAA4B,mBAAmB,YAAY,WAAW;AACtE,SAAO;AACX;AACA,SAAS,QAAQ,KAAK,UAAU,CAAC,GAAG;AAChC,QAAM,EAAE,UAAU,OAAO,QAAQ,SAAS,SAAS,QAAQ,aAAa,mBAAmB,eAAe,WAAY,IAAI,IAAI;AAC9H,QAAM,mBAAmB,QAAQ,WAAW,CAAC,qBAAqB;AAClE,QAAM,gBAAgB,UAAU;AAChC,MAAI,kBAAkB,oBAAoB,CAAC,QAAQ,UAAU;AACzD,UAAM,QAAQ,SAAS,EAAE,OAAO,CAAC,MAAM;AA19C/C;AA29CY,YAAM,YAAY,QAAQ,qBAAqB,EAAE,SAAS,EAAE,SAAS,CAAC,EAAE;AACxE,WAAI,aAAQ,UAAR,mBAAe,QAAQ;AACvB,eAAO,aAAa,QAAQ,MAAM,KAAK,CAAC,eAAe,WAAW,OAAO,EAAE,EAAE;AAAA,MACjF;AACA,aAAO;AAAA,IACX,CAAC;AACD,UAAM,mBAAmB,MAAM,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM;AAC/D,QAAI,MAAM,SAAS,KAAK,kBAAkB;AACtC,YAAM,SAAS,eAAe,OAAO,UAAU;AAC/C,YAAM,EAAE,GAAG,GAAG,KAAK,IAAI,qBAAqB,QAAQ,OAAO,QAAQ,QAAQ,WAAW,SAAS,QAAQ,WAAW,SAAS,QAAQ,WAAW,GAAG;AACjJ,YAAM,gBAAgB,SAAa,UAAU,GAAG,CAAC,EAAE,MAAM,IAAI;AAC7D,UAAI,OAAO,QAAQ,aAAa,YAAY,QAAQ,WAAW,GAAG;AAC9D,eAAO,UAAU,gBAAgB,aAAa,QAAQ,QAAQ,GAAG,aAAa;AAAA,MAClF,OACK;AACD,eAAO,UAAU,aAAa,aAAa;AAAA,MAC/C;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,oCAAoC,aAAa,eAAe;AACrE,cAAY,QAAQ,CAAC,WAAW;AAC5B,UAAM,OAAO,cAAc,IAAI,OAAO,EAAE;AACxC,QAAI,MAAM;AACN,oBAAc,IAAI,KAAK,IAAI;AAAA,QACvB,GAAG;AAAA,QACH,CAAC,eAAe,GAAG,KAAK,eAAe;AAAA,QACvC,UAAU,OAAO;AAAA,MACrB,CAAC;AAAA,IACL;AAAA,EACJ,CAAC;AACD,SAAO,IAAI,IAAI,aAAa;AAChC;AACA,SAAS,oCAAoC,aAAa,OAAO;AAC7D,SAAO,MAAM,IAAI,CAAC,MAAM;AACpB,UAAM,SAAS,YAAY,KAAK,CAACc,YAAWA,QAAO,OAAO,EAAE,EAAE;AAC9D,QAAI,QAAQ;AACR,QAAE,WAAW,OAAO;AAAA,IACxB;AACA,WAAO;AAAA,EACX,CAAC;AACL;AACA,SAAS,8BAA8B,EAAE,cAAc,cAAc,KAAK,IAAI,GAAG;AAC7E,QAAM,EAAE,eAAe,OAAO,eAAe,eAAe,iBAAiB,gBAAgB,IAAI,IAAI;AACrG,MAAI,6CAAc,QAAQ;AACtB,QAAI,iBAAiB;AACjB,UAAI,EAAE,eAAe,oCAAoC,cAAc,aAAa,EAAE,CAAC;AAAA,IAC3F;AACA,mDAAgB;AAAA,EACpB;AACA,MAAI,6CAAc,QAAQ;AACtB,QAAI,iBAAiB;AACjB,UAAI,EAAE,OAAO,oCAAoC,cAAc,KAAK,EAAE,CAAC;AAAA,IAC3E;AACA,mDAAgB;AAAA,EACpB;AACJ;AAGA,IAAM,OAAO,MAAM;AAAE;AACrB,IAAM,wBAAwB;AAAA,EAC1B,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,SAAS,MAAM;AAAA,EACf,aAAa;AAAA,EACb,aAAa,OAAO,EAAE,GAAG,GAAG,GAAG,GAAG,MAAM,EAAE;AAAA,EAC1C,SAAS,MAAM;AAAA,EACf,WAAW;AAAA,EACX,WAAW;AAAA,EACX,SAAS,CAAC,aAAa;AAAA,EACvB,sBAAsB,CAAC,aAAa;AAAA,EACpC,sBAAsB,CAAC,aAAa;AAAA,EACpC,qBAAqB;AACzB;AACA,IAAM,aAAa,CAAC,OAAO;AAAA,EACvB,QAAQ,EAAE;AAAA,EACV,aAAa,EAAE;AACnB;AACA,IAAM,oBAAoB,MAAM;AAC5B,QAAM,QAAQ,YAAY;AAC1B,QAAM,EAAE,QAAQ,YAAY,IAAI,SAAS,YAAY,SAAO;AAC5D,QAAM,8BAA0B,uBAAQ,MAAM;AAC1C,QAAI,eAAe,QAAQ;AACvB,aAAO;AAAA,QACH,QAAQ,CAAC,YAAY,OAAO,QAAQ,gBAAgB,aAAa,mCAAS,QAAQ,GAAG,GAAG;AAAA,QACxF,SAAS,CAAC,YAAY,OAAO,QAAQ,gBAAgB,aAAa,mCAAS,QAAQ,GAAG,IAAI,GAAG;AAAA,QAC7F,QAAQ,CAAC,WAAW,YAAY,OAAO,QAAQ,gBAAgB,aAAa,mCAAS,QAAQ,GAAG,SAAS;AAAA,QACzG,SAAS,MAAM,MAAM,SAAS,EAAE,UAAU,CAAC;AAAA,QAC3C,aAAa,CAAC,WAAW,YAAY;AACjC,gBAAM,CAAC,GAAG,GAAG,IAAI,IAAI,MAAM,SAAS,EAAE;AACtC,gBAAM,gBAAgB,SACjB,UAAU,UAAU,KAAK,GAAG,UAAU,KAAK,CAAC,EAC5C,MAAM,UAAU,QAAQ,IAAI;AACjC,iBAAO,UAAU,gBAAgB,aAAa,mCAAS,QAAQ,GAAG,aAAa;AAAA,QACnF;AAAA,QACA,aAAa,MAAM;AACf,gBAAM,CAAC,GAAG,GAAG,IAAI,IAAI,MAAM,SAAS,EAAE;AACtC,iBAAO,EAAE,GAAG,GAAG,KAAK;AAAA,QACxB;AAAA,QACA,SAAS,CAAC,YAAY,QAAQ,MAAM,UAAU,OAAO;AAAA,QACrD,WAAW,CAAC,GAAG,GAAG,YAAY;AAC1B,gBAAM,EAAE,OAAO,QAAQ,QAAQ,IAAI,MAAM,SAAS;AAClD,gBAAM,WAAW,QAAO,mCAAS,UAAS,cAAc,QAAQ,OAAO;AACvE,gBAAM,UAAU,QAAQ,IAAI,IAAI;AAChC,gBAAM,UAAU,SAAS,IAAI,IAAI;AACjC,gBAAM,YAAY,SAAa,UAAU,SAAS,OAAO,EAAE,MAAM,QAAQ;AACzE,iBAAO,UAAU,gBAAgB,aAAa,mCAAS,QAAQ,GAAG,SAAS;AAAA,QAC/E;AAAA,QACA,WAAW,CAAC,QAAQ,YAAY;AAC5B,gBAAM,EAAE,OAAO,QAAQ,SAAS,QAAQ,IAAI,MAAM,SAAS;AAC3D,gBAAM,EAAE,GAAG,GAAG,KAAK,IAAI,qBAAqB,QAAQ,OAAO,QAAQ,SAAS,UAAS,mCAAS,YAAW,GAAG;AAC5G,gBAAM,YAAY,SAAa,UAAU,GAAG,CAAC,EAAE,MAAM,IAAI;AACzD,iBAAO,UAAU,gBAAgB,aAAa,mCAAS,QAAQ,GAAG,SAAS;AAAA,QAC/E;AAAA;AAAA,QAEA,SAAS,CAAC,aAAa;AACnB,gBAAM,EAAE,WAAW,YAAY,SAAS,IAAI,MAAM,SAAS;AAC3D,kBAAQ,KAAK,6NAA6N;AAC1O,iBAAO,qBAAqB,UAAU,WAAW,YAAY,QAAQ;AAAA,QACzE;AAAA,QACA,sBAAsB,CAAC,aAAa;AAChC,gBAAM,EAAE,WAAW,YAAY,UAAU,QAAQ,IAAI,MAAM,SAAS;AACpE,cAAI,CAAC,SAAS;AACV,mBAAO;AAAA,UACX;AACA,gBAAM,EAAE,GAAG,MAAM,GAAG,KAAK,IAAI,QAAQ,sBAAsB;AAC3D,gBAAM,mBAAmB;AAAA,YACrB,GAAG,SAAS,IAAI;AAAA,YAChB,GAAG,SAAS,IAAI;AAAA,UACpB;AACA,iBAAO,qBAAqB,kBAAkB,WAAW,YAAY,QAAQ;AAAA,QACjF;AAAA,QACA,sBAAsB,CAAC,aAAa;AAChC,gBAAM,EAAE,WAAW,QAAQ,IAAI,MAAM,SAAS;AAC9C,cAAI,CAAC,SAAS;AACV,mBAAO;AAAA,UACX;AACA,gBAAM,EAAE,GAAG,MAAM,GAAG,KAAK,IAAI,QAAQ,sBAAsB;AAC3D,gBAAM,mBAAmB,qBAAqB,UAAU,SAAS;AACjE,iBAAO;AAAA,YACH,GAAG,iBAAiB,IAAI;AAAA,YACxB,GAAG,iBAAiB,IAAI;AAAA,UAC5B;AAAA,QACJ;AAAA,QACA,qBAAqB;AAAA,MACzB;AAAA,IACJ;AACA,WAAO;AAAA,EACX,GAAG,CAAC,QAAQ,WAAW,CAAC;AACxB,SAAO;AACX;AAGA,SAAS,eAAe;AACpB,QAAM,iBAAiB,kBAAkB;AACzC,QAAM,QAAQ,YAAY;AAC1B,QAAM,eAAW,2BAAY,MAAM;AAC/B,WAAO,MACF,SAAS,EACT,SAAS,EACT,IAAI,CAAC,OAAO,EAAE,GAAG,EAAE,EAAE;AAAA,EAC9B,GAAG,CAAC,CAAC;AACL,QAAM,cAAU,2BAAY,CAAC,OAAO;AAChC,WAAO,MAAM,SAAS,EAAE,cAAc,IAAI,EAAE;AAAA,EAChD,GAAG,CAAC,CAAC;AACL,QAAM,eAAW,2BAAY,MAAM;AAC/B,UAAM,EAAE,QAAQ,CAAC,EAAE,IAAI,MAAM,SAAS;AACtC,WAAO,MAAM,IAAI,CAAC,OAAO,EAAE,GAAG,EAAE,EAAE;AAAA,EACtC,GAAG,CAAC,CAAC;AACL,QAAM,cAAU,2BAAY,CAAC,OAAO;AAChC,UAAM,EAAE,QAAQ,CAAC,EAAE,IAAI,MAAM,SAAS;AACtC,WAAO,MAAM,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE;AAAA,EACxC,GAAG,CAAC,CAAC;AACL,QAAM,eAAW,2BAAY,CAAC,YAAY;AACtC,UAAM,EAAE,UAAAC,WAAU,UAAAC,WAAU,iBAAiB,cAAc,IAAI,MAAM,SAAS;AAC9E,UAAM,QAAQD,UAAS;AACvB,UAAM,YAAY,OAAO,YAAY,aAAa,QAAQ,KAAK,IAAI;AACnE,QAAI,iBAAiB;AACjB,MAAAC,UAAS,SAAS;AAAA,IACtB,WACS,eAAe;AACpB,YAAM,UAAU,UAAU,WAAW,IAC/B,MAAM,IAAI,CAAC,UAAU,EAAE,MAAM,UAAU,IAAI,KAAK,GAAG,EAAE,IACrD,UAAU,IAAI,CAAC,UAAU,EAAE,MAAM,MAAM,MAAM,QAAQ,EAAE;AAC7D,oBAAc,OAAO;AAAA,IACzB;AAAA,EACJ,GAAG,CAAC,CAAC;AACL,QAAM,eAAW,2BAAY,CAAC,YAAY;AACtC,UAAM,EAAE,QAAQ,CAAC,GAAG,UAAAC,WAAU,iBAAiB,cAAc,IAAI,MAAM,SAAS;AAChF,UAAM,YAAY,OAAO,YAAY,aAAa,QAAQ,KAAK,IAAI;AACnE,QAAI,iBAAiB;AACjB,MAAAA,UAAS,SAAS;AAAA,IACtB,WACS,eAAe;AACpB,YAAM,UAAU,UAAU,WAAW,IAC/B,MAAM,IAAI,CAAC,UAAU,EAAE,MAAM,UAAU,IAAI,KAAK,GAAG,EAAE,IACrD,UAAU,IAAI,CAAC,UAAU,EAAE,MAAM,MAAM,MAAM,QAAQ,EAAE;AAC7D,oBAAc,OAAO;AAAA,IACzB;AAAA,EACJ,GAAG,CAAC,CAAC;AACL,QAAM,eAAW,2BAAY,CAAC,YAAY;AACtC,UAAM,QAAQ,MAAM,QAAQ,OAAO,IAAI,UAAU,CAAC,OAAO;AACzD,UAAM,EAAE,UAAAF,WAAU,UAAAC,WAAU,iBAAiB,cAAc,IAAI,MAAM,SAAS;AAC9E,QAAI,iBAAiB;AACjB,YAAM,eAAeD,UAAS;AAC9B,YAAM,YAAY,CAAC,GAAG,cAAc,GAAG,KAAK;AAC5C,MAAAC,UAAS,SAAS;AAAA,IACtB,WACS,eAAe;AACpB,YAAM,UAAU,MAAM,IAAI,CAAC,UAAU,EAAE,MAAM,MAAM,MAAM,MAAM,EAAE;AACjE,oBAAc,OAAO;AAAA,IACzB;AAAA,EACJ,GAAG,CAAC,CAAC;AACL,QAAM,eAAW,2BAAY,CAAC,YAAY;AACtC,UAAM,YAAY,MAAM,QAAQ,OAAO,IAAI,UAAU,CAAC,OAAO;AAC7D,UAAM,EAAE,QAAQ,CAAC,GAAG,UAAAC,WAAU,iBAAiB,cAAc,IAAI,MAAM,SAAS;AAChF,QAAI,iBAAiB;AACjB,MAAAA,UAAS,CAAC,GAAG,OAAO,GAAG,SAAS,CAAC;AAAA,IACrC,WACS,eAAe;AACpB,YAAM,UAAU,UAAU,IAAI,CAAC,UAAU,EAAE,MAAM,MAAM,MAAM,MAAM,EAAE;AACrE,oBAAc,OAAO;AAAA,IACzB;AAAA,EACJ,GAAG,CAAC,CAAC;AACL,QAAM,eAAW,2BAAY,MAAM;AAC/B,UAAM,EAAE,UAAAF,WAAU,QAAQ,CAAC,GAAG,UAAU,IAAI,MAAM,SAAS;AAC3D,UAAM,CAAC,GAAG,GAAG,IAAI,IAAI;AACrB,WAAO;AAAA,MACH,OAAOA,UAAS,EAAE,IAAI,CAAC,OAAO,EAAE,GAAG,EAAE,EAAE;AAAA,MACvC,OAAO,MAAM,IAAI,CAAC,OAAO,EAAE,GAAG,EAAE,EAAE;AAAA,MAClC,UAAU;AAAA,QACN;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ,GAAG,CAAC,CAAC;AACL,QAAM,qBAAiB,2BAAY,CAAC,EAAE,OAAO,cAAc,OAAO,aAAa,MAAM;AACjF,UAAM,EAAE,eAAe,UAAAA,WAAU,OAAO,iBAAiB,iBAAiB,eAAe,eAAe,eAAe,cAAe,IAAI,MAAM,SAAS;AACzJ,UAAM,WAAW,gBAAgB,CAAC,GAAG,IAAI,CAAC,SAAS,KAAK,EAAE;AAC1D,UAAM,WAAW,gBAAgB,CAAC,GAAG,IAAI,CAAC,SAAS,KAAK,EAAE;AAC1D,UAAM,gBAAgBA,UAAS,EAAE,OAAO,CAAC,KAAK,SAAS;AACnD,YAAM,WAAW,KAAK,cAAc,KAAK;AACzC,YAAM,YAAY,CAAC,QAAQ,SAAS,KAAK,EAAE,KAAK,YAAY,IAAI,KAAK,CAAC,MAAM,EAAE,OAAO,QAAQ;AAC7F,YAAM,YAAY,OAAO,KAAK,cAAc,YAAY,KAAK,YAAY;AACzE,UAAI,cAAc,QAAQ,SAAS,KAAK,EAAE,KAAK,YAAY;AACvD,YAAI,KAAK,IAAI;AAAA,MACjB;AACA,aAAO;AAAA,IACX,GAAG,CAAC,CAAC;AACL,UAAM,iBAAiB,MAAM,OAAO,CAAC,MAAO,OAAO,EAAE,cAAc,YAAY,EAAE,YAAY,IAAK;AAClG,UAAM,kBAAkB,eAAe,OAAO,CAAC,MAAM,QAAQ,SAAS,EAAE,EAAE,CAAC;AAC3E,QAAI,iBAAiB,iBAAiB;AAClC,YAAM,iBAAiB,kBAAkB,eAAe,cAAc;AACtE,YAAM,gBAAgB,CAAC,GAAG,iBAAiB,GAAG,cAAc;AAC5D,YAAM,kBAAkB,cAAc,OAAO,CAAC,KAAK,SAAS;AACxD,YAAI,CAAC,IAAI,SAAS,KAAK,EAAE,GAAG;AACxB,cAAI,KAAK,KAAK,EAAE;AAAA,QACpB;AACA,eAAO;AAAA,MACX,GAAG,CAAC,CAAC;AACL,UAAI,mBAAmB,iBAAiB;AACpC,YAAI,iBAAiB;AACjB,gBAAM,SAAS;AAAA,YACX,OAAO,MAAM,OAAO,CAAC,MAAM,CAAC,gBAAgB,SAAS,EAAE,EAAE,CAAC;AAAA,UAC9D,CAAC;AAAA,QACL;AACA,YAAI,iBAAiB;AACjB,wBAAc,QAAQ,CAAC,SAAS;AAC5B,0BAAc,OAAO,KAAK,EAAE;AAAA,UAChC,CAAC;AACD,gBAAM,SAAS;AAAA,YACX,eAAe,IAAI,IAAI,aAAa;AAAA,UACxC,CAAC;AAAA,QACL;AAAA,MACJ;AACA,UAAI,gBAAgB,SAAS,GAAG;AAC5B,uDAAgB;AAChB,YAAI,eAAe;AACf,wBAAc,gBAAgB,IAAI,CAAC,QAAQ;AAAA,YACvC;AAAA,YACA,MAAM;AAAA,UACV,EAAE,CAAC;AAAA,QACP;AAAA,MACJ;AACA,UAAI,cAAc,SAAS,GAAG;AAC1B,uDAAgB;AAChB,YAAI,eAAe;AACf,gBAAM,cAAc,cAAc,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,MAAM,SAAS,EAAE;AAC3E,wBAAc,WAAW;AAAA,QAC7B;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ,GAAG,CAAC,CAAC;AACL,QAAM,kBAAc,2BAAY,CAAC,eAAe;AAC5C,UAAM,SAAS,aAAa,UAAU;AACtC,UAAM,OAAO,SAAS,OAAO,MAAM,SAAS,EAAE,cAAc,IAAI,WAAW,EAAE;AAC7E,QAAI,CAAC,UAAU,CAAC,MAAM;AAClB,aAAO,CAAC,MAAM,MAAM,MAAM;AAAA,IAC9B;AACA,UAAM,WAAW,SAAS,aAAa,WAAW,IAAI;AACtD,WAAO,CAAC,UAAU,MAAM,MAAM;AAAA,EAClC,GAAG,CAAC,CAAC;AACL,QAAM,2BAAuB,2BAAY,CAAC,YAAY,YAAY,MAAM,UAAU;AAC9E,UAAM,CAAC,UAAU,MAAM,MAAM,IAAI,YAAY,UAAU;AACvD,QAAI,CAAC,UAAU;AACX,aAAO,CAAC;AAAA,IACZ;AACA,YAAQ,SAAS,MAAM,SAAS,EAAE,SAAS,GAAG,OAAO,CAAC,MAAM;AACxD,UAAI,CAAC,WAAW,EAAE,OAAO,KAAK,MAAM,CAAC,EAAE,mBAAmB;AACtD,eAAO;AAAA,MACX;AACA,YAAM,eAAe,WAAW,CAAC;AACjC,YAAM,kBAAkB,mBAAmB,cAAc,QAAQ;AACjE,YAAM,mBAAmB,aAAa,kBAAkB;AACxD,aAAO,oBAAoB,mBAAmB,SAAS,QAAQ,SAAS;AAAA,IAC5E,CAAC;AAAA,EACL,GAAG,CAAC,CAAC;AACL,QAAM,yBAAqB,2BAAY,CAAC,YAAY,MAAM,YAAY,SAAS;AAC3E,UAAM,CAAC,QAAQ,IAAI,YAAY,UAAU;AACzC,QAAI,CAAC,UAAU;AACX,aAAO;AAAA,IACX;AACA,UAAM,kBAAkB,mBAAmB,UAAU,IAAI;AACzD,UAAM,mBAAmB,aAAa,kBAAkB;AACxD,WAAO,oBAAoB,mBAAmB,SAAS,QAAQ,SAAS;AAAA,EAC5E,GAAG,CAAC,CAAC;AACL,aAAO,uBAAQ,MAAM;AACjB,WAAO;AAAA,MACH,GAAG;AAAA,MACH;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,EACJ,GAAG;AAAA,IACC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;AACL;AAEA,IAAM,mBAAmB,EAAE,4BAA4B,MAAM;AAC7D,IAAI,sBAAsB,CAAC,EAAE,eAAe,sBAAsB,MAAM;AACpE,QAAM,QAAQ,YAAY;AAC1B,QAAM,EAAE,eAAe,IAAI,aAAa;AACxC,QAAM,mBAAmB,YAAY,eAAe,gBAAgB;AACpE,QAAM,2BAA2B,YAAY,qBAAqB;AAClE,+BAAU,MAAM;AACZ,QAAI,kBAAkB;AAClB,YAAM,EAAE,OAAO,SAAS,IAAI,MAAM,SAAS;AAC3C,YAAM,gBAAgB,SAAS,EAAE,OAAO,CAAC,SAAS,KAAK,QAAQ;AAC/D,YAAM,gBAAgB,MAAM,OAAO,CAAC,SAAS,KAAK,QAAQ;AAC1D,qBAAe,EAAE,OAAO,eAAe,OAAO,cAAc,CAAC;AAC7D,YAAM,SAAS,EAAE,sBAAsB,MAAM,CAAC;AAAA,IAClD;AAAA,EACJ,GAAG,CAAC,gBAAgB,CAAC;AACrB,+BAAU,MAAM;AACZ,UAAM,SAAS,EAAE,sBAAsB,yBAAyB,CAAC;AAAA,EACrE,GAAG,CAAC,wBAAwB,CAAC;AACjC;AAEA,SAAS,iBAAiB,cAAc;AACpC,QAAM,QAAQ,YAAY;AAC1B,+BAAU,MAAM;AACZ,QAAI;AACJ,UAAM,mBAAmB,MAAM;AA91DvC;AA+1DY,UAAI,CAAC,aAAa,SAAS;AACvB;AAAA,MACJ;AACA,YAAM,OAAO,cAAc,aAAa,OAAO;AAC/C,UAAI,KAAK,WAAW,KAAK,KAAK,UAAU,GAAG;AACvC,0BAAM,SAAS,GAAE,YAAjB,4BAA2B,OAAO,cAAc,UAAU,EAAE;AAAA,MAChE;AACA,YAAM,SAAS,EAAE,OAAO,KAAK,SAAS,KAAK,QAAQ,KAAK,UAAU,IAAI,CAAC;AAAA,IAC3E;AACA,qBAAiB;AACjB,WAAO,iBAAiB,UAAU,gBAAgB;AAClD,QAAI,aAAa,SAAS;AACtB,uBAAiB,IAAI,eAAe,MAAM,iBAAiB,CAAC;AAC5D,qBAAe,QAAQ,aAAa,OAAO;AAAA,IAC/C;AACA,WAAO,MAAM;AACT,aAAO,oBAAoB,UAAU,gBAAgB;AACrD,UAAI,kBAAkB,aAAa,SAAS;AACxC,uBAAe,UAAU,aAAa,OAAO;AAAA,MACjD;AAAA,IACJ;AAAA,EACJ,GAAG,CAAC,CAAC;AACT;AAEA,IAAM,iBAAiB;AAAA,EACnB,UAAU;AAAA,EACV,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,KAAK;AAAA,EACL,MAAM;AACV;AAGA,IAAM,cAAc,CAAC,cAAc,mBAAmB,aAAa,MAAM,eAAe,KAAK,aAAa,MAAM,eAAe,KAAK,aAAa,SAAS,eAAe;AACzK,IAAM,uBAAuB,CAAC,oBAAoB;AAAA,EAC9C,GAAG,eAAe;AAAA,EAClB,GAAG,eAAe;AAAA,EAClB,MAAM,eAAe;AACzB;AACA,IAAM,qBAAqB,CAAC,OAAO,cAAc,MAAM,OAAO,QAAQ,IAAI,SAAS,EAAE;AACrF,IAAM,kBAAkB,CAAC,WAAW,eAAe,eAAe,KAAK,MAAM,QAAQ,SAAS,KAAK,UAAU,SAAS,CAAC;AACvH,IAAM,aAAa,CAAC,UAAU;AAC1B,QAAM,SAAS,MAAM,WAAW,QAAQ,IAAI,KAAK;AACjD,SAAO,CAAC,MAAM,UAAU,MAAM,cAAc,IAAI,OAAO,MAAM,YAAY,IAAI,QAAS;AAC1F;AACA,IAAM,aAAa,CAAC,OAAO;AAAA,EACvB,QAAQ,EAAE;AAAA,EACV,aAAa,EAAE;AAAA,EACf,eAAe,EAAE;AAAA,EACjB,qBAAqB,EAAE;AAC3B;AACA,IAAM,WAAW,CAAC,EAAE,QAAQ,aAAa,WAAW,mBAAmB,eAAe,MAAM,cAAc,MAAM,cAAc,OAAO,mBAAmB,KAAK,kBAAkB,gBAAgB,MAAM,oBAAoB,MAAM,oBAAoB,YAAY,MAAM,iBAAiB,iBAAiB,SAAS,SAAS,uBAAuB,mBAAmB,MAAM,UAAU,kBAAkB,eAAgB,MAAM;AACvZ,QAAM,cAAU,sBAAO;AACvB,QAAM,QAAQ,YAAY;AAC1B,QAAM,yBAAqB,sBAAO,KAAK;AACvC,QAAM,iCAA6B,sBAAO,KAAK;AAC/C,QAAM,eAAW,sBAAO,IAAI;AAC5B,QAAM,oBAAgB,sBAAO,EAAE,GAAG,GAAG,GAAG,GAAG,MAAM,EAAE,CAAC;AACpD,QAAM,EAAE,QAAQ,aAAa,eAAe,oBAAoB,IAAI,SAAS,YAAY,SAAO;AAChG,QAAM,2BAA2B,YAAY,qBAAqB;AAClE,QAAM,kBAAc,sBAAO,CAAC;AAC5B,QAAM,qBAAiB,sBAAO,KAAK;AACnC,QAAM,uBAAmB,sBAAO;AAChC,mBAAiB,QAAQ;AACzB,+BAAU,MAAM;AACZ,QAAI,SAAS,SAAS;AAClB,YAAM,OAAO,SAAS,QAAQ,sBAAsB;AACpD,YAAM,iBAAiB,aAAK,EAAE,YAAY,CAAC,SAAS,OAAO,CAAC,EAAE,gBAAgB,eAAe;AAC7F,YAAM,YAAY,eAAO,SAAS,OAAO,EAAE,KAAK,cAAc;AAC9D,YAAM,mBAAmB,SACpB,UAAU,gBAAgB,GAAG,gBAAgB,CAAC,EAC9C,MAAM,MAAM,gBAAgB,MAAM,SAAS,OAAO,CAAC;AACxD,YAAM,SAAS;AAAA,QACX,CAAC,GAAG,CAAC;AAAA,QACL,CAAC,KAAK,OAAO,KAAK,MAAM;AAAA,MAC5B;AACA,YAAM,uBAAuB,eAAe,UAAU,EAAE,kBAAkB,QAAQ,eAAe;AACjG,qBAAe,UAAU,WAAW,oBAAoB;AACxD,qBAAe,WAAW,UAAU;AACpC,YAAM,SAAS;AAAA,QACX,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,eAAe,UAAU,GAAG,YAAY;AAAA;AAAA,QAExC,WAAW,CAAC,qBAAqB,GAAG,qBAAqB,GAAG,qBAAqB,CAAC;AAAA,QAClF,SAAS,SAAS,QAAQ,QAAQ,aAAa;AAAA,MACnD,CAAC;AAAA,IACL;AAAA,EACJ,GAAG,CAAC,CAAC;AACL,+BAAU,MAAM;AACZ,QAAI,eAAe,QAAQ;AACvB,UAAI,eAAe,CAAC,4BAA4B,CAAC,qBAAqB;AAClE,oBAAY,GAAG,cAAc,CAAC,UAAU;AACpC,cAAI,mBAAmB,OAAO,gBAAgB,GAAG;AAC7C,mBAAO;AAAA,UACX;AACA,gBAAM,eAAe;AACrB,gBAAM,yBAAyB;AAC/B,gBAAM,cAAc,YAAY,SAAS,QAAQ,EAAE,KAAK;AAExD,cAAI,MAAM,WAAW,aAAa;AAC9B,kBAAM,QAAQ,gBAAQ,KAAK;AAC3B,kBAAM,aAAa,WAAW,KAAK;AACnC,kBAAM,OAAO,cAAc,KAAK,IAAI,GAAG,UAAU;AAEjD,mBAAO,QAAQ,aAAa,MAAM,OAAO,KAAK;AAC9C;AAAA,UACJ;AAGA,gBAAM,iBAAiB,MAAM,cAAc,IAAI,KAAK;AACpD,cAAI,SAAS,oBAAoB,gBAAgB,WAAW,IAAI,MAAM,SAAS;AAC/E,cAAI,SAAS,oBAAoB,gBAAgB,aAAa,IAAI,MAAM,SAAS;AAEjF,cAAI,CAAC,QAAQ,KAAK,MAAM,YAAY,oBAAoB,gBAAgB,UAAU;AAC9E,qBAAS,MAAM,SAAS;AACxB,qBAAS;AAAA,UACb;AACA,iBAAO;AAAA,YAAY;AAAA,YAAa,EAAE,SAAS,eAAe;AAAA,YAAkB,EAAE,SAAS,eAAe;AAAA;AAAA,YAEtG,EAAE,UAAU,KAAK;AAAA,UAAC;AAClB,gBAAM,eAAe,qBAAqB,YAAY,SAAS,QAAQ,CAAC;AACxE,gBAAM,EAAE,uBAAuB,kBAAkB,oBAAoB,IAAI,MAAM,SAAS;AACxF,uBAAa,iBAAiB,OAAO;AAIrC,cAAI,CAAC,eAAe,SAAS;AACzB,2BAAe,UAAU;AACzB,uDAAc,OAAO;AACrB,2EAAwB;AAAA,UAC5B;AACA,cAAI,eAAe,SAAS;AACxB,6CAAS,OAAO;AAChB,iEAAmB;AACnB,6BAAiB,UAAU,WAAW,MAAM;AACxC,qDAAY,OAAO;AACnB,yEAAsB;AACtB,6BAAe,UAAU;AAAA,YAC7B,GAAG,GAAG;AAAA,UACV;AAAA,QACJ,GAAG,EAAE,SAAS,MAAM,CAAC;AAAA,MACzB,WACS,OAAO,kBAAkB,aAAa;AAC3C,oBAAY,GAAG,cAAc,SAAU,OAAO,GAAG;AAE7C,gBAAM,eAAe,CAAC,oBAAoB,MAAM,SAAS,WAAW,CAAC,MAAM;AAC3E,cAAI,gBAAgB,mBAAmB,OAAO,gBAAgB,GAAG;AAC7D,mBAAO;AAAA,UACX;AACA,gBAAM,eAAe;AACrB,wBAAc,KAAK,MAAM,OAAO,CAAC;AAAA,QACrC,GAAG,EAAE,SAAS,MAAM,CAAC;AAAA,MACzB;AAAA,IACJ;AAAA,EACJ,GAAG;AAAA,IACC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;AACD,+BAAU,MAAM;AACZ,QAAI,QAAQ;AACR,aAAO,GAAG,SAAS,CAAC,UAAU;AA3gE1C;AA4gEgB,YAAI,CAAC,MAAM,eAAe,MAAM,YAAY,UAAU;AAClD,iBAAO;AAAA,QACX;AAEA,oBAAY,WAAU,WAAM,gBAAN,mBAAmB;AACzC,cAAM,EAAE,sBAAsB,IAAI,MAAM,SAAS;AACjD,cAAM,gBAAgB,qBAAqB,MAAM,SAAS;AAC1D,2BAAmB,UAAU;AAC7B,sBAAc,UAAU;AACxB,cAAI,WAAM,gBAAN,mBAAmB,UAAS,aAAa;AACzC,gBAAM,SAAS,EAAE,cAAc,KAAK,CAAC;AAAA,QACzC;AACA,uEAAwB;AACxB,mDAAc,MAAM,aAAa;AAAA,MACrC,CAAC;AAAA,IACL;AAAA,EACJ,GAAG,CAAC,QAAQ,WAAW,CAAC;AACxB,+BAAU,MAAM;AACZ,QAAI,QAAQ;AACR,UAAI,uBAAuB,CAAC,mBAAmB,SAAS;AACpD,eAAO,GAAG,QAAQ,IAAI;AAAA,MAC1B,WACS,CAAC,qBAAqB;AAC3B,eAAO,GAAG,QAAQ,CAAC,UAAU;AAniE7C;AAoiEoB,gBAAM,EAAE,iBAAiB,IAAI,MAAM,SAAS;AAC5C,gBAAM,SAAS,EAAE,WAAW,CAAC,MAAM,UAAU,GAAG,MAAM,UAAU,GAAG,MAAM,UAAU,CAAC,EAAE,CAAC;AACvF,qCAA2B,UAAU,CAAC,EAAE,qBAAqB,gBAAgB,WAAW,YAAY,WAAW,CAAC;AAChH,eAAK,UAAU,qBAAqB,GAAC,WAAM,gBAAN,mBAAmB,WAAU;AAC9D,kBAAM,gBAAgB,qBAAqB,MAAM,SAAS;AAC1D,iEAAmB;AACnB,6CAAS,MAAM,aAAa;AAAA,UAChC;AAAA,QACJ,CAAC;AAAA,MACL;AAAA,IACJ;AAAA,EACJ,GAAG,CAAC,qBAAqB,QAAQ,QAAQ,WAAW,iBAAiB,CAAC;AACtE,+BAAU,MAAM;AACZ,QAAI,QAAQ;AACR,aAAO,GAAG,OAAO,CAAC,UAAU;AACxB,YAAI,CAAC,MAAM,eAAe,MAAM,YAAY,UAAU;AAClD,iBAAO;AAAA,QACX;AACA,cAAM,EAAE,oBAAoB,IAAI,MAAM,SAAS;AAC/C,2BAAmB,UAAU;AAC7B,cAAM,SAAS,EAAE,cAAc,MAAM,CAAC;AACtC,YAAI,qBACA,gBAAgB,WAAW,YAAY,WAAW,CAAC,KACnD,CAAC,2BAA2B,SAAS;AACrC,4BAAkB,MAAM,WAAW;AAAA,QACvC;AACA,mCAA2B,UAAU;AACrC,aAAK,aAAa,wBAAwB,YAAY,cAAc,SAAS,MAAM,SAAS,GAAG;AAC3F,gBAAM,gBAAgB,qBAAqB,MAAM,SAAS;AAC1D,wBAAc,UAAU;AACxB,uBAAa,QAAQ,OAAO;AAC5B,kBAAQ,UAAU,WAAW,MAAM;AAC/B,uEAAsB;AACtB,mDAAY,MAAM,aAAa;AAAA,UACnC,GAAG,cAAc,MAAM,CAAC;AAAA,QAC5B;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,EACJ,GAAG,CAAC,QAAQ,aAAa,WAAW,WAAW,iBAAiB,CAAC;AACjE,+BAAU,MAAM;AACZ,QAAI,QAAQ;AACR,aAAO,OAAO,CAAC,UAAU;AACrB,cAAM,aAAa,4BAA4B;AAC/C,cAAM,YAAY,eAAe,MAAM;AACvC,aAAK,cAAc,QAAS,MAAM,QAAQ,SAAS,KAAK,UAAU,SAAS,CAAC,MACxE,MAAM,WAAW,KACjB,MAAM,SAAS,gBACd,mBAAmB,OAAO,kBAAkB,KAAK,mBAAmB,OAAO,kBAAkB,IAAI;AAClG,iBAAO;AAAA,QACX;AAEA,YAAI,CAAC,aAAa,CAAC,cAAc,CAAC,eAAe,CAAC,qBAAqB,CAAC,aAAa;AACjF,iBAAO;AAAA,QACX;AAEA,YAAI,qBAAqB;AACrB,iBAAO;AAAA,QACX;AAEA,YAAI,CAAC,qBAAqB,MAAM,SAAS,YAAY;AACjD,iBAAO;AAAA,QACX;AAEA,YAAI,mBAAmB,OAAO,gBAAgB,KAAK,MAAM,SAAS,SAAS;AACvE,iBAAO;AAAA,QACX;AAEA,YAAI,mBAAmB,OAAO,cAAc,MACvC,MAAM,SAAS,WAAY,eAAe,MAAM,SAAS,WAAW,CAAC,2BAA4B;AAClG,iBAAO;AAAA,QACX;AACA,YAAI,CAAC,eAAe,MAAM,WAAW,MAAM,SAAS,SAAS;AACzD,iBAAO;AAAA,QACX;AAEA,YAAI,CAAC,cAAc,CAAC,eAAe,CAAC,aAAa,MAAM,SAAS,SAAS;AACrE,iBAAO;AAAA,QACX;AAEA,YAAI,CAAC,cAAc,MAAM,SAAS,eAAe,MAAM,SAAS,eAAe;AAC3E,iBAAO;AAAA,QACX;AAEA,YAAI,MAAM,QAAQ,SAAS,KAAK,CAAC,UAAU,SAAS,MAAM,MAAM,KAAK,MAAM,SAAS,aAAa;AAC7F,iBAAO;AAAA,QACX;AAEA,cAAM,gBAAiB,MAAM,QAAQ,SAAS,KAAK,UAAU,SAAS,MAAM,MAAM,KAAM,CAAC,MAAM,UAAU,MAAM,UAAU;AAEzH,gBAAQ,CAAC,MAAM,WAAW,MAAM,SAAS,YAAY;AAAA,MACzD,CAAC;AAAA,IACL;AAAA,EACJ,GAAG;AAAA,IACC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;AACD,SAAQ,cAAAf,QAAM,cAAc,OAAO,EAAE,WAAW,wBAAwB,KAAK,UAAU,OAAO,eAAe,GAAG,QAAQ;AAC5H;AAEA,IAAM,aAAa,CAAC,OAAO;AAAA,EACvB,qBAAqB,EAAE;AAAA,EACvB,mBAAmB,EAAE;AACzB;AACA,SAAS,gBAAgB;AACrB,QAAM,EAAE,qBAAqB,kBAAkB,IAAI,SAAS,YAAY,SAAO;AAC/E,QAAM,WAAW,uBAAuB;AACxC,MAAI,CAAC,UAAU;AACX,WAAO;AAAA,EACX;AACA,SAAQ,cAAAA,QAAM,cAAc,OAAO,EAAE,WAAW,+CAA+C,OAAO;AAAA,IAC9F,OAAO,kBAAkB;AAAA,IACzB,QAAQ,kBAAkB;AAAA,IAC1B,WAAW,aAAa,kBAAkB,CAAC,OAAO,kBAAkB,CAAC;AAAA,EACzE,EAAE,CAAC;AACX;AAEA,SAAS,mBAAmB,KAAK,YAAY;AACzC,QAAM,WAAW,WAAW,cAAc,WAAW;AACrD,QAAM,SAAS,IAAI,KAAK,CAAC,MAAM,EAAE,OAAO,QAAQ;AAChD,MAAI,QAAQ;AACR,UAAM,cAAc,WAAW,SAAS,IAAI,WAAW,QAAQ,OAAO;AACtE,UAAM,eAAe,WAAW,SAAS,IAAI,WAAW,SAAS,OAAO;AACxE,QAAI,cAAc,KAAK,eAAe,KAAK,WAAW,SAAS,IAAI,KAAK,WAAW,SAAS,IAAI,GAAG;AAC/F,aAAO,QAAQ,EAAE,GAAG,OAAO,MAAM;AACjC,aAAO,MAAM,QAAQ,OAAO,MAAM,SAAS,OAAO;AAClD,aAAO,MAAM,SAAS,OAAO,MAAM,UAAU,OAAO;AACpD,UAAI,cAAc,GAAG;AACjB,eAAO,MAAM,SAAS;AAAA,MAC1B;AACA,UAAI,eAAe,GAAG;AAClB,eAAO,MAAM,UAAU;AAAA,MAC3B;AACA,UAAI,WAAW,SAAS,IAAI,GAAG;AAC3B,cAAM,QAAQ,KAAK,IAAI,WAAW,SAAS,CAAC;AAC5C,eAAO,SAAS,IAAI,OAAO,SAAS,IAAI;AACxC,eAAO,MAAM,SAAS;AACtB,mBAAW,SAAS,IAAI;AAAA,MAC5B;AACA,UAAI,WAAW,SAAS,IAAI,GAAG;AAC3B,cAAM,QAAQ,KAAK,IAAI,WAAW,SAAS,CAAC;AAC5C,eAAO,SAAS,IAAI,OAAO,SAAS,IAAI;AACxC,eAAO,MAAM,UAAU;AACvB,mBAAW,SAAS,IAAI;AAAA,MAC5B;AACA,aAAO,QAAQ,OAAO,MAAM;AAC5B,aAAO,SAAS,OAAO,MAAM;AAAA,IACjC;AAAA,EACJ;AACJ;AACA,SAAS,aAAa,SAAS,UAAU;AAErC,MAAI,QAAQ,KAAK,CAAC,MAAM,EAAE,SAAS,OAAO,GAAG;AACzC,WAAO,QAAQ,OAAO,CAAC,MAAM,EAAE,SAAS,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI;AAAA,EACtE;AACA,QAAM,eAAe,QAAQ,OAAO,CAAC,MAAM,EAAE,SAAS,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI;AAC9E,SAAO,SAAS,OAAO,CAAC,KAAK,SAAS;AAClC,UAAM,iBAAiB,QAAQ,OAAO,CAAC,MAAM,EAAE,OAAO,KAAK,EAAE;AAC7D,QAAI,eAAe,WAAW,GAAG;AAC7B,UAAI,KAAK,IAAI;AACb,aAAO;AAAA,IACX;AACA,UAAM,aAAa,EAAE,GAAG,KAAK;AAC7B,eAAW,iBAAiB,gBAAgB;AACxC,UAAI,eAAe;AACf,gBAAQ,cAAc,MAAM;AAAA,UACxB,KAAK,UAAU;AACX,uBAAW,WAAW,cAAc;AACpC;AAAA,UACJ;AAAA,UACA,KAAK,YAAY;AACb,gBAAI,OAAO,cAAc,aAAa,aAAa;AAC/C,yBAAW,WAAW,cAAc;AAAA,YACxC;AACA,gBAAI,OAAO,cAAc,qBAAqB,aAAa;AACvD,yBAAW,mBAAmB,cAAc;AAAA,YAChD;AACA,gBAAI,OAAO,cAAc,aAAa,aAAa;AAC/C,yBAAW,WAAW,cAAc;AAAA,YACxC;AACA,gBAAI,WAAW,cAAc;AACzB,iCAAmB,KAAK,UAAU;AAAA,YACtC;AACA;AAAA,UACJ;AAAA,UACA,KAAK,cAAc;AACf,gBAAI,OAAO,cAAc,eAAe,aAAa;AACjD,yBAAW,QAAQ,cAAc,WAAW;AAC5C,yBAAW,SAAS,cAAc,WAAW;AAAA,YACjD;AACA,gBAAI,OAAO,cAAc,gBAAgB,aAAa;AAClD,yBAAW,QAAQ,EAAE,GAAI,WAAW,SAAS,CAAC,GAAI,GAAG,cAAc,WAAW;AAAA,YAClF;AACA,gBAAI,OAAO,cAAc,aAAa,WAAW;AAC7C,yBAAW,WAAW,cAAc;AAAA,YACxC;AACA,gBAAI,WAAW,cAAc;AACzB,iCAAmB,KAAK,UAAU;AAAA,YACtC;AACA;AAAA,UACJ;AAAA,UACA,KAAK,UAAU;AACX,mBAAO;AAAA,UACX;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,KAAK,UAAU;AACnB,WAAO;AAAA,EACX,GAAG,YAAY;AACnB;AACA,SAAS,iBAAiB,SAAS,OAAO;AACtC,SAAO,aAAa,SAAS,KAAK;AACtC;AACA,SAAS,iBAAiB,SAAS,OAAO;AACtC,SAAO,aAAa,SAAS,KAAK;AACtC;AACA,IAAM,wBAAwB,CAAC,IAAI,cAAc;AAAA,EAC7C;AAAA,EACA,MAAM;AAAA,EACN;AACJ;AACA,SAAS,oBAAoB,OAAO,aAAa;AAC7C,SAAO,MAAM,OAAO,CAAC,KAAK,SAAS;AAC/B,UAAM,iBAAiB,YAAY,SAAS,KAAK,EAAE;AACnD,QAAI,CAAC,KAAK,YAAY,gBAAgB;AAClC,WAAK,WAAW;AAChB,UAAI,KAAK,sBAAsB,KAAK,IAAI,IAAI,CAAC;AAAA,IACjD,WACS,KAAK,YAAY,CAAC,gBAAgB;AACvC,WAAK,WAAW;AAChB,UAAI,KAAK,sBAAsB,KAAK,IAAI,KAAK,CAAC;AAAA,IAClD;AACA,WAAO;AAAA,EACX,GAAG,CAAC,CAAC;AACT;AAKA,IAAM,cAAc,CAAC,SAAS,iBAAiB;AAC3C,SAAO,CAAC,UAAU;AACd,QAAI,MAAM,WAAW,aAAa,SAAS;AACvC;AAAA,IACJ;AACA,uCAAU;AAAA,EACd;AACJ;AACA,IAAM,aAAa,CAAC,OAAO;AAAA,EACvB,qBAAqB,EAAE;AAAA,EACvB,oBAAoB,EAAE;AAAA,EACtB,UAAU,EAAE;AAChB;AACA,IAAM,WAAO,oBAAK,CAAC,EAAE,aAAa,gBAAgB,cAAc,MAAM,WAAW,kBAAkB,gBAAgB,aAAa,mBAAmB,cAAc,kBAAkB,iBAAiB,kBAAkB,SAAU,MAAM;AAClO,QAAM,gBAAY,sBAAO,IAAI;AAC7B,QAAM,QAAQ,YAAY;AAC1B,QAAM,6BAAyB,sBAAO,CAAC;AACvC,QAAM,6BAAyB,sBAAO,CAAC;AACvC,QAAM,sBAAkB,sBAAO;AAC/B,QAAM,EAAE,qBAAqB,oBAAoB,SAAS,IAAI,SAAS,YAAY,SAAO;AAC1F,QAAM,qBAAqB,MAAM;AAC7B,UAAM,SAAS,EAAE,qBAAqB,OAAO,mBAAmB,KAAK,CAAC;AACtE,2BAAuB,UAAU;AACjC,2BAAuB,UAAU;AAAA,EACrC;AACA,QAAM,UAAU,CAAC,UAAU;AACvB,+CAAc;AACd,UAAM,SAAS,EAAE,sBAAsB;AACvC,UAAM,SAAS,EAAE,sBAAsB,MAAM,CAAC;AAAA,EAClD;AACA,QAAM,gBAAgB,CAAC,UAAU;AAC7B,QAAI,MAAM,QAAQ,SAAS,MAAK,uCAAW,SAAS,KAAI;AACpD,YAAM,eAAe;AACrB;AAAA,IACJ;AACA,2DAAoB;AAAA,EACxB;AACA,QAAM,UAAU,eAAe,CAAC,UAAU,aAAa,KAAK,IAAI;AAChE,QAAM,cAAc,CAAC,UAAU;AAC3B,UAAM,EAAE,uBAAuB,QAAQ,IAAI,MAAM,SAAS;AAC1D,oBAAgB,UAAU,mCAAS;AACnC,QAAI,CAAC,sBACD,CAAC,eACD,MAAM,WAAW,KACjB,MAAM,WAAW,UAAU,WAC3B,CAAC,gBAAgB,SAAS;AAC1B;AAAA,IACJ;AACA,UAAM,EAAE,GAAG,EAAE,IAAI,iBAAiB,OAAO,gBAAgB,OAAO;AAChE,0BAAsB;AACtB,UAAM,SAAS;AAAA,MACX,mBAAmB;AAAA,QACf,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR;AAAA,QACA;AAAA,MACJ;AAAA,IACJ,CAAC;AACD,yDAAmB;AAAA,EACvB;AACA,QAAM,cAAc,CAAC,UAAU;AAC3B,UAAM,EAAE,mBAAmB,eAAe,OAAO,WAAW,eAAe,eAAe,YAAY,SAAS,IAAI,MAAM,SAAS;AAClI,QAAI,CAAC,eAAe,CAAC,gBAAgB,WAAW,CAAC,mBAAmB;AAChE;AAAA,IACJ;AACA,UAAM,SAAS,EAAE,qBAAqB,MAAM,sBAAsB,MAAM,CAAC;AACzE,UAAM,WAAW,iBAAiB,OAAO,gBAAgB,OAAO;AAChE,UAAM,SAAS,kBAAkB,UAAU;AAC3C,UAAM,SAAS,kBAAkB,UAAU;AAC3C,UAAM,qBAAqB;AAAA,MACvB,GAAG;AAAA,MACH,GAAG,SAAS,IAAI,SAAS,SAAS,IAAI;AAAA,MACtC,GAAG,SAAS,IAAI,SAAS,SAAS,IAAI;AAAA,MACtC,OAAO,KAAK,IAAI,SAAS,IAAI,MAAM;AAAA,MACnC,QAAQ,KAAK,IAAI,SAAS,IAAI,MAAM;AAAA,IACxC;AACA,UAAM,QAAQ,SAAS;AACvB,UAAM,gBAAgB,eAAe,eAAe,oBAAoB,WAAW,kBAAkB,cAAc,SAAS,MAAM,UAAU;AAC5I,UAAM,kBAAkB,kBAAkB,eAAe,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,EAAE;AAC/E,UAAM,kBAAkB,cAAc,IAAI,CAAC,MAAM,EAAE,EAAE;AACrD,QAAI,uBAAuB,YAAY,gBAAgB,QAAQ;AAC3D,6BAAuB,UAAU,gBAAgB;AACjD,YAAM,UAAU,oBAAoB,OAAO,eAAe;AAC1D,UAAI,QAAQ,QAAQ;AAChB,uDAAgB;AAAA,MACpB;AAAA,IACJ;AACA,QAAI,uBAAuB,YAAY,gBAAgB,QAAQ;AAC3D,6BAAuB,UAAU,gBAAgB;AACjD,YAAM,UAAU,oBAAoB,OAAO,eAAe;AAC1D,UAAI,QAAQ,QAAQ;AAChB,uDAAgB;AAAA,MACpB;AAAA,IACJ;AACA,UAAM,SAAS;AAAA,MACX,mBAAmB;AAAA,IACvB,CAAC;AAAA,EACL;AACA,QAAM,YAAY,CAAC,UAAU;AACzB,QAAI,MAAM,WAAW,GAAG;AACpB;AAAA,IACJ;AACA,UAAM,EAAE,kBAAkB,IAAI,MAAM,SAAS;AAG7C,QAAI,CAAC,uBAAuB,qBAAqB,MAAM,WAAW,UAAU,SAAS;AACjF,yCAAU;AAAA,IACd;AACA,UAAM,SAAS,EAAE,sBAAsB,uBAAuB,UAAU,EAAE,CAAC;AAC3E,uBAAmB;AACnB,qDAAiB;AAAA,EACrB;AACA,QAAM,eAAe,CAAC,UAAU;AAC5B,QAAI,qBAAqB;AACrB,YAAM,SAAS,EAAE,sBAAsB,uBAAuB,UAAU,EAAE,CAAC;AAC3E,uDAAiB;AAAA,IACrB;AACA,uBAAmB;AAAA,EACvB;AACA,QAAM,qBAAqB,uBAAuB,eAAe;AACjE,SAAQ,cAAAA,QAAM;AAAA,IAAc;AAAA,IAAO,EAAE,WAAW,GAAG,CAAC,oBAAoB,EAAE,UAAU,WAAW,YAAY,CAAC,CAAC,GAAG,SAAS,qBAAqB,SAAY,YAAY,SAAS,SAAS,GAAG,eAAe,YAAY,eAAe,SAAS,GAAG,SAAS,YAAY,SAAS,SAAS,GAAG,cAAc,qBAAqB,SAAY,kBAAkB,aAAa,qBAAqB,cAAc,QAAW,aAAa,qBAAqB,cAAc,iBAAiB,WAAW,qBAAqB,YAAY,QAAW,cAAc,qBAAqB,eAAe,kBAAkB,KAAK,WAAW,OAAO,eAAe;AAAA,IACrnB;AAAA,IACA,cAAAA,QAAM,cAAc,eAAe,IAAI;AAAA,EAAC;AAChD,CAAC;AACD,KAAK,cAAc;AAEnB,SAAS,iBAAiB,MAAM,eAAe;AAC3C,QAAM,WAAW,KAAK,cAAc,KAAK;AACzC,MAAI,CAAC,UAAU;AACX,WAAO;AAAA,EACX;AACA,QAAM,aAAa,cAAc,IAAI,QAAQ;AAC7C,MAAI,CAAC,YAAY;AACb,WAAO;AAAA,EACX;AACA,MAAI,WAAW,UAAU;AACrB,WAAO;AAAA,EACX;AACA,SAAO,iBAAiB,YAAY,aAAa;AACrD;AACA,SAAS,YAAY,QAAQF,WAAU,SAAS;AAC5C,MAAI,UAAU;AACd,KAAG;AACC,QAAI,mCAAS,QAAQA;AACjB,aAAO;AACX,QAAI,YAAY,QAAQ;AACpB,aAAO;AACX,cAAU,QAAQ;AAAA,EACtB,SAAS;AACT,SAAO;AACX;AAEA,SAAS,aAAa,eAAe,gBAAgB,UAAU,QAAQ;AACnE,SAAO,MAAM,KAAK,cAAc,OAAO,CAAC,EACnC,OAAO,CAAC,OAAO,EAAE,YAAY,EAAE,OAAO,YACtC,CAAC,EAAE,cAAc,EAAE,YAAY,CAAC,iBAAiB,GAAG,aAAa,OACjE,EAAE,aAAc,kBAAkB,OAAO,EAAE,cAAc,YAAa,EACtE,IAAI,CAAC,MAAG;AAz7EjB;AAy7EqB;AAAA,MACb,IAAI,EAAE;AAAA,MACN,UAAU,EAAE,YAAY,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,MACrC,kBAAkB,EAAE,oBAAoB,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,MACrD,UAAU;AAAA,QACN,GAAG,SAAS,OAAK,OAAE,qBAAF,mBAAoB,MAAK;AAAA,QAC1C,GAAG,SAAS,OAAK,OAAE,qBAAF,mBAAoB,MAAK;AAAA,MAC9C;AAAA,MACA,OAAO;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,MACP;AAAA,MACA,QAAQ,EAAE;AAAA,MACV,YAAY,EAAE,cAAc,EAAE;AAAA,MAC9B,UAAU,EAAE,cAAc,EAAE;AAAA,MAC5B,OAAO,EAAE;AAAA,MACT,QAAQ,EAAE;AAAA,MACV,cAAc,EAAE;AAAA,IACpB;AAAA,GAAE;AACN;AACA,SAAS,gBAAgB,MAAM,QAAQ;AACnC,MAAI,CAAC,UAAU,WAAW,UAAU;AAChC,WAAO;AAAA,EACX;AACA,SAAO,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,KAAK,SAAS,IAAI,OAAO,CAAC,EAAE,CAAC,KAAK,KAAK,UAAU,EAAE,CAAC;AAC5F;AACA,SAAS,iBAAiB,MAAM,cAAc,eAAe,YAAY,aAAa,CAAC,GAAG,CAAC,GAAG,SAAS;AACnG,QAAM,oBAAoB,gBAAgB,MAAM,KAAK,UAAU,UAAU;AACzE,MAAI,gBAAgB;AACpB,QAAM,WAAW,KAAK,cAAc,KAAK;AACzC,MAAI,KAAK,WAAW,YAAY,CAAC,KAAK,cAAc;AAChD,QAAI,YAAY,KAAK,SAAS,KAAK,QAAQ;AACvC,YAAM,SAAS,cAAc,IAAI,QAAQ;AACzC,YAAM,EAAE,GAAG,SAAS,GAAG,QAAQ,IAAI,0BAA0B,QAAQ,UAAU,EAAE;AACjF,sBACI,UAAU,UAAU,OAAO,KAAK,UAAU,OAAO,KAAK,UAAU,OAAO,KAAK,KAAK,UAAU,OAAO,MAAM,IAClG;AAAA,QACE,CAAC,UAAU,KAAK,QAAQ,WAAW,CAAC,GAAG,UAAU,KAAK,SAAS,WAAW,CAAC,CAAC;AAAA,QAC5E;AAAA,UACI,UAAU,OAAO,QAAQ,KAAK,QAAQ,KAAK,QAAQ,WAAW,CAAC;AAAA,UAC/D,UAAU,OAAO,SAAS,KAAK,SAAS,KAAK,SAAS,WAAW,CAAC;AAAA,QACtE;AAAA,MACJ,IACE;AAAA,IACd,OACK;AACD,yCAAU,OAAO,cAAc,UAAU,EAAE;AAC3C,sBAAgB;AAAA,IACpB;AAAA,EACJ,WACS,KAAK,UAAU,YAAY,KAAK,WAAW,UAAU;AAC1D,UAAM,SAAS,cAAc,IAAI,QAAQ;AACzC,UAAM,EAAE,GAAG,SAAS,GAAG,QAAQ,IAAI,0BAA0B,QAAQ,UAAU,EAAE;AACjF,oBAAgB;AAAA,MACZ,CAAC,KAAK,OAAO,CAAC,EAAE,CAAC,IAAI,SAAS,KAAK,OAAO,CAAC,EAAE,CAAC,IAAI,OAAO;AAAA,MACzD,CAAC,KAAK,OAAO,CAAC,EAAE,CAAC,IAAI,SAAS,KAAK,OAAO,CAAC,EAAE,CAAC,IAAI,OAAO;AAAA,IAC7D;AAAA,EACJ;AACA,MAAI,iBAAiB,EAAE,GAAG,GAAG,GAAG,EAAE;AAClC,MAAI,UAAU;AACV,UAAM,aAAa,cAAc,IAAI,QAAQ;AAC7C,qBAAiB,0BAA0B,YAAY,UAAU,EAAE;AAAA,EACvE;AACA,QAAM,mBAAmB,iBAAiB,kBAAkB,WACtD,cAAc,cAAc,aAAa,IACzC;AACN,SAAO;AAAA,IACH,UAAU;AAAA,MACN,GAAG,iBAAiB,IAAI,eAAe;AAAA,MACvC,GAAG,iBAAiB,IAAI,eAAe;AAAA,IAC3C;AAAA,IACA;AAAA,EACJ;AACJ;AAIA,SAAS,sBAAsB,EAAE,QAAQ,WAAW,cAAe,GAAG;AAClE,QAAM,oBAAoB,UAAU,IAAI,CAAC,MAAM;AAC3C,UAAM,OAAO,cAAc,IAAI,EAAE,EAAE;AACnC,WAAO;AAAA,MACH,GAAG;AAAA,MACH,UAAU,EAAE;AAAA,MACZ,kBAAkB,EAAE;AAAA,IACxB;AAAA,EACJ,CAAC;AACD,SAAO,CAAC,SAAS,kBAAkB,KAAK,CAAC,MAAM,EAAE,OAAO,MAAM,IAAI,kBAAkB,CAAC,GAAG,iBAAiB;AAC7G;AAEA,IAAM,kBAAkB,CAACA,WAAU,aAAa,MAAM,eAAe;AACjE,QAAM,UAAU,YAAY,iBAAiBA,SAAQ;AACrD,MAAI,CAAC,WAAW,CAAC,QAAQ,QAAQ;AAC7B,WAAO;AAAA,EACX;AACA,QAAM,eAAe,MAAM,KAAK,OAAO;AACvC,QAAM,aAAa,YAAY,sBAAsB;AACrD,QAAM,aAAa;AAAA,IACf,GAAG,WAAW,QAAQ,WAAW,CAAC;AAAA,IAClC,GAAG,WAAW,SAAS,WAAW,CAAC;AAAA,EACvC;AACA,SAAO,aAAa,IAAI,CAAC,WAAW;AAChC,UAAM,eAAe,OAAO,sBAAsB;AAClD,WAAO;AAAA,MACH,IAAI,OAAO,aAAa,eAAe;AAAA,MACvC,UAAU,OAAO,aAAa,gBAAgB;AAAA,MAC9C,IAAI,aAAa,OAAO,WAAW,OAAO,WAAW,KAAK;AAAA,MAC1D,IAAI,aAAa,MAAM,WAAW,MAAM,WAAW,KAAK;AAAA,MACxD,GAAG,cAAc,MAAM;AAAA,IAC3B;AAAA,EACJ,CAAC;AACL;AACA,SAAS,gBAAgB,IAAI,UAAU,SAAS;AAC5C,SAAO,YAAY,SACb,UACA,CAAC,UAAU;AACT,UAAM,OAAO,SAAS,EAAE,cAAc,IAAI,EAAE;AAC5C,QAAI,MAAM;AACN,cAAQ,OAAO,EAAE,GAAG,KAAK,CAAC;AAAA,IAC9B;AAAA,EACJ;AACR;AAKA,SAAS,gBAAgB,EAAE,IAAI,OAAO,WAAW,OAAO,QAAS,GAAG;AAChE,QAAM,EAAE,kBAAkB,uBAAuB,sBAAsB,eAAe,QAAQ,IAAI,MAAM,SAAS;AACjH,QAAM,OAAO,cAAc,IAAI,EAAE;AACjC,MAAI,CAAC,MAAM;AACP,uCAAU,OAAO,cAAc,UAAU,EAAE,EAAE;AAC7C;AAAA,EACJ;AACA,QAAM,SAAS,EAAE,sBAAsB,MAAM,CAAC;AAC9C,MAAI,CAAC,KAAK,UAAU;AAChB,qBAAiB,CAAC,EAAE,CAAC;AAAA,EACzB,WACS,YAAa,KAAK,YAAY,sBAAuB;AAC1D,0BAAsB,EAAE,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,EAAE,CAAC;AAClD,0BAAsB,MAAG;AAnkFjC;AAmkFoC,sDAAS,YAAT,mBAAkB;AAAA,KAAM;AAAA,EACxD;AACJ;AAEA,SAAS,wBAAwB;AAC7B,QAAM,QAAQ,YAAY;AAE1B,QAAM,yBAAqB,2BAAY,CAAC,EAAE,YAAY,MAAM;AACxD,UAAM,EAAE,WAAW,UAAU,WAAW,IAAI,MAAM,SAAS;AAC3D,UAAM,IAAI,YAAY,UAAU,YAAY,QAAQ,CAAC,EAAE,UAAU,YAAY;AAC7E,UAAM,IAAI,YAAY,UAAU,YAAY,QAAQ,CAAC,EAAE,UAAU,YAAY;AAC7E,UAAM,aAAa;AAAA,MACf,IAAI,IAAI,UAAU,CAAC,KAAK,UAAU,CAAC;AAAA,MACnC,IAAI,IAAI,UAAU,CAAC,KAAK,UAAU,CAAC;AAAA,IACvC;AAEA,WAAO;AAAA,MACH,UAAU,aAAa,SAAS,CAAC,IAAI,KAAK,MAAM,WAAW,IAAI,SAAS,CAAC,CAAC,IAAI,WAAW;AAAA,MACzF,UAAU,aAAa,SAAS,CAAC,IAAI,KAAK,MAAM,WAAW,IAAI,SAAS,CAAC,CAAC,IAAI,WAAW;AAAA,MACzF,GAAG;AAAA,IACP;AAAA,EACJ,GAAG,CAAC,CAAC;AACL,SAAO;AACX;AAEA,SAAS,sBAAsB,eAAe;AAC1C,SAAO,CAAC,OAAO,GAAG,UAAU,+CAAgB,OAAO;AACvD;AACA,SAAS,QAAQ,EAAE,SAAS,WAAW,OAAO,iBAAiB,gBAAgB,QAAQ,cAAc,kBAAmB,GAAG;AACvH,QAAM,QAAQ,YAAY;AAC1B,QAAM,CAAC,UAAU,WAAW,QAAI,wBAAS,KAAK;AAC9C,QAAM,gBAAY,sBAAO,CAAC,CAAC;AAC3B,QAAM,cAAU,sBAAO,EAAE,GAAG,MAAM,GAAG,KAAK,CAAC;AAC3C,QAAM,gBAAY,sBAAO,CAAC;AAC1B,QAAM,sBAAkB,sBAAO,IAAI;AACnC,QAAM,oBAAgB,sBAAO,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC;AAC3C,QAAM,gBAAY,sBAAO,IAAI;AAC7B,QAAM,qBAAiB,sBAAO,KAAK;AACnC,QAAM,kBAAc,sBAAO,KAAK;AAChC,QAAM,gBAAY,sBAAO,KAAK;AAC9B,QAAM,qBAAqB,sBAAsB;AACjD,+BAAU,MAAM;AACZ,QAAI,mCAAS,SAAS;AAClB,YAAM,YAAY,eAAO,QAAQ,OAAO;AACxC,YAAM,cAAc,CAAC,EAAE,GAAG,EAAE,MAAM;AAC9B,cAAM,EAAE,eAAe,YAAY,iBAAiB,qBAAqB,YAAY,UAAU,YAAY,YAAY,QAAS,IAAI,MAAM,SAAS;AACnJ,gBAAQ,UAAU,EAAE,GAAG,EAAE;AACzB,YAAI,YAAY;AAChB,YAAI,WAAW,EAAE,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,EAAE;AAC1C,YAAI,UAAU,QAAQ,SAAS,KAAK,YAAY;AAC5C,gBAAM,OAAO,eAAe,UAAU,SAAS,UAAU;AACzD,qBAAW,UAAU,IAAI;AAAA,QAC7B;AACA,kBAAU,UAAU,UAAU,QAAQ,IAAI,CAAC,MAAM;AAC7C,gBAAM,eAAe,EAAE,GAAG,IAAI,EAAE,SAAS,GAAG,GAAG,IAAI,EAAE,SAAS,EAAE;AAChE,cAAI,YAAY;AACZ,yBAAa,IAAI,SAAS,CAAC,IAAI,KAAK,MAAM,aAAa,IAAI,SAAS,CAAC,CAAC;AACtE,yBAAa,IAAI,SAAS,CAAC,IAAI,KAAK,MAAM,aAAa,IAAI,SAAS,CAAC,CAAC;AAAA,UAC1E;AAGA,gBAAM,qBAAqB;AAAA,YACvB,CAAC,WAAW,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC;AAAA,YACnC,CAAC,WAAW,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC;AAAA,UACvC;AACA,cAAI,UAAU,QAAQ,SAAS,KAAK,cAAc,CAAC,EAAE,QAAQ;AACzD,+BAAmB,CAAC,EAAE,CAAC,IAAI,EAAE,iBAAiB,IAAI,SAAS,IAAI,WAAW,CAAC,EAAE,CAAC;AAC9E,+BAAmB,CAAC,EAAE,CAAC,IAAI,EAAE,iBAAiB,KAAK,EAAE,SAAS,KAAK,SAAS,KAAK,WAAW,CAAC,EAAE,CAAC;AAChG,+BAAmB,CAAC,EAAE,CAAC,IAAI,EAAE,iBAAiB,IAAI,SAAS,IAAI,WAAW,CAAC,EAAE,CAAC;AAC9E,+BAAmB,CAAC,EAAE,CAAC,IAAI,EAAE,iBAAiB,KAAK,EAAE,UAAU,KAAK,SAAS,KAAK,WAAW,CAAC,EAAE,CAAC;AAAA,UACrG;AACA,gBAAM,aAAa,iBAAiB,GAAG,cAAc,eAAe,oBAAoB,YAAY,OAAO;AAE3G,sBAAY,aAAa,EAAE,SAAS,MAAM,WAAW,SAAS,KAAK,EAAE,SAAS,MAAM,WAAW,SAAS;AACxG,YAAE,WAAW,WAAW;AACxB,YAAE,mBAAmB,WAAW;AAChC,iBAAO;AAAA,QACX,CAAC;AACD,YAAI,CAAC,WAAW;AACZ;AAAA,QACJ;AACA,4BAAoB,UAAU,SAAS,MAAM,IAAI;AACjD,oBAAY,IAAI;AAChB,cAAM,SAAS,SAAS,aAAa,sBAAsB,eAAe;AAC1E,YAAI,UAAU,UAAU,SAAS;AAC7B,gBAAM,CAAC,aAAa,KAAK,IAAI,sBAAsB;AAAA,YAC/C;AAAA,YACA,WAAW,UAAU;AAAA,YACrB;AAAA,UACJ,CAAC;AACD,iBAAO,UAAU,SAAS,aAAa,KAAK;AAAA,QAChD;AAAA,MACJ;AACA,YAAM,UAAU,MAAM;AAClB,YAAI,CAAC,gBAAgB,SAAS;AAC1B;AAAA,QACJ;AACA,cAAM,CAAC,WAAW,SAAS,IAAI,YAAY,cAAc,SAAS,gBAAgB,OAAO;AACzF,YAAI,cAAc,KAAK,cAAc,GAAG;AACpC,gBAAM,EAAE,WAAW,MAAM,IAAI,MAAM,SAAS;AAC5C,kBAAQ,QAAQ,KAAK,QAAQ,QAAQ,KAAK,KAAK,YAAY,UAAU,CAAC;AACtE,kBAAQ,QAAQ,KAAK,QAAQ,QAAQ,KAAK,KAAK,YAAY,UAAU,CAAC;AACtE,cAAI,MAAM,EAAE,GAAG,WAAW,GAAG,UAAU,CAAC,GAAG;AACvC,wBAAY,QAAQ,OAAO;AAAA,UAC/B;AAAA,QACJ;AACA,kBAAU,UAAU,sBAAsB,OAAO;AAAA,MACrD;AACA,YAAM,YAAY,CAAC,UAAU;AA/qFzC;AAgrFgB,cAAM,EAAE,eAAe,sBAAsB,gBAAgB,uBAAuB,iBAAiB,qBAAsB,IAAI,MAAM,SAAS;AAC9I,oBAAY,UAAU;AACtB,cAAM,UAAU,SAAS,kBAAkB,sBAAsB,oBAAoB;AACrF,aAAK,CAAC,qBAAqB,CAAC,iBAAiB,CAAC,wBAAwB,QAAQ;AAC1E,cAAI,GAAC,mBAAc,IAAI,MAAM,MAAxB,mBAA2B,WAAU;AAEtC,kCAAsB;AAAA,UAC1B;AAAA,QACJ;AACA,YAAI,UAAU,gBAAgB,mBAAmB;AAC7C,0BAAgB;AAAA,YACZ,IAAI;AAAA,YACJ;AAAA,YACA;AAAA,UACJ,CAAC;AAAA,QACL;AACA,cAAM,aAAa,mBAAmB,KAAK;AAC3C,gBAAQ,UAAU;AAClB,kBAAU,UAAU,aAAa,eAAe,gBAAgB,YAAY,MAAM;AAClF,YAAI,WAAW,UAAU,SAAS;AAC9B,gBAAM,CAAC,aAAa,KAAK,IAAI,sBAAsB;AAAA,YAC/C;AAAA,YACA,WAAW,UAAU;AAAA,YACrB;AAAA,UACJ,CAAC;AACD,kBAAQ,MAAM,aAAa,aAAa,KAAK;AAAA,QACjD;AAAA,MACJ;AACA,UAAI,UAAU;AACV,kBAAU,GAAG,SAAS,IAAI;AAAA,MAC9B,OACK;AACD,cAAM,cAAc,aAAK,EACpB,GAAG,SAAS,CAAC,UAAU;AACxB,gBAAM,EAAE,SAAS,kBAAkB,IAAI,MAAM,SAAS;AACtD,cAAI,sBAAsB,GAAG;AACzB,sBAAU,KAAK;AAAA,UACnB;AACA,oBAAU,UAAU;AACpB,gBAAM,aAAa,mBAAmB,KAAK;AAC3C,kBAAQ,UAAU;AAClB,0BAAgB,WAAU,mCAAS,4BAA2B;AAC9D,wBAAc,UAAU,iBAAiB,MAAM,aAAa,gBAAgB,OAAO;AAAA,QACvF,CAAC,EACI,GAAG,QAAQ,CAAC,UAAU;AA5tF3C;AA6tFoB,gBAAM,aAAa,mBAAmB,KAAK;AAC3C,gBAAM,EAAE,mBAAmB,kBAAkB,IAAI,MAAM,SAAS;AAChE,cAAI,MAAM,YAAY,SAAS,eAAe,MAAM,YAAY,QAAQ,SAAS,GAAG;AAChF,sBAAU,UAAU;AAAA,UACxB;AACA,cAAI,UAAU,SAAS;AACnB;AAAA,UACJ;AACA,cAAI,CAAC,eAAe,WAAW,YAAY,WAAW,mBAAmB;AACrE,2BAAe,UAAU;AACzB,oBAAQ;AAAA,UACZ;AACA,cAAI,CAAC,YAAY,SAAS;AACtB,kBAAM,IAAI,WAAW,cAAY,wCAAS,YAAT,mBAAkB,MAAK;AACxD,kBAAM,IAAI,WAAW,cAAY,wCAAS,YAAT,mBAAkB,MAAK;AACxD,kBAAMY,YAAW,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC;AACxC,gBAAIA,YAAW,mBAAmB;AAC9B,wBAAU,KAAK;AAAA,YACnB;AAAA,UACJ;AAEA,eAAK,QAAQ,QAAQ,MAAM,WAAW,YAAY,QAAQ,QAAQ,MAAM,WAAW,aAC/E,UAAU,WACV,YAAY,SAAS;AACrB,sBAAU,UAAU,MAAM;AAC1B,0BAAc,UAAU,iBAAiB,MAAM,aAAa,gBAAgB,OAAO;AACnF,wBAAY,UAAU;AAAA,UAC1B;AAAA,QACJ,CAAC,EACI,GAAG,OAAO,CAAC,UAAU;AACtB,cAAI,CAAC,YAAY,WAAW,UAAU,SAAS;AAC3C;AAAA,UACJ;AACA,sBAAY,KAAK;AACjB,yBAAe,UAAU;AACzB,sBAAY,UAAU;AACtB,+BAAqB,UAAU,OAAO;AACtC,cAAI,UAAU,SAAS;AACnB,kBAAM,EAAE,qBAAqB,eAAe,gBAAgB,oBAAoB,IAAI,MAAM,SAAS;AACnG,kBAAM,SAAS,SAAS,iBAAiB,sBAAsB,mBAAmB;AAClF,gCAAoB,UAAU,SAAS,OAAO,KAAK;AACnD,gBAAI,QAAQ;AACR,oBAAM,CAAC,aAAa,KAAK,IAAI,sBAAsB;AAAA,gBAC/C;AAAA,gBACA,WAAW,UAAU;AAAA,gBACrB;AAAA,cACJ,CAAC;AACD,qBAAO,MAAM,aAAa,aAAa,KAAK;AAAA,YAChD;AAAA,UACJ;AAAA,QACJ,CAAC,EACI,OAAO,CAAC,UAAU;AACnB,gBAAM,SAAS,MAAM;AACrB,gBAAM,cAAc,CAAC,MAAM,WACtB,CAAC,mBAAmB,CAAC,YAAY,QAAQ,IAAI,eAAe,IAAI,OAAO,OACvE,CAAC,kBAAkB,YAAY,QAAQ,gBAAgB,OAAO;AACnE,iBAAO;AAAA,QACX,CAAC;AACD,kBAAU,KAAK,WAAW;AAC1B,eAAO,MAAM;AACT,oBAAU,GAAG,SAAS,IAAI;AAAA,QAC9B;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ,GAAG;AAAA,IACC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;AACD,SAAO;AACX;AAEA,SAAS,yBAAyB;AAC9B,QAAM,QAAQ,YAAY;AAC1B,QAAM,sBAAkB,2BAAY,CAAC,WAAW;AAC5C,UAAM,EAAE,eAAe,YAAY,qBAAqB,UAAU,YAAY,UAAU,SAAS,eAAe,IAAI,MAAM,SAAS;AACnI,UAAM,gBAAgB,SAAS,EAAE,OAAO,CAAC,MAAM,EAAE,aAAa,EAAE,aAAc,kBAAkB,OAAO,EAAE,cAAc,YAAa;AAGpI,UAAM,QAAQ,aAAa,SAAS,CAAC,IAAI;AACzC,UAAM,QAAQ,aAAa,SAAS,CAAC,IAAI;AACzC,UAAM,SAAS,OAAO,iBAAiB,IAAI;AAC3C,UAAM,gBAAgB,OAAO,IAAI,QAAQ;AACzC,UAAM,gBAAgB,OAAO,IAAI,QAAQ;AACzC,UAAM,cAAc,cAAc,IAAI,CAAC,MAAM;AACzC,UAAI,EAAE,kBAAkB;AACpB,cAAM,eAAe,EAAE,GAAG,EAAE,iBAAiB,IAAI,eAAe,GAAG,EAAE,iBAAiB,IAAI,cAAc;AACxG,YAAI,YAAY;AACZ,uBAAa,IAAI,SAAS,CAAC,IAAI,KAAK,MAAM,aAAa,IAAI,SAAS,CAAC,CAAC;AACtE,uBAAa,IAAI,SAAS,CAAC,IAAI,KAAK,MAAM,aAAa,IAAI,SAAS,CAAC,CAAC;AAAA,QAC1E;AACA,cAAM,EAAE,kBAAkB,SAAS,IAAI,iBAAiB,GAAG,cAAc,eAAe,YAAY,QAAW,OAAO;AACtH,UAAE,WAAW;AACb,UAAE,mBAAmB;AAAA,MACzB;AACA,aAAO;AAAA,IACX,CAAC;AACD,wBAAoB,aAAa,MAAM,KAAK;AAAA,EAChD,GAAG,CAAC,CAAC;AACL,SAAO;AACX;AAEA,IAAM,gBAAgB;AAAA,EAClB,SAAS,EAAE,GAAG,GAAG,GAAG,GAAG;AAAA,EACvB,WAAW,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,EACxB,WAAW,EAAE,GAAG,IAAI,GAAG,EAAE;AAAA,EACzB,YAAY,EAAE,GAAG,GAAG,GAAG,EAAE;AAC7B;AACA,IAAI,WAAW,CAAC,kBAAkB;AAC9B,QAAM,cAAc,CAAC,EAAE,IAAI,MAAM,MAAM,MAAM,MAAM,YAAY,YAAY,UAAU,SAAS,cAAc,aAAa,cAAc,eAAe,eAAe,OAAAX,QAAO,WAAW,aAAa,cAAc,eAAe,aAAa,mBAAmB,gBAAgB,gBAAgB,QAAQ,gBAAgB,YAAY,QAAQ,UAAU,iBAAiB,gBAAgB,aAAa,qBAAqB,WAAW,MAAM,gBAAiB,MAAM;AAC9b,UAAM,QAAQ,YAAY;AAC1B,UAAM,cAAU,sBAAO,IAAI;AAC3B,UAAM,kBAAc,sBAAO,IAAI;AAC/B,UAAM,yBAAqB,sBAAO,cAAc;AAChD,UAAM,yBAAqB,sBAAO,cAAc;AAChD,UAAM,eAAW,sBAAO,IAAI;AAC5B,UAAM,mBAAmB,gBAAgB,eAAe,WAAW,gBAAgB,eAAe;AAClG,UAAM,kBAAkB,uBAAuB;AAC/C,UAAM,sBAAsB,gBAAgB,IAAI,MAAM,UAAU,YAAY;AAC5E,UAAM,qBAAqB,gBAAgB,IAAI,MAAM,UAAU,WAAW;AAC1E,UAAM,sBAAsB,gBAAgB,IAAI,MAAM,UAAU,YAAY;AAC5E,UAAM,uBAAuB,gBAAgB,IAAI,MAAM,UAAU,aAAa;AAC9E,UAAM,uBAAuB,gBAAgB,IAAI,MAAM,UAAU,aAAa;AAC9E,UAAM,sBAAsB,CAAC,UAAU;AACnC,YAAM,EAAE,kBAAkB,IAAI,MAAM,SAAS;AAC7C,UAAI,iBAAiB,CAAC,qBAAqB,CAAC,eAAe,oBAAoB,IAAI;AAE/E,wBAAgB;AAAA,UACZ;AAAA,UACA;AAAA,UACA;AAAA,QACJ,CAAC;AAAA,MACL;AACA,UAAI,SAAS;AACT,cAAM,OAAO,MAAM,SAAS,EAAE,cAAc,IAAI,EAAE;AAClD,YAAI,MAAM;AACN,kBAAQ,OAAO,EAAE,GAAG,KAAK,CAAC;AAAA,QAC9B;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,YAAY,CAAC,UAAU;AACzB,UAAI,eAAe,KAAK,GAAG;AACvB;AAAA,MACJ;AACA,UAAI,qBAAqB;AACrB;AAAA,MACJ;AACA,UAAI,qBAAqB,SAAS,MAAM,GAAG,KAAK,cAAc;AAC1D,cAAM,WAAW,MAAM,QAAQ;AAC/B,wBAAgB;AAAA,UACZ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACJ,CAAC;AAAA,MACL,WACS,eAAe,YAAY,OAAO,UAAU,eAAe,KAAK,eAAe,MAAM,GAAG,GAAG;AAChG,cAAM,SAAS;AAAA,UACX,iBAAiB,uBAAuB,MAAM,IACzC,QAAQ,SAAS,EAAE,EACnB,YAAY,CAAC,sBAAsB,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,IAAI;AAAA,QAChE,CAAC;AACD,wBAAgB;AAAA,UACZ,GAAG,cAAc,MAAM,GAAG,EAAE;AAAA,UAC5B,GAAG,cAAc,MAAM,GAAG,EAAE;AAAA,UAC5B,gBAAgB,MAAM;AAAA,QAC1B,CAAC;AAAA,MACL;AAAA,IACJ;AACA,iCAAU,MAAM;AACZ,aAAO,MAAM;AACT,YAAI,YAAY,SAAS;AACrB,2DAAgB,UAAU,YAAY;AACtC,sBAAY,UAAU;AAAA,QAC1B;AAAA,MACJ;AAAA,IACJ,GAAG,CAAC,CAAC;AACL,iCAAU,MAAM;AACZ,UAAI,QAAQ,WAAW,CAAC,QAAQ;AAC5B,cAAM,WAAW,QAAQ;AACzB,YAAI,CAAC,eAAe,CAAC,mBAAmB,YAAY,YAAY,UAAU;AAGtE,cAAI,YAAY,SAAS;AACrB,6DAAgB,UAAU,YAAY;AAAA,UAC1C;AACA,2DAAgB,QAAQ;AACxB,sBAAY,UAAU;AAAA,QAC1B;AAAA,MACJ;AAAA,IACJ,GAAG,CAAC,QAAQ,aAAa,eAAe,CAAC;AACzC,iCAAU,MAAM;AAEZ,YAAM,cAAc,SAAS,YAAY;AACzC,YAAM,mBAAmB,mBAAmB,YAAY;AACxD,YAAM,mBAAmB,mBAAmB,YAAY;AACxD,UAAI,QAAQ,YAAY,eAAe,oBAAoB,mBAAmB;AAC1E,YAAI,aAAa;AACb,mBAAS,UAAU;AAAA,QACvB;AACA,YAAI,kBAAkB;AAClB,6BAAmB,UAAU;AAAA,QACjC;AACA,YAAI,kBAAkB;AAClB,6BAAmB,UAAU;AAAA,QACjC;AACA,cAAM,SAAS,EAAE,qBAAqB,CAAC,EAAE,IAAI,aAAa,QAAQ,SAAS,aAAa,KAAK,CAAC,CAAC;AAAA,MACnG;AAAA,IACJ,GAAG,CAAC,IAAI,MAAM,gBAAgB,cAAc,CAAC;AAC7C,UAAM,WAAW,QAAQ;AAAA,MACrB;AAAA,MACA,UAAU,UAAU,CAAC;AAAA,MACrB;AAAA,MACA,gBAAgB;AAAA,MAChB,QAAQ;AAAA,MACR;AAAA,MACA;AAAA,IACJ,CAAC;AACD,QAAI,QAAQ;AACR,aAAO;AAAA,IACX;AACA,WAAQ,cAAAC,QAAM;AAAA,MAAc;AAAA,MAAO,EAAE,WAAW,GAAG;AAAA,QAC3C;AAAA,QACA,oBAAoB,IAAI;AAAA,QACxB;AAAA;AAAA,UAEI,CAAC,cAAc,GAAG;AAAA,QACtB;AAAA,QACA;AAAA,QACA;AAAA,UACI;AAAA,UACA,YAAY;AAAA,UACZ,QAAQ;AAAA,UACR;AAAA,QACJ;AAAA,MACJ,CAAC,GAAG,KAAK,SAAS,OAAO;AAAA,QACrB;AAAA,QACA,WAAW,aAAa,UAAU,MAAM,UAAU;AAAA,QAClD,eAAe,mBAAmB,QAAQ;AAAA,QAC1C,YAAY,cAAc,YAAY;AAAA,QACtC,GAAGD;AAAA,MACP,GAAG,WAAW,IAAI,eAAe,YAAY,EAAE,IAAI,cAAc,qBAAqB,aAAa,oBAAoB,cAAc,qBAAqB,eAAe,sBAAsB,SAAS,qBAAqB,eAAe,sBAAsB,WAAW,cAAc,YAAY,QAAW,UAAU,cAAc,IAAI,QAAW,MAAM,cAAc,WAAW,QAAW,oBAAoB,sBAAsB,SAAY,GAAG,kBAAkB,IAAI,IAAI,IAAI,cAAc,UAAU;AAAA,MAClf,cAAAC,QAAM;AAAA,QAAc;AAAA,QAAU,EAAE,OAAO,GAAG;AAAA,QACtC,cAAAA,QAAM,cAAc,eAAe,EAAE,IAAQ,MAAY,MAAY,MAAY,MAAY,UAAoB,eAA8B,gBAAgC,gBAAgC,UAAoB,YAAwB,OAAe,CAAC;AAAA,MAAC;AAAA,IAAC;AAAA,EACzR;AACA,cAAY,cAAc;AAC1B,aAAO,oBAAK,WAAW;AAC3B;AAMA,IAAM,aAAa,CAAC,MAAM;AACtB,QAAM,gBAAgB,EAAE,SAAS,EAAE,OAAO,CAAC,MAAM,EAAE,QAAQ;AAC3D,SAAO;AAAA,IACH,GAAG,eAAe,eAAe,EAAE,UAAU;AAAA,IAC7C,iBAAiB,aAAa,EAAE,UAAU,CAAC,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;AAAA,IAC3F,qBAAqB,EAAE;AAAA,EAC3B;AACJ;AACA,SAAS,eAAe,EAAE,wBAAwB,gBAAgB,oBAAoB,GAAG;AACrF,QAAM,QAAQ,YAAY;AAC1B,QAAM,EAAE,OAAO,QAAQ,GAAG,MAAM,GAAG,KAAK,iBAAiB,oBAAoB,IAAI,SAAS,YAAY,SAAO;AAC7G,QAAM,kBAAkB,uBAAuB;AAC/C,QAAM,cAAU,sBAAO,IAAI;AAC3B,+BAAU,MAAM;AA7+FpB;AA8+FQ,QAAI,CAAC,qBAAqB;AACtB,oBAAQ,YAAR,mBAAiB,MAAM;AAAA,QACnB,eAAe;AAAA,MACnB;AAAA,IACJ;AAAA,EACJ,GAAG,CAAC,mBAAmB,CAAC;AACxB,UAAQ;AAAA,IACJ;AAAA,EACJ,CAAC;AACD,MAAI,uBAAuB,CAAC,SAAS,CAAC,QAAQ;AAC1C,WAAO;AAAA,EACX;AACA,QAAM,gBAAgB,yBAChB,CAAC,UAAU;AACT,UAAM,gBAAgB,MACjB,SAAS,EACT,SAAS,EACT,OAAO,CAAC,MAAM,EAAE,QAAQ;AAC7B,2BAAuB,OAAO,aAAa;AAAA,EAC/C,IACE;AACN,QAAM,YAAY,CAAC,UAAU;AACzB,QAAI,OAAO,UAAU,eAAe,KAAK,eAAe,MAAM,GAAG,GAAG;AAChE,sBAAgB;AAAA,QACZ,GAAG,cAAc,MAAM,GAAG,EAAE;AAAA,QAC5B,GAAG,cAAc,MAAM,GAAG,EAAE;AAAA,QAC5B,gBAAgB,MAAM;AAAA,MAC1B,CAAC;AAAA,IACL;AAAA,EACJ;AACA,SAAQ,cAAAA,QAAM;AAAA,IAAc;AAAA,IAAO,EAAE,WAAW,GAAG,CAAC,8BAA8B,yBAAyB,cAAc,CAAC,GAAG,OAAO;AAAA,MAC5H,WAAW;AAAA,IACf,EAAE;AAAA,IACF,cAAAA,QAAM,cAAc,OAAO,EAAE,KAAK,SAAS,WAAW,mCAAmC,eAA8B,UAAU,sBAAsB,SAAY,IAAI,WAAW,sBAAsB,SAAY,WAAW,OAAO;AAAA,MAC9N;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ,EAAE,CAAC;AAAA,EAAC;AAChB;AACA,IAAI,uBAAmB,oBAAK,cAAc;AAE1C,IAAM,aAAa,CAAC,MAAM,EAAE;AAC5B,IAAM,eAAe,CAAC,EAAE,UAAU,aAAa,kBAAkB,iBAAiB,kBAAkB,mBAAmB,cAAc,eAAe,QAAQ,aAAa,WAAW,kBAAkB,iBAAiB,eAAe,kBAAkB,gBAAgB,uBAAuB,sBAAsB,uBAAuB,oBAAoB,cAAc,aAAa,aAAa,cAAc,kBAAkB,iBAAiB,mBAAmB,WAAW,YAAY,iBAAiB,iBAAiB,SAAS,SAAS,kBAAkB,wBAAwB,kBAAkB,gBAAgB,oBAAqB,MAAM;AAC1nB,QAAM,uBAAuB,SAAS,UAAU;AAChD,QAAM,sBAAsB,YAAY,gBAAgB;AACxD,QAAM,0BAA0B,YAAY,oBAAoB;AAChE,QAAM,YAAY,2BAA2B;AAC7C,QAAM,cAAc,2BAA2B;AAC/C,QAAM,cAAc,uBAAwB,mBAAmB,cAAc;AAC7E,sBAAoB,EAAE,eAAe,sBAAsB,CAAC;AAC5D,SAAQ,cAAAA,QAAM;AAAA,IAAc;AAAA,IAAU,EAAE,QAAgB,aAA0B,WAAsB,mBAAsC,oBAAwC,cAA4B,aAA0B,aAA0B,kBAAoC,iBAAkC,mBAAsC,WAAW,CAAC,uBAAuB,WAAW,iBAAkC,iBAAkC,SAAkB,SAAkB,uBAA8C,kBAAoC,kBAAoC,eAA+B;AAAA,IACzpB,cAAAA,QAAM;AAAA,MAAc;AAAA,MAAM,EAAE,kBAAoC,gBAAgC,aAA0B,kBAAoC,iBAAkC,kBAAoC,mBAAsC,cAA4B,WAAsB,aAAa,CAAC,CAAC,aAAa,cAA6B;AAAA,MACjX;AAAA,MACA,wBAAyB,cAAAA,QAAM,cAAc,kBAAkB,EAAE,wBAAgD,gBAAgC,oBAAyC,CAAC;AAAA,IAAE;AAAA,EAAC;AAC1M;AACA,aAAa,cAAc;AAC3B,IAAI,qBAAiB,oBAAK,YAAY;AAEtC,SAAS,gBAAgB,mBAAmB;AACxC,QAAM,QAAQ,aAAS,2BAAY,CAAC,MAAM,oBACpC,eAAe,EAAE,eAAe,EAAE,GAAG,GAAG,GAAG,GAAG,OAAO,EAAE,OAAO,QAAQ,EAAE,OAAO,GAAG,EAAE,WAAW,IAAI,IACnG,EAAE,SAAS,GAAG,CAAC,iBAAiB,CAAC,CAAC;AACxC,SAAO;AACX;AAEA,SAAS,gBAAgB,WAAW;AAChC,QAAM,gBAAgB;AAAA,IAClB,OAAO,SAAU,UAAU,SAAS,WAAY;AAAA,IAChD,SAAS,SAAU,UAAU,WAAW,aAAc;AAAA,IACtD,QAAQ,SAAU,UAAU,UAAU,YAAa;AAAA,IACnD,OAAO,SAAU,UAAU,SAAS,SAAU;AAAA,EAClD;AACA,QAAM,eAAe,CAAC;AACtB,QAAM,eAAe,OAAO,KAAK,SAAS,EACrC,OAAO,CAAC,MAAM,CAAC,CAAC,SAAS,WAAW,UAAU,OAAO,EAAE,SAAS,CAAC,CAAC,EAClE,OAAO,CAAC,KAAK,QAAQ;AACtB,QAAI,GAAG,IAAI,SAAU,UAAU,GAAG,KAAK,aAAc;AACrD,WAAO;AAAA,EACX,GAAG,YAAY;AACf,SAAO;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,EACP;AACJ;AACA,IAAM,wBAAwB,CAAC,EAAE,GAAG,GAAG,OAAO,QAAQ,OAAQ,MAAM;AAChE,MAAI,CAAC,SAAS,CAAC,QAAQ;AACnB,WAAO,EAAE,GAAG,EAAE;AAAA,EAClB;AACA,MAAI,OAAO,CAAC,IAAI,KAAK,OAAO,CAAC,IAAI,KAAK,OAAO,CAAC,IAAI,KAAK,OAAO,CAAC,IAAI,GAAG;AAClE,WAAO,EAAE,GAAG,EAAE;AAAA,EAClB;AACA,SAAO;AAAA,IACH,GAAG,IAAI,QAAQ,OAAO,CAAC;AAAA,IACvB,GAAG,IAAI,SAAS,OAAO,CAAC;AAAA,EAC5B;AACJ;AAEA,IAAM,aAAa,CAAC,OAAO;AAAA,EACvB,gBAAgB,EAAE;AAAA,EAClB,kBAAkB,EAAE;AAAA,EACpB,gBAAgB,EAAE;AAAA,EAClB,oBAAoB,EAAE;AAAA,EACtB,sBAAsB,EAAE;AAAA,EACxB,SAAS,EAAE;AACf;AACA,IAAM,eAAe,CAAC,UAAU;AAC5B,QAAM,EAAE,gBAAgB,kBAAkB,gBAAgB,oBAAoB,sBAAsB,QAAQ,IAAI,SAAS,YAAY,SAAO;AAC5I,QAAM,QAAQ,gBAAgB,MAAM,yBAAyB;AAC7D,QAAM,wBAAoB,sBAAO;AACjC,QAAM,qBAAiB,uBAAQ,MAAM;AACjC,QAAI,OAAO,mBAAmB,aAAa;AACvC,aAAO;AAAA,IACX;AACA,UAAM,WAAW,IAAI,eAAe,CAAC,YAAY;AAC7C,YAAM,UAAU,QAAQ,IAAI,CAAC,WAAW;AAAA,QACpC,IAAI,MAAM,OAAO,aAAa,SAAS;AAAA,QACvC,aAAa,MAAM;AAAA,QACnB,aAAa;AAAA,MACjB,EAAE;AACF,2BAAqB,OAAO;AAAA,IAChC,CAAC;AACD,sBAAkB,UAAU;AAC5B,WAAO;AAAA,EACX,GAAG,CAAC,CAAC;AACL,+BAAU,MAAM;AACZ,WAAO,MAAM;AA5mGrB;AA6mGY,mEAAmB,YAAnB,mBAA4B;AAAA,IAChC;AAAA,EACJ,GAAG,CAAC,CAAC;AACL,SAAQ,cAAAA,QAAM,cAAc,OAAO,EAAE,WAAW,qBAAqB,OAAO,eAAe,GAAG,MAAM,IAAI,CAAC,SAAS;AAhnGtH;AAinGQ,QAAI,WAAW,KAAK,QAAQ;AAC5B,QAAI,CAAC,MAAM,UAAU,QAAQ,GAAG;AAC5B,yCAAU,OAAO,cAAc,UAAU,EAAE,QAAQ;AACnD,iBAAW;AAAA,IACf;AACA,UAAM,gBAAiB,MAAM,UAAU,QAAQ,KAAK,MAAM,UAAU;AACpE,UAAM,cAAc,CAAC,EAAE,KAAK,aAAc,kBAAkB,OAAO,KAAK,cAAc;AACtF,UAAM,eAAe,CAAC,EAAE,KAAK,cAAe,sBAAsB,OAAO,KAAK,eAAe;AAC7F,UAAM,gBAAgB,CAAC,EAAE,KAAK,eAAgB,oBAAoB,OAAO,KAAK,gBAAgB;AAC9F,UAAM,cAAc,CAAC,EAAE,KAAK,aAAc,kBAAkB,OAAO,KAAK,cAAc;AACtF,UAAM,kBAAkB,MAAM,aACxB,cAAc,KAAK,kBAAkB,MAAM,UAAU,IACrD,KAAK;AACX,UAAM,QAAO,mDAAiB,MAAK;AACnC,UAAM,QAAO,mDAAiB,MAAK;AACnC,UAAM,YAAY,sBAAsB;AAAA,MACpC,GAAG;AAAA,MACH,GAAG;AAAA,MACH,OAAO,KAAK,SAAS;AAAA,MACrB,QAAQ,KAAK,UAAU;AAAA,MACvB,QAAQ,MAAM;AAAA,IAClB,CAAC;AACD,WAAQ,cAAAA,QAAM,cAAc,eAAe,EAAE,KAAK,KAAK,IAAI,IAAI,KAAK,IAAI,WAAW,KAAK,WAAW,OAAO,KAAK,OAAO,MAAM,UAAU,MAAM,KAAK,MAAM,gBAAgB,KAAK,kBAAkB,SAAS,QAAQ,gBAAgB,KAAK,kBAAkB,SAAS,KAAK,QAAQ,KAAK,QAAQ,MAAM,MAAM,MAAM,MAAM,YAAY,UAAU,GAAG,YAAY,UAAU,GAAG,mBAAmB,MAAM,mBAAmB,SAAS,MAAM,aAAa,cAAc,MAAM,kBAAkB,aAAa,MAAM,iBAAiB,cAAc,MAAM,kBAAkB,eAAe,MAAM,mBAAmB,eAAe,MAAM,mBAAmB,UAAU,CAAC,CAAC,KAAK,UAAU,aAA0B,cAA4B,eAA8B,aAA0B,gBAAgC,YAAY,KAAK,YAAY,UAAQ,UAAK,eAAe,MAApB,mBAAuB,MAAK,GAAG,UAAU,CAAC,GAAC,UAAK,eAAe,MAApB,mBAAuB,WAAU,iBAAiB,MAAM,iBAAiB,gBAAgB,MAAM,gBAAgB,aAAa,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,KAAK,QAAQ,MAAM,MAAM,MAAM,qBAAqB,MAAM,qBAAqB,WAAW,KAAK,WAAW,iBAAiB,CAAC,GAAC,UAAK,eAAe,MAApB,mBAAuB,cAAa,CAAC;AAAA,EACxpC,CAAC,CAAC;AACN;AACA,aAAa,cAAc;AAC3B,IAAI,qBAAiB,oBAAK,YAAY;AAEtC,IAAM,SAAS,CAAC,GAAG,OAAO,aAAa;AACnC,MAAI,aAAa,SAAS;AACtB,WAAO,IAAI;AACf,MAAI,aAAa,SAAS;AACtB,WAAO,IAAI;AACf,SAAO;AACX;AACA,IAAM,SAAS,CAAC,GAAG,OAAO,aAAa;AACnC,MAAI,aAAa,SAAS;AACtB,WAAO,IAAI;AACf,MAAI,aAAa,SAAS;AACtB,WAAO,IAAI;AACf,SAAO;AACX;AACA,IAAM,uBAAuB;AAC7B,IAAM,aAAa,CAAC,EAAE,UAAU,SAAS,SAAS,SAAS,IAAI,aAAa,cAAc,YAAY,KAAM,MAAO,cAAAA,QAAM,cAAc,UAAU,EAAE,aAA0B,cAA4B,YAAwB,WAAW,GAAG,CAAC,sBAAsB,GAAG,oBAAoB,IAAI,IAAI,EAAE,CAAC,GAAG,IAAI,OAAO,SAAS,QAAQ,QAAQ,GAAG,IAAI,OAAO,SAAS,QAAQ,QAAQ,GAAG,GAAG,QAAQ,QAAQ,eAAe,MAAM,cAAc,CAAC;AAEhb,IAAM,wBAAwB,MAAM;AACpC,IAAI,WAAW,CAAC,kBAAkB;AAC9B,QAAM,cAAc,CAAC,EAAE,IAAI,WAAW,MAAM,MAAM,SAAS,mBAAmB,UAAU,UAAU,OAAO,YAAY,aAAa,cAAc,gBAAgB,qBAAqB,OAAAD,QAAO,QAAQ,QAAQ,SAAS,SAAS,SAAS,SAAS,gBAAgB,gBAAgB,oBAAoB,QAAQ,gBAAgB,gBAAgB,eAAe,cAAc,aAAa,cAAc,iBAAiB,aAAa,kBAAkB,gBAAgB,WAAW,aAAa,MAAM,WAAW,aAAa,iBAAiB,aAAa,kBAAkB,oBAAqB,MAAM;AACjkB,UAAM,cAAU,sBAAO,IAAI;AAC3B,UAAM,CAAC,aAAa,cAAc,QAAI,wBAAS,KAAK;AACpD,UAAM,CAAC,UAAU,WAAW,QAAI,wBAAS,KAAK;AAC9C,UAAM,QAAQ,YAAY;AAC1B,UAAM,qBAAiB,uBAAQ,MAAM,SAAS,YAAY,aAAa,IAAI,CAAC,MAAM,CAAC,aAAa,IAAI,CAAC;AACrG,UAAM,mBAAe,uBAAQ,MAAM,SAAS,YAAY,WAAW,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,CAAC;AAC/F,QAAI,QAAQ;AACR,aAAO;AAAA,IACX;AACA,UAAM,cAAc,CAAC,UAAU;AA1qGvC;AA2qGY,YAAM,EAAE,OAAO,kBAAkB,uBAAuB,qBAAqB,IAAI,MAAM,SAAS;AAChG,YAAM,OAAO,MAAM,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE;AAC1C,UAAI,CAAC,MAAM;AACP;AAAA,MACJ;AACA,UAAI,oBAAoB;AACpB,cAAM,SAAS,EAAE,sBAAsB,MAAM,CAAC;AAC9C,YAAI,KAAK,YAAY,sBAAsB;AACvC,gCAAsB,EAAE,OAAO,CAAC,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;AAClD,wBAAQ,YAAR,mBAAiB;AAAA,QACrB,OACK;AACD,2BAAiB,CAAC,EAAE,CAAC;AAAA,QACzB;AAAA,MACJ;AACA,UAAI,SAAS;AACT,gBAAQ,OAAO,IAAI;AAAA,MACvB;AAAA,IACJ;AACA,UAAM,2BAA2B,kBAAkB,IAAI,MAAM,UAAU,iBAAiB;AACxF,UAAM,oBAAoB,kBAAkB,IAAI,MAAM,UAAU,aAAa;AAC7E,UAAM,mBAAmB,kBAAkB,IAAI,MAAM,UAAU,YAAY;AAC3E,UAAM,kBAAkB,kBAAkB,IAAI,MAAM,UAAU,WAAW;AACzE,UAAM,mBAAmB,kBAAkB,IAAI,MAAM,UAAU,YAAY;AAC3E,UAAM,oBAAoB,CAAC,OAAO,mBAAmB;AAEjD,UAAI,MAAM,WAAW,GAAG;AACpB;AAAA,MACJ;AACA,YAAM,EAAE,OAAO,mBAAmB,uBAAuB,IAAI,MAAM,SAAS;AAC5E,YAAM,SAAS,iBAAiB,SAAS;AACzC,YAAM,YAAY,iBAAiB,iBAAiB,mBAAmB;AACvE,YAAM,aAAa,iBAAiB,WAAW;AAC/C,YAAM,oBAAoB,0BAA0B;AACpD,YAAM,WAAW;AACjB,YAAM,OAAO,MAAM,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE;AAC1C,kBAAY,IAAI;AAChB,2DAAmB,OAAO,MAAM;AAChC,YAAM,kBAAkB,CAAC,QAAQ;AAC7B,oBAAY,KAAK;AACjB,yDAAiB,KAAK,MAAM;AAAA,MAChC;AACA,YAAM,gBAAgB,CAAC,eAAe,2CAAc,MAAM;AAC1D,wBAAkB;AAAA,QACd;AAAA,QACA;AAAA,QACA;AAAA,QACA,WAAW;AAAA,QACX;AAAA,QACA,UAAU,MAAM;AAAA,QAChB,UAAU,MAAM;AAAA,QAChB;AAAA,QACA,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,MACpB,CAAC;AAAA,IACL;AACA,UAAM,+BAA+B,CAAC,UAAU,kBAAkB,OAAO,IAAI;AAC7E,UAAM,+BAA+B,CAAC,UAAU,kBAAkB,OAAO,KAAK;AAC9E,UAAM,0BAA0B,MAAM,eAAe,IAAI;AACzD,UAAM,wBAAwB,MAAM,eAAe,KAAK;AACxD,UAAM,WAAW,CAAC,sBAAsB,CAAC;AACzC,UAAM,YAAY,CAAC,UAAU;AAxuGrC;AAyuGY,UAAI,CAAC,uBAAuB,qBAAqB,SAAS,MAAM,GAAG,KAAK,oBAAoB;AACxF,cAAM,EAAE,uBAAuB,kBAAkB,MAAM,IAAI,MAAM,SAAS;AAC1E,cAAM,WAAW,MAAM,QAAQ;AAC/B,YAAI,UAAU;AACV,wBAAQ,YAAR,mBAAiB;AACjB,gCAAsB,EAAE,OAAO,CAAC,MAAM,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;AAAA,QACrE,OACK;AACD,2BAAiB,CAAC,EAAE,CAAC;AAAA,QACzB;AAAA,MACJ;AAAA,IACJ;AACA,WAAQ,cAAAC,QAAM;AAAA,MAAc;AAAA,MAAK,EAAE,WAAW,GAAG;AAAA,QACzC;AAAA,QACA,oBAAoB,IAAI;AAAA,QACxB;AAAA,QACA,EAAE,UAAU,UAAU,UAAU,UAAU,YAAY;AAAA,MAC1D,CAAC,GAAG,SAAS,aAAa,eAAe,0BAA0B,eAAe,mBAAmB,cAAc,kBAAkB,aAAa,iBAAiB,cAAc,kBAAkB,WAAW,cAAc,YAAY,QAAW,UAAU,cAAc,IAAI,QAAW,MAAM,cAAc,WAAW,OAAO,eAAe,YAAY,EAAE,IAAI,cAAc,cAAc,OAAO,SAAY,YAAY,YAAY,aAAa,MAAM,OAAO,MAAM,IAAI,oBAAoB,cAAc,GAAG,kBAAkB,IAAI,IAAI,KAAK,QAAW,KAAK,QAAQ;AAAA,MACxiB,CAAC,YAAa,cAAAA,QAAM,cAAc,eAAe,EAAE,IAAQ,QAAgB,QAAgB,UAAoB,UAAoB,OAAc,YAAwB,aAA0B,cAA4B,gBAAgC,qBAA0C,MAAY,OAAOD,QAAO,SAAkB,SAAkB,SAAkB,SAAkB,gBAAgC,gBAAgC,gBAAgC,gBAAgC,aAAa,gBAAgB,WAAW,cAAc,aAA0B,iBAAmC,CAAC;AAAA,MAC/nB,mBAAoB,cAAAC,QAAM;AAAA,QAAc,cAAAA,QAAM;AAAA,QAAU;AAAA,SACnD,oBAAoB,YAAY,oBAAoB,SAAU,cAAAA,QAAM,cAAc,YAAY,EAAE,UAAU,gBAAgB,SAAS,SAAS,SAAS,SAAS,QAAQ,iBAAiB,aAAa,8BAA8B,cAAc,yBAAyB,YAAY,uBAAuB,MAAM,SAAS,CAAC;AAAA,SAC5T,oBAAoB,YAAY,oBAAoB,SAAU,cAAAA,QAAM,cAAc,YAAY,EAAE,UAAU,gBAAgB,SAAS,SAAS,SAAS,SAAS,QAAQ,iBAAiB,aAAa,8BAA8B,cAAc,yBAAyB,YAAY,uBAAuB,MAAM,SAAS,CAAC;AAAA,MAAE;AAAA,IAAE;AAAA,EAC7U;AACA,cAAY,cAAc;AAC1B,aAAO,oBAAK,WAAW;AAC3B;AAEA,SAAS,gBAAgB,WAAW;AAChC,QAAM,gBAAgB;AAAA,IAClB,SAAS,SAAU,UAAU,WAAW,UAAW;AAAA,IACnD,UAAU,SAAU,UAAU,UAAU,YAAa;AAAA,IACrD,MAAM,SAAU,UAAU,QAAQ,QAAS;AAAA,IAC3C,YAAY,SAAU,UAAU,QAAQ,cAAe;AAAA,IACvD,cAAc,SAAU,UAAU,gBAAgB,gBAAiB;AAAA,EACvE;AACA,QAAM,eAAe,CAAC;AACtB,QAAM,eAAe,OAAO,KAAK,SAAS,EACrC,OAAO,CAAC,MAAM,CAAC,CAAC,WAAW,QAAQ,EAAE,SAAS,CAAC,CAAC,EAChD,OAAO,CAAC,KAAK,QAAQ;AACtB,QAAI,GAAG,IAAI,SAAU,UAAU,GAAG,KAAK,UAAW;AAClD,WAAO;AAAA,EACX,GAAG,YAAY;AACf,SAAO;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,EACP;AACJ;AACA,SAAS,kBAAkB,UAAU,UAAU,SAAS,MAAM;AAC1D,QAAM,MAAK,iCAAQ,MAAK,KAAK,SAAS;AACtC,QAAM,MAAK,iCAAQ,MAAK,KAAK,SAAS;AACtC,QAAM,SAAQ,iCAAQ,UAAS,SAAS;AACxC,QAAM,UAAS,iCAAQ,WAAU,SAAS;AAC1C,UAAQ,UAAU;AAAA,IACd,KAAK,SAAS;AACV,aAAO;AAAA,QACH,GAAG,IAAI,QAAQ;AAAA,QACf;AAAA,MACJ;AAAA,IACJ,KAAK,SAAS;AACV,aAAO;AAAA,QACH,GAAG,IAAI;AAAA,QACP,GAAG,IAAI,SAAS;AAAA,MACpB;AAAA,IACJ,KAAK,SAAS;AACV,aAAO;AAAA,QACH,GAAG,IAAI,QAAQ;AAAA,QACf,GAAG,IAAI;AAAA,MACX;AAAA,IACJ,KAAK,SAAS;AACV,aAAO;AAAA,QACH;AAAA,QACA,GAAG,IAAI,SAAS;AAAA,MACpB;AAAA,EACR;AACJ;AACA,SAAS,UAAU,QAAQ,UAAU;AACjC,MAAI,CAAC,QAAQ;AACT,WAAO;AAAA,EACX;AACA,MAAI,OAAO,WAAW,KAAK,CAAC,UAAU;AAClC,WAAO,OAAO,CAAC;AAAA,EACnB,WACS,UAAU;AACf,WAAO,OAAO,KAAK,CAAC,MAAM,EAAE,OAAO,QAAQ,KAAK;AAAA,EACpD;AACA,SAAO;AACX;AACA,IAAM,mBAAmB,CAAC,gBAAgB,cAAc,gBAAgB,gBAAgB,cAAc,mBAAmB;AACrH,QAAM,kBAAkB,kBAAkB,gBAAgB,gBAAgB,YAAY;AACtF,QAAM,kBAAkB,kBAAkB,gBAAgB,gBAAgB,YAAY;AACtF,SAAO;AAAA,IACH,SAAS,gBAAgB;AAAA,IACzB,SAAS,gBAAgB;AAAA,IACzB,SAAS,gBAAgB;AAAA,IACzB,SAAS,gBAAgB;AAAA,EAC7B;AACJ;AACA,SAAS,cAAc,EAAE,WAAW,WAAW,aAAa,cAAc,aAAa,cAAc,OAAO,QAAQ,UAAW,GAAG;AAC9H,QAAM,UAAU;AAAA,IACZ,GAAG,KAAK,IAAI,UAAU,GAAG,UAAU,CAAC;AAAA,IACpC,GAAG,KAAK,IAAI,UAAU,GAAG,UAAU,CAAC;AAAA,IACpC,IAAI,KAAK,IAAI,UAAU,IAAI,aAAa,UAAU,IAAI,WAAW;AAAA,IACjE,IAAI,KAAK,IAAI,UAAU,IAAI,cAAc,UAAU,IAAI,YAAY;AAAA,EACvE;AACA,MAAI,QAAQ,MAAM,QAAQ,IAAI;AAC1B,YAAQ,MAAM;AAAA,EAClB;AACA,MAAI,QAAQ,MAAM,QAAQ,IAAI;AAC1B,YAAQ,MAAM;AAAA,EAClB;AACA,QAAM,UAAU,UAAU;AAAA,IACtB,IAAI,IAAI,UAAU,CAAC,KAAK,UAAU,CAAC;AAAA,IACnC,IAAI,IAAI,UAAU,CAAC,KAAK,UAAU,CAAC;AAAA,IACnC,OAAO,QAAQ,UAAU,CAAC;AAAA,IAC1B,QAAQ,SAAS,UAAU,CAAC;AAAA,EAChC,CAAC;AACD,QAAM,WAAW,KAAK,IAAI,GAAG,KAAK,IAAI,QAAQ,IAAI,QAAQ,EAAE,IAAI,KAAK,IAAI,QAAQ,GAAG,QAAQ,CAAC,CAAC;AAC9F,QAAM,WAAW,KAAK,IAAI,GAAG,KAAK,IAAI,QAAQ,IAAI,QAAQ,EAAE,IAAI,KAAK,IAAI,QAAQ,GAAG,QAAQ,CAAC,CAAC;AAC9F,QAAM,kBAAkB,KAAK,KAAK,WAAW,QAAQ;AACrD,SAAO,kBAAkB;AAC7B;AACA,SAAS,YAAY,MAAM;AAl2G3B;AAm2GI,QAAM,iBAAe,kCAAO,qBAAP,mBAAyB,iBAAgB;AAC9D,QAAM,UAAU,iBACZ,6BAAM,WACN,6BAAM,WACN,SAAO,kCAAM,qBAAN,mBAAwB,OAAM,eACrC,SAAO,kCAAM,qBAAN,mBAAwB,OAAM;AACzC,SAAO;AAAA,IACH;AAAA,MACI,KAAG,kCAAM,qBAAN,mBAAwB,MAAK;AAAA,MAChC,KAAG,kCAAM,qBAAN,mBAAwB,MAAK;AAAA,MAChC,QAAO,6BAAM,UAAS;AAAA,MACtB,SAAQ,6BAAM,WAAU;AAAA,IAC5B;AAAA,IACA;AAAA,IACA,CAAC,CAAC;AAAA,EACN;AACJ;AAEA,IAAM,kBAAkB,CAAC,EAAE,OAAO,GAAG,YAAY,MAAM,OAAO,CAAC,EAAE,CAAC;AAClE,SAAS,mBAAmB,OAAO,eAAe,uBAAuB,OAAO;AAC5E,MAAI,WAAW;AACf,QAAM,cAAc,MAAM,OAAO,CAAC,MAAM,SAAS;AAx3GrD;AAy3GQ,UAAM,YAAY,UAAU,KAAK,MAAM;AACvC,QAAI,IAAI,YAAY,KAAK,SAAS;AAClC,QAAI,sBAAsB;AACtB,YAAM,aAAa,cAAc,IAAI,KAAK,MAAM;AAChD,YAAM,aAAa,cAAc,IAAI,KAAK,MAAM;AAChD,YAAM,8BAA8B,KAAK,aAAY,yCAAY,cAAY,yCAAY;AACzF,YAAM,iBAAiB,KAAK,MAAI,8CAAa,qBAAb,mBAA+B,MAAK,KAAG,8CAAa,qBAAb,mBAA+B,MAAK,GAAG,GAAI;AAClH,WAAK,YAAY,KAAK,SAAS,MAAM,8BAA8B,iBAAiB;AAAA,IACxF;AACA,QAAI,KAAK,CAAC,GAAG;AACT,WAAK,CAAC,EAAE,KAAK,IAAI;AAAA,IACrB,OACK;AACD,WAAK,CAAC,IAAI,CAAC,IAAI;AAAA,IACnB;AACA,eAAW,IAAI,WAAW,IAAI;AAC9B,WAAO;AAAA,EACX,GAAG,CAAC,CAAC;AACL,QAAM,WAAW,OAAO,QAAQ,WAAW,EAAE,IAAI,CAAC,CAAC,KAAKkB,MAAK,MAAM;AAC/D,UAAM,QAAQ,CAAC;AACf,WAAO;AAAA,MACH,OAAAA;AAAA,MACA;AAAA,MACA,YAAY,UAAU;AAAA,IAC1B;AAAA,EACJ,CAAC;AACD,MAAI,SAAS,WAAW,GAAG;AACvB,WAAO;AAAA,EACX;AACA,SAAO;AACX;AACA,SAAS,gBAAgB,mBAAmB,eAAe,sBAAsB;AAC7E,QAAM,QAAQ,aAAS,2BAAY,CAAC,MAAM;AACtC,QAAI,CAAC,mBAAmB;AACpB,aAAO,EAAE;AAAA,IACb;AACA,WAAO,EAAE,MAAM,OAAO,CAAC,MAAM;AACzB,YAAM,aAAa,cAAc,IAAI,EAAE,MAAM;AAC7C,YAAM,aAAa,cAAc,IAAI,EAAE,MAAM;AAC7C,cAAQ,yCAAY,WAChB,yCAAY,YACZ,yCAAY,WACZ,yCAAY,WACZ,cAAc;AAAA,QACV,WAAW,WAAW,oBAAoB,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,QACvD,WAAW,WAAW,oBAAoB,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,QACvD,aAAa,WAAW;AAAA,QACxB,cAAc,WAAW;AAAA,QACzB,aAAa,WAAW;AAAA,QACxB,cAAc,WAAW;AAAA,QACzB,OAAO,EAAE;AAAA,QACT,QAAQ,EAAE;AAAA,QACV,WAAW,EAAE;AAAA,MACjB,CAAC;AAAA,IACT,CAAC;AAAA,EACL,GAAG,CAAC,mBAAmB,aAAa,CAAC,CAAC;AACtC,SAAO,mBAAmB,OAAO,eAAe,oBAAoB;AACxE;AAEA,IAAM,cAAc,CAAC,EAAE,QAAQ,QAAQ,cAAc,EAAE,MAAM;AACzD,SAAQ,cAAAlB,QAAM,cAAc,YAAY,EAAE,OAAO;AAAA,IACzC,QAAQ;AAAA,IACR;AAAA,EACJ,GAAG,eAAe,SAAS,gBAAgB,SAAS,MAAM,QAAQ,QAAQ,iBAAiB,CAAC;AACpG;AACA,IAAM,oBAAoB,CAAC,EAAE,QAAQ,QAAQ,cAAc,EAAE,MAAM;AAC/D,SAAQ,cAAAA,QAAM,cAAc,YAAY,EAAE,OAAO;AAAA,IACzC,QAAQ;AAAA,IACR,MAAM;AAAA,IACN;AAAA,EACJ,GAAG,eAAe,SAAS,gBAAgB,SAAS,QAAQ,uBAAuB,CAAC;AAC5F;AACA,IAAM,gBAAgB;AAAA,EAClB,CAAC,WAAW,KAAK,GAAG;AAAA,EACpB,CAAC,WAAW,WAAW,GAAG;AAC9B;AACA,SAAS,gBAAgB,MAAM;AAC3B,QAAM,QAAQ,YAAY;AAC1B,QAAM,aAAS,uBAAQ,MAAM;AAv8GjC;AAw8GQ,UAAM,eAAe,OAAO,UAAU,eAAe,KAAK,eAAe,IAAI;AAC7E,QAAI,CAAC,cAAc;AACf,wBAAM,SAAS,GAAE,YAAjB,4BAA2B,OAAO,cAAc,UAAU,EAAE,IAAI;AAChE,aAAO;AAAA,IACX;AACA,WAAO,cAAc,IAAI;AAAA,EAC7B,GAAG,CAAC,IAAI,CAAC;AACT,SAAO;AACX;AAEA,IAAM,SAAS,CAAC,EAAE,IAAI,MAAM,OAAO,QAAQ,MAAM,SAAS,MAAM,cAAc,eAAe,aAAa,SAAS,qBAAsB,MAAM;AAC3I,QAAMmB,UAAS,gBAAgB,IAAI;AACnC,MAAI,CAACA,SAAQ;AACT,WAAO;AAAA,EACX;AACA,SAAQ,cAAAnB,QAAM;AAAA,IAAc;AAAA,IAAU,EAAE,WAAW,yBAAyB,IAAQ,aAAa,GAAG,KAAK,IAAI,cAAc,GAAG,MAAM,IAAI,SAAS,iBAAiB,aAA0B,QAAgB,MAAM,KAAK,MAAM,IAAI;AAAA,IAC7N,cAAAA,QAAM,cAAcmB,SAAQ,EAAE,OAAc,YAAyB,CAAC;AAAA,EAAC;AAC/E;AACA,IAAM,iBAAiB,CAAC,EAAE,cAAAC,eAAc,KAAK,MAAM,CAAC,MAAM;AACtD,QAAM,MAAM,CAAC;AACb,SAAO,EAAE,MACJ,OAAO,CAAC,SAAS,SAAS;AAC3B,KAAC,KAAK,aAAa,KAAK,SAAS,EAAE,QAAQ,CAAC,WAAW;AACnD,UAAI,UAAU,OAAO,WAAW,UAAU;AACtC,cAAM,WAAW,YAAY,QAAQ,IAAI;AACzC,YAAI,CAAC,IAAI,SAAS,QAAQ,GAAG;AACzB,kBAAQ,KAAK,EAAE,IAAI,UAAU,OAAO,OAAO,SAASA,eAAc,GAAG,OAAO,CAAC;AAC7E,cAAI,KAAK,QAAQ;AAAA,QACrB;AAAA,MACJ;AAAA,IACJ,CAAC;AACD,WAAO;AAAA,EACX,GAAG,CAAC,CAAC,EACA,KAAK,CAAC,GAAG,MAAM,EAAE,GAAG,cAAc,EAAE,EAAE,CAAC;AAChD;AAIA,IAAM,oBAAoB,CAAC,EAAE,cAAAA,eAAc,KAAK,MAAM;AAClD,QAAM,UAAU;AAAA,QAAS,2BAAY,eAAe,EAAE,cAAAA,eAAc,KAAK,CAAC,GAAG,CAACA,eAAc,IAAI,CAAC;AAAA;AAAA,IAEjG,CAAC,GAAG,MAAM,EAAE,EAAE,WAAW,EAAE,UAAU,EAAE,KAAK,CAAC,GAAG,MAAM,EAAE,OAAO,EAAE,CAAC,EAAE,EAAE;AAAA,EAAE;AACxE,SAAQ,cAAApB,QAAM,cAAc,QAAQ,MAAM,QAAQ,IAAI,CAAC,WAAY,cAAAA,QAAM,cAAc,QAAQ,EAAE,IAAI,OAAO,IAAI,KAAK,OAAO,IAAI,MAAM,OAAO,MAAM,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,QAAQ,OAAO,QAAQ,aAAa,OAAO,aAAa,aAAa,OAAO,aAAa,QAAQ,OAAO,OAAO,CAAC,CAAE,CAAC;AACpT;AACA,kBAAkB,cAAc;AAChC,IAAI,0BAAsB,oBAAK,iBAAiB;AAEhD,IAAM,aAAa,CAAC,OAAO;AAAA,EACvB,kBAAkB,EAAE;AAAA,EACpB,gBAAgB,EAAE;AAAA,EAClB,gBAAgB,EAAE;AAAA,EAClB,oBAAoB,EAAE;AAAA,EACtB,OAAO,EAAE;AAAA,EACT,QAAQ,EAAE;AAAA,EACV,gBAAgB,EAAE;AAAA,EAClB,eAAe,EAAE;AAAA,EACjB,SAAS,EAAE;AACf;AACA,IAAM,eAAe,CAAC,EAAE,oBAAoB,2BAA2B,sBAAsB,MAAM,WAAW,gBAAgB,mBAAmB,kBAAkB,iBAAiB,kBAAkB,aAAa,mBAAmB,aAAa,kBAAkB,gBAAgB,iBAAiB,UAAU,oBAAqB,MAAM;AACvU,QAAM,EAAE,gBAAgB,gBAAgB,oBAAoB,OAAO,QAAQ,gBAAgB,eAAe,QAAQ,IAAI,SAAS,YAAY,SAAO;AAClJ,QAAM,WAAW,gBAAgB,2BAA2B,eAAe,oBAAoB;AAC/F,MAAI,CAAC,OAAO;AACR,WAAO;AAAA,EACX;AACA,SAAQ,cAAAA,QAAM;AAAA,IAAc,cAAAA,QAAM;AAAA,IAAU;AAAA,IACxC,SAAS,IAAI,CAAC,EAAE,OAAO,OAAO,WAAW,MAAO,cAAAA,QAAM;AAAA,MAAc;AAAA,MAAO,EAAE,KAAK,OAAO,OAAO,EAAE,QAAQ,MAAM,GAAG,OAAc,QAAgB,WAAW,0CAA0C;AAAA,MAClM,cAAc,cAAAA,QAAM,cAAc,qBAAqB,EAAE,cAAc,oBAAoB,KAAW,CAAC;AAAA,MACvG,cAAAA,QAAM,cAAc,KAAK,MAAM,MAAM,IAAI,CAAC,SAAS;AAC/C,cAAM,CAAC,gBAAgB,oBAAoB,aAAa,IAAI,YAAY,cAAc,IAAI,KAAK,MAAM,CAAC;AACtG,cAAM,CAAC,gBAAgB,oBAAoB,aAAa,IAAI,YAAY,cAAc,IAAI,KAAK,MAAM,CAAC;AACtG,YAAI,CAAC,iBAAiB,CAAC,eAAe;AAClC,iBAAO;AAAA,QACX;AACA,YAAI,WAAW,KAAK,QAAQ;AAC5B,YAAI,CAAC,UAAU,QAAQ,GAAG;AACtB,6CAAU,OAAO,cAAc,UAAU,EAAE,QAAQ;AACnD,qBAAW;AAAA,QACf;AACA,cAAM,gBAAgB,UAAU,QAAQ,KAAK,UAAU;AAEvD,cAAM,oBAAoB,mBAAmB,eAAe,SACtD,mBAAmB,UAClB,mBAAmB,UAAU,CAAC,GAAG,OAAO,mBAAmB,UAAU,CAAC,CAAC;AAC9E,cAAM,eAAe,UAAU,mBAAmB,QAAQ,KAAK,YAAY;AAC3E,cAAM,eAAe,UAAU,mBAAmB,KAAK,YAAY;AACnE,cAAM,kBAAiB,6CAAc,aAAY,SAAS;AAC1D,cAAM,kBAAiB,6CAAc,aAAY,SAAS;AAC1D,cAAM,cAAc,CAAC,EAAE,KAAK,aAAc,kBAAkB,OAAO,KAAK,cAAc;AACtF,cAAM,oBAAoB,KAAK,iBAAiB,KAAK;AACrD,cAAM,kBAAkB,OAAO,gBAAgB,gBAC1C,qBAAsB,kBAAkB,OAAO,sBAAsB;AAC1E,YAAI,CAAC,gBAAgB,CAAC,cAAc;AAChC,6CAAU,OAAO,cAAc,UAAU,EAAE,cAAc,IAAI;AAC7D,iBAAO;AAAA,QACX;AACA,cAAM,EAAE,SAAS,SAAS,SAAS,QAAQ,IAAI,iBAAiB,gBAAgB,cAAc,gBAAgB,gBAAgB,cAAc,cAAc;AAC1J,eAAQ,cAAAA,QAAM,cAAc,eAAe,EAAE,KAAK,KAAK,IAAI,IAAI,KAAK,IAAI,WAAW,GAAG,CAAC,KAAK,WAAW,cAAc,CAAC,GAAG,MAAM,UAAU,MAAM,KAAK,MAAM,UAAU,CAAC,CAAC,KAAK,UAAU,UAAU,CAAC,CAAC,KAAK,UAAU,QAAQ,CAAC,CAAC,KAAK,QAAQ,OAAO,KAAK,OAAO,YAAY,KAAK,YAAY,aAAa,KAAK,aAAa,cAAc,KAAK,cAAc,gBAAgB,KAAK,gBAAgB,qBAAqB,KAAK,qBAAqB,OAAO,KAAK,OAAO,QAAQ,KAAK,QAAQ,QAAQ,KAAK,QAAQ,gBAAgB,KAAK,cAAc,gBAAgB,KAAK,cAAc,WAAW,KAAK,WAAW,aAAa,KAAK,aAAa,SAAkB,SAAkB,SAAkB,SAAkB,gBAAgC,gBAAgC,oBAAwC,eAAe,mBAAmB,cAAc,kBAAkB,aAAa,iBAAiB,cAAc,kBAAkB,SAAS,aAAa,mBAAsC,aAA0B,kBAAoC,gBAAgC,iBAAkC,MAAY,WAAW,KAAK,WAAW,aAA0B,iBAAkC,aAAa,iBAAiB,OAAO,KAAK,cAAc,QAAW,kBAAkB,KAAK,kBAAkB,oBAAyC,CAAC;AAAA,MACj1C,CAAC,CAAC;AAAA,IAAC,CAAE;AAAA,IACT;AAAA,EAAQ;AAChB;AACA,aAAa,cAAc;AAC3B,IAAI,qBAAiB,oBAAK,YAAY;AAEtC,IAAM,aAAa,CAAC,MAAM,aAAa,EAAE,UAAU,CAAC,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;AACpG,SAAS,SAAS,EAAE,SAAS,GAAG;AAC5B,QAAM,YAAY,SAAS,UAAU;AACrC,SAAQ,cAAAA,QAAM,cAAc,OAAO,EAAE,WAAW,8CAA8C,OAAO,EAAE,UAAU,EAAE,GAAG,QAAQ;AAClI;AAEA,SAAS,iBAAiB,QAAQ;AAC9B,QAAM,aAAa,aAAa;AAChC,QAAM,oBAAgB,sBAAO,KAAK;AAClC,+BAAU,MAAM;AACZ,QAAI,CAAC,cAAc,WAAW,WAAW,uBAAuB,QAAQ;AACpE,iBAAW,MAAM,OAAO,UAAU,GAAG,CAAC;AACtC,oBAAc,UAAU;AAAA,IAC5B;AAAA,EACJ,GAAG,CAAC,QAAQ,WAAW,mBAAmB,CAAC;AAC/C;AAEA,IAAM,mBAAmB;AAAA,EACrB,CAAC,SAAS,IAAI,GAAG,SAAS;AAAA,EAC1B,CAAC,SAAS,KAAK,GAAG,SAAS;AAAA,EAC3B,CAAC,SAAS,GAAG,GAAG,SAAS;AAAA,EACzB,CAAC,SAAS,MAAM,GAAG,SAAS;AAChC;AACA,IAAM,iBAAiB,CAAC,EAAE,QAAQ,YAAY,OAAAD,QAAO,OAAO,mBAAmB,QAAQ,iBAAiB,iBAAkB,MAAM;AAtkHhI;AAukHI,QAAM,EAAE,UAAU,UAAU,KAAK,KAAK,eAAe,IAAI,aAAS,2BAAY,CAAC,OAAO;AAAA,IAClF,UAAU,EAAE,cAAc,IAAI,MAAM;AAAA,IACpC,UAAU,EAAE;AAAA,IACZ,MAAM,EAAE,mBAAmB,IAAI,EAAE,UAAU,CAAC,KAAK,EAAE,UAAU,CAAC;AAAA,IAC9D,MAAM,EAAE,mBAAmB,IAAI,EAAE,UAAU,CAAC,KAAK,EAAE,UAAU,CAAC;AAAA,IAC9D,gBAAgB,EAAE;AAAA,EACtB,IAAI,CAAC,MAAM,CAAC,GAAG,SAAO;AACtB,QAAM,oBAAmB,0CAAW,qBAAX,mBAA6B;AACtD,MAAI,eAAe,qDAAmB;AACtC,MAAI,mBAAmB,eAAe,OAAO;AACzC,mBAAe,eAAe,eAAe,qDAAmB,eAAe,WAAW,WAAW;AAAA,EACzG;AACA,MAAI,CAAC,YAAY,CAAC,cAAc;AAC5B,WAAO;AAAA,EACX;AACA,QAAM,aAAa,WAAW,aAAa,KAAK,CAAC,MAAM,EAAE,OAAO,QAAQ,IAAI,aAAa,CAAC;AAC1F,QAAM,cAAc,aAAa,WAAW,IAAI,WAAW,QAAQ,KAAK,SAAS,SAAS,KAAK;AAC/F,QAAM,cAAc,aAAa,WAAW,IAAI,WAAW,SAAS,IAAI,SAAS,UAAU;AAC3F,QAAM,WAAS,cAAS,qBAAT,mBAA2B,MAAK,KAAK;AACpD,QAAM,WAAS,cAAS,qBAAT,mBAA2B,MAAK,KAAK;AACpD,QAAM,eAAe,yCAAY;AACjC,QAAM,aAAa,eAAe,iBAAiB,YAAY,IAAI;AACnE,MAAI,CAAC,gBAAgB,CAAC,YAAY;AAC9B,WAAO;AAAA,EACX;AACA,MAAI,iBAAiB;AACjB,WAAQ,cAAAC,QAAM,cAAc,iBAAiB,EAAE,oBAAoB,MAAM,qBAAqBD,QAAO,UAAoB,YAAwB,OAAc,OAAc,KAAU,KAAU,cAA4B,YAAwB,iBAAmC,CAAC;AAAA,EAC7R;AACA,MAAI,QAAQ;AACZ,QAAM,aAAa;AAAA,IACf,SAAS;AAAA,IACT,SAAS;AAAA,IACT,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,SAAS;AAAA,IACT,gBAAgB;AAAA,EACpB;AACA,MAAI,SAAS,mBAAmB,QAAQ;AAEpC,KAAC,KAAK,IAAI,cAAc,UAAU;AAAA,EACtC,WACS,SAAS,mBAAmB,MAAM;AACvC,KAAC,KAAK,IAAI,kBAAkB;AAAA,MACxB,GAAG;AAAA,MACH,cAAc;AAAA,IAClB,CAAC;AAAA,EACL,WACS,SAAS,mBAAmB,YAAY;AAC7C,KAAC,KAAK,IAAI,kBAAkB,UAAU;AAAA,EAC1C,WACS,SAAS,mBAAmB,cAAc;AAC/C,KAAC,KAAK,IAAI,oBAAoB,UAAU;AAAA,EAC5C,OACK;AACD,YAAQ,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,GAAG;AAAA,EAC5C;AACA,SAAO,cAAAC,QAAM,cAAc,QAAQ,EAAE,GAAG,OAAO,MAAM,QAAQ,WAAW,+BAA+B,OAAOD,OAAM,CAAC;AACzH;AACA,eAAe,cAAc;AAC7B,IAAM,aAAa,CAAC,OAAO;AAAA,EACvB,QAAQ,EAAE;AAAA,EACV,YAAY,EAAE;AAAA,EACd,kBAAkB,EAAE;AAAA,EACpB,kBAAkB,EAAE;AAAA,EACpB,OAAO,EAAE;AAAA,EACT,QAAQ,EAAE;AACd;AACA,SAAS,sBAAsB,EAAE,gBAAAsB,iBAAgB,OAAAtB,QAAO,MAAM,UAAU,GAAG;AACvE,QAAM,EAAE,QAAQ,YAAY,kBAAkB,OAAO,QAAQ,iBAAiB,IAAI,SAAS,YAAY,SAAO;AAC9G,QAAM,UAAU,CAAC,EAAE,UAAU,cAAc,SAAS;AACpD,MAAI,CAAC,SAAS;AACV,WAAO;AAAA,EACX;AACA,SAAQ,cAAAC,QAAM;AAAA,IAAc;AAAA,IAAO,EAAE,OAAOqB,iBAAgB,OAAc,QAAgB,WAAW,qEAAqE;AAAA,IACtK,cAAArB,QAAM;AAAA,MAAc;AAAA,MAAK,EAAE,WAAW,GAAG,CAAC,0BAA0B,gBAAgB,CAAC,EAAE;AAAA,MACnF,cAAAA,QAAM,cAAc,gBAAgB,EAAE,QAAgB,YAAwB,OAAOD,QAAO,MAAY,iBAAiB,WAAW,iBAAmC,CAAC;AAAA,IAAC;AAAA,EAAC;AACtL;AAGA,SAAS,mBAAmB,iBAAiB,aAAa;AACtD,QAAM,mBAAe,sBAAO,IAAI;AAChC,QAAM,QAAQ,YAAY;AAC1B,QAAM,kBAAc,uBAAQ,MAAM;AAzpHtC;AA0pHQ,QAAI,MAAwC;AACxC,YAAM,WAAW,OAAO,KAAK,eAAe;AAC5C,UAAI,UAAQ,aAAa,SAAS,QAAQ,GAAG;AACzC,0BAAM,SAAS,GAAE,YAAjB,4BAA2B,OAAO,cAAc,UAAU,EAAE;AAAA,MAChE;AACA,mBAAa,UAAU;AAAA,IAC3B;AACA,WAAO,YAAY,eAAe;AAAA,EACtC,GAAG,CAAC,eAAe,CAAC;AACpB,SAAO;AACX;AAEA,IAAM,YAAY,CAAC,EAAE,WAAW,WAAW,QAAQ,aAAa,WAAW,QAAQ,aAAa,aAAa,mBAAmB,mBAAmB,kBAAkB,iBAAiB,kBAAkB,mBAAmB,wBAAwB,kBAAkB,gBAAgB,oBAAoB,qBAAqB,yBAAyB,8BAA8B,kBAAkB,iBAAiB,eAAe,uBAAuB,sBAAsB,uBAAuB,eAAe,2BAA2B,oBAAoB,mBAAmB,iBAAiB,iBAAiB,SAAS,SAAS,kBAAkB,oBAAoB,cAAc,aAAa,aAAa,kBAAkB,iBAAiB,mBAAmB,WAAW,aAAa,kBAAkB,iBAAiB,kBAAkB,cAAc,mBAAmB,mBAAmB,kBAAkB,iBAAiB,kBAAkB,aAAa,kBAAkB,gBAAgB,iBAAiB,iBAAiB,kBAAkB,gBAAgB,sBAAsB,qBAAqB,YAAY,YAAY,KAAM,MAAM;AACxmC,QAAM,mBAAmB,mBAAmB,WAAW,eAAe;AACtE,QAAM,mBAAmB,mBAAmB,WAAW,eAAe;AACtE,mBAAiB,MAAM;AACvB,SAAQ,cAAAC,QAAM;AAAA,IAAc;AAAA,IAAgB,EAAE,aAA0B,kBAAoC,iBAAkC,kBAAoC,mBAAsC,cAA4B,eAA8B,kBAAoC,iBAAkC,eAA8B,kBAAoC,gBAAgC,uBAA8C,sBAA4C,uBAA8C,oBAAwC,QAAgB,aAA0B,WAAsB,cAA4B,aAA0B,mBAAsC,aAA0B,kBAAoC,iBAAkC,WAAsB,iBAAkC,iBAAkC,SAAkB,SAAkB,wBAAgD,kBAAoC,iBAAkC,kBAAoC,gBAAgC,oBAAyC;AAAA,IACnsC,cAAAA,QAAM;AAAA,MAAc;AAAA,MAAU;AAAA,MAC1B,cAAAA,QAAM;AAAA,QAAc;AAAA,QAAgB,EAAE,WAAW,kBAAkB,aAA0B,mBAAsC,2BAAsD,mBAAsC,kBAAoC,iBAAkC,kBAAoC,aAA0B,kBAAoC,gBAAgC,iBAAkC,oBAAwC,gBAAgC,sBAAsB,CAAC,CAAC,sBAAsB,qBAA0C,KAAW;AAAA,QAChnB,cAAAA,QAAM,cAAc,uBAAuB,EAAE,OAAO,qBAAqB,MAAM,oBAAoB,WAAW,yBAAyB,gBAAgB,6BAA6B,CAAC;AAAA,MAAC;AAAA,MAC1L,cAAAA,QAAM,cAAc,OAAO,EAAE,WAAW,iCAAiC,CAAC;AAAA,MAC1E,cAAAA,QAAM,cAAc,gBAAgB,EAAE,WAAW,kBAAkB,aAA0B,mBAAsC,kBAAoC,iBAAkC,kBAAoC,mBAAsC,mBAAsC,2BAAsD,gBAAgC,iBAAkC,qBAA0C,YAAwB,YAAwB,KAAW,CAAC;AAAA,IAAC;AAAA,EAAC;AACriB;AACA,UAAU,cAAc;AACxB,IAAI,kBAAc,oBAAK,SAAS;AAEhC,IAAM,iBAAiB;AAAA,EACnB,CAAC,OAAO,mBAAmB,OAAO,iBAAiB;AAAA,EACnD,CAAC,OAAO,mBAAmB,OAAO,iBAAiB;AACvD;AACA,IAAM,eAAe;AAAA,EACjB,MAAM;AAAA,EACN,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,WAAW,CAAC,GAAG,GAAG,CAAC;AAAA,EACnB,eAAe,oBAAI,IAAI;AAAA,EACvB,OAAO,CAAC;AAAA,EACR,eAAe;AAAA,EACf,eAAe;AAAA,EACf,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,eAAe;AAAA,EACf,SAAS;AAAA,EACT,SAAS;AAAA,EACT,iBAAiB;AAAA,EACjB,YAAY;AAAA,EACZ,sBAAsB;AAAA,EACtB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,sBAAsB;AAAA,EACtB,oBAAoB,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,EACjC,kBAAkB;AAAA,EAClB,gBAAgB,eAAe;AAAA,EAC/B,SAAS;AAAA,EACT,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,YAAY,CAAC,GAAG,CAAC;AAAA,EACjB,mBAAmB;AAAA,EACnB,UAAU,CAAC,IAAI,EAAE;AAAA,EACjB,YAAY;AAAA,EACZ,gBAAgB;AAAA,EAChB,kBAAkB;AAAA,EAClB,gBAAgB;AAAA,EAChB,gBAAgB;AAAA,EAChB,gBAAgB;AAAA,EAChB,oBAAoB;AAAA,EACpB,sBAAsB;AAAA,EACtB,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,sBAAsB;AAAA,EACtB,mBAAmB,CAAC;AAAA,EACpB,sBAAsB;AAAA,EACtB,uBAAuB;AAAA,EACvB,qBAAqB;AAAA,EACrB,4BAA4B;AAAA,EAC5B,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,kBAAkB;AAAA,EAClB,SAAS;AAAA,EACT,mBAAmB;AACvB;AAEA,IAAM,gBAAgB,MAAM,qBAAqB,CAAC,KAAK,SAAS;AAAA,EAC5D,GAAG;AAAA,EACH,UAAU,CAAC,UAAU;AACjB,UAAM,EAAE,eAAe,YAAY,qBAAqB,IAAI,IAAI;AAChE,QAAI,EAAE,eAAe,oBAAoB,OAAO,eAAe,YAAY,oBAAoB,EAAE,CAAC;AAAA,EACtG;AAAA,EACA,UAAU,MAAM;AACZ,WAAO,MAAM,KAAK,IAAI,EAAE,cAAc,OAAO,CAAC;AAAA,EAClD;AAAA,EACA,UAAU,CAAC,UAAU;AACjB,UAAM,EAAE,qBAAqB,CAAC,EAAE,IAAI,IAAI;AACxC,QAAI,EAAE,OAAO,MAAM,IAAI,CAAC,OAAO,EAAE,GAAG,oBAAoB,GAAG,EAAE,EAAE,EAAE,CAAC;AAAA,EACtE;AAAA,EACA,yBAAyB,CAAC,OAAO,UAAU;AACvC,UAAM,kBAAkB,OAAO,UAAU;AACzC,UAAM,kBAAkB,OAAO,UAAU;AACzC,UAAM,gBAAgB,kBAChB,oBAAoB,OAAO,oBAAI,IAAI,GAAG,IAAI,EAAE,YAAY,IAAI,EAAE,oBAAoB,IAClF,oBAAI,IAAI;AACd,UAAM,YAAY,kBAAkB,QAAQ,CAAC;AAC7C,QAAI,EAAE,eAAe,OAAO,WAAW,iBAAiB,gBAAgB,CAAC;AAAA,EAC7E;AAAA,EACA,sBAAsB,CAAC,YAAY;AAC/B,UAAM,EAAE,eAAe,eAAe,eAAe,mBAAmB,sBAAsB,SAAS,WAAY,IAAI,IAAI;AAC3H,UAAM,eAAe,mCAAS,cAAc;AAC5C,QAAI,CAAC,cAAc;AACf;AAAA,IACJ;AACA,UAAMD,SAAQ,OAAO,iBAAiB,YAAY;AAClD,UAAM,EAAE,KAAK,KAAK,IAAI,IAAI,OAAO,kBAAkBA,OAAM,SAAS;AAClE,UAAM,UAAU,QAAQ,OAAO,CAAC,KAAK,WAAW;AAC5C,YAAM,OAAO,cAAc,IAAI,OAAO,EAAE;AACxC,UAAI,6BAAM,QAAQ;AACd,sBAAc,IAAI,KAAK,IAAI;AAAA,UACvB,GAAG;AAAA,UACH,CAAC,eAAe,GAAG;AAAA,YACf,GAAG,KAAK,eAAe;AAAA;AAAA;AAAA,YAGvB,cAAc;AAAA,UAClB;AAAA,QACJ,CAAC;AAAA,MACL,WACS,MAAM;AACX,cAAM,aAAa,cAAc,OAAO,WAAW;AACnD,cAAM,WAAW,CAAC,EAAE,WAAW,SAC3B,WAAW,WACV,KAAK,UAAU,WAAW,SAAS,KAAK,WAAW,WAAW,UAAU,OAAO;AACpF,YAAI,UAAU;AACV,wBAAc,IAAI,KAAK,IAAI;AAAA,YACvB,GAAG;AAAA,YACH,CAAC,eAAe,GAAG;AAAA,cACf,GAAG,KAAK,eAAe;AAAA,cACvB,cAAc;AAAA,gBACV,QAAQ,gBAAgB,WAAW,OAAO,aAAa,MAAM,UAAU;AAAA,gBACvE,QAAQ,gBAAgB,WAAW,OAAO,aAAa,MAAM,UAAU;AAAA,cAC3E;AAAA,YACJ;AAAA,YACA,GAAG;AAAA,UACP,CAAC;AACD,cAAI,KAAK;AAAA,YACL,IAAI,KAAK;AAAA,YACT,MAAM;AAAA,YACN;AAAA,UACJ,CAAC;AAAA,QACL;AAAA,MACJ;AACA,aAAO;AAAA,IACX,GAAG,CAAC,CAAC;AACL,gCAA4B,eAAe,UAAU;AACrD,UAAM,wBAAwB,qBACzB,iBAAiB,CAAC,qBAAqB,QAAQ,KAAK,EAAE,SAAS,MAAM,GAAG,qBAAqB,CAAC;AACnG,QAAI,EAAE,eAAe,IAAI,IAAI,aAAa,GAAG,mBAAmB,sBAAsB,CAAC;AACvF,SAAI,mCAAS,UAAS,GAAG;AACrB,qDAAgB;AAAA,IACpB;AAAA,EACJ;AAAA,EACA,qBAAqB,CAAC,eAAe,kBAAkB,MAAM,WAAW,UAAU;AAC9E,UAAM,EAAE,mBAAmB,IAAI,IAAI;AACnC,UAAM,UAAU,cAAc,IAAI,CAAC,SAAS;AACxC,YAAM,SAAS;AAAA,QACX,IAAI,KAAK;AAAA,QACT,MAAM;AAAA,QACN;AAAA,MACJ;AACA,UAAI,iBAAiB;AACjB,eAAO,mBAAmB,KAAK;AAC/B,eAAO,WAAW,KAAK;AAAA,MAC3B;AACA,aAAO;AAAA,IACX,CAAC;AACD,uBAAmB,OAAO;AAAA,EAC9B;AAAA,EACA,oBAAoB,CAAC,YAAY;AAC7B,UAAM,EAAE,eAAe,eAAe,iBAAiB,YAAY,UAAU,qBAAqB,IAAI,IAAI;AAC1G,QAAI,mCAAS,QAAQ;AACjB,UAAI,iBAAiB;AACjB,cAAM,QAAQ,iBAAiB,SAAS,SAAS,CAAC;AAClD,cAAM,oBAAoB,oBAAoB,OAAO,eAAe,YAAY,oBAAoB;AACpG,YAAI,EAAE,eAAe,kBAAkB,CAAC;AAAA,MAC5C;AACA,qDAAgB;AAAA,IACpB;AAAA,EACJ;AAAA,EACA,kBAAkB,CAAC,oBAAoB;AACnC,UAAM,EAAE,sBAAsB,OAAO,SAAS,IAAI,IAAI;AACtD,QAAI;AACJ,QAAI,eAAe;AACnB,QAAI,sBAAsB;AACtB,qBAAe,gBAAgB,IAAI,CAAC,WAAW,sBAAsB,QAAQ,IAAI,CAAC;AAAA,IACtF,OACK;AACD,qBAAe,oBAAoB,SAAS,GAAG,eAAe;AAC9D,qBAAe,oBAAoB,OAAO,CAAC,CAAC;AAAA,IAChD;AACA,kCAA8B;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,kBAAkB,CAAC,oBAAoB;AACnC,UAAM,EAAE,sBAAsB,OAAO,SAAS,IAAI,IAAI;AACtD,QAAI;AACJ,QAAI,eAAe;AACnB,QAAI,sBAAsB;AACtB,qBAAe,gBAAgB,IAAI,CAAC,WAAW,sBAAsB,QAAQ,IAAI,CAAC;AAAA,IACtF,OACK;AACD,qBAAe,oBAAoB,OAAO,eAAe;AACzD,qBAAe,oBAAoB,SAAS,GAAG,CAAC,CAAC;AAAA,IACrD;AACA,kCAA8B;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,uBAAuB,CAAC,EAAE,OAAO,MAAM,IAAI,CAAC,MAAM;AAC9C,UAAM,EAAE,OAAO,YAAY,SAAS,IAAI,IAAI;AAC5C,UAAM,kBAAkB,QAAQ,QAAQ,SAAS;AACjD,UAAM,kBAAkB,QAAQ,QAAQ;AACxC,UAAM,eAAe,gBAAgB,IAAI,CAAC,MAAM;AAC5C,QAAE,WAAW;AACb,aAAO,sBAAsB,EAAE,IAAI,KAAK;AAAA,IAC5C,CAAC;AACD,UAAM,eAAe,gBAAgB,IAAI,CAAC,SAAS,sBAAsB,KAAK,IAAI,KAAK,CAAC;AACxF,kCAA8B;AAAA,MAC1B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,YAAY,CAAC,YAAY;AACrB,UAAM,EAAE,QAAQ,QAAQ,IAAI,IAAI;AAChC,qCAAQ,YAAY,CAAC,SAAS,OAAO;AACrC,QAAI,EAAE,QAAQ,CAAC;AAAA,EACnB;AAAA,EACA,YAAY,CAAC,YAAY;AACrB,UAAM,EAAE,QAAQ,QAAQ,IAAI,IAAI;AAChC,qCAAQ,YAAY,CAAC,SAAS,OAAO;AACrC,QAAI,EAAE,QAAQ,CAAC;AAAA,EACnB;AAAA,EACA,oBAAoB,CAAC,oBAAoB;AAx5H7C;AAy5HQ,cAAI,EAAE,WAAN,mBAAc,gBAAgB;AAC9B,QAAI,EAAE,gBAAgB,CAAC;AAAA,EAC3B;AAAA,EACA,uBAAuB,MAAM;AACzB,UAAM,EAAE,OAAO,SAAS,IAAI,IAAI;AAChC,UAAM,QAAQ,SAAS;AACvB,UAAM,kBAAkB,MACnB,OAAO,CAAC,MAAM,EAAE,QAAQ,EACxB,IAAI,CAAC,MAAM,sBAAsB,EAAE,IAAI,KAAK,CAAC;AAClD,UAAM,kBAAkB,MACnB,OAAO,CAAC,MAAM,EAAE,QAAQ,EACxB,IAAI,CAAC,MAAM,sBAAsB,EAAE,IAAI,KAAK,CAAC;AAClD,kCAA8B;AAAA,MAC1B,cAAc;AAAA,MACd,cAAc;AAAA,MACd;AAAA,MACA;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,eAAe,CAAC,eAAe;AAC3B,UAAM,EAAE,cAAc,IAAI,IAAI;AAC9B,kBAAc,QAAQ,CAAC,SAAS;AAC5B,WAAK,mBAAmB,cAAc,KAAK,UAAU,UAAU;AAAA,IACnE,CAAC;AACD,QAAI;AAAA,MACA;AAAA,MACA,eAAe,IAAI,IAAI,aAAa;AAAA,IACxC,CAAC;AAAA,EACL;AAAA,EACA,OAAO,CAAC,UAAU;AACd,UAAM,EAAE,WAAW,OAAO,QAAQ,QAAQ,aAAa,gBAAgB,IAAI,IAAI;AAC/E,QAAI,CAAC,UAAU,CAAC,eAAgB,CAAC,MAAM,KAAK,CAAC,MAAM,GAAI;AACnD,aAAO;AAAA,IACX;AACA,UAAM,gBAAgB,SACjB,UAAU,UAAU,CAAC,IAAI,MAAM,GAAG,UAAU,CAAC,IAAI,MAAM,CAAC,EACxD,MAAM,UAAU,CAAC,CAAC;AACvB,UAAM,SAAS;AAAA,MACX,CAAC,GAAG,CAAC;AAAA,MACL,CAAC,OAAO,MAAM;AAAA,IAClB;AACA,UAAM,uBAAuB,iCAAQ,YAAY,eAAe,QAAQ;AACxE,WAAO,UAAU,aAAa,oBAAoB;AAClD,UAAM,mBAAmB,UAAU,CAAC,MAAM,qBAAqB,KAC3D,UAAU,CAAC,MAAM,qBAAqB,KACtC,UAAU,CAAC,MAAM,qBAAqB;AAC1C,WAAO;AAAA,EACX;AAAA,EACA,kBAAkB,MAAM,IAAI;AAAA,IACxB,kBAAkB,aAAa;AAAA,IAC/B,oBAAoB,aAAa;AAAA,IACjC,sBAAsB,aAAa;AAAA,IACnC,kBAAkB,aAAa;AAAA,IAC/B,uBAAuB,aAAa;AAAA,IACpC,qBAAqB,aAAa;AAAA,EACtC,CAAC;AAAA,EACD,OAAO,MAAM,IAAI,EAAE,GAAG,aAAa,CAAC;AACxC,IAAI,OAAO,EAAE;AAEb,IAAM,oBAAoB,CAAC,EAAE,SAAS,MAAM;AACxC,QAAM,eAAW,sBAAO,IAAI;AAC5B,MAAI,CAAC,SAAS,SAAS;AACnB,aAAS,UAAU,cAAc;AAAA,EACrC;AACA,SAAO,cAAAC,QAAM,cAAc,YAAY,EAAE,OAAO,SAAS,QAAQ,GAAG,QAAQ;AAChF;AACA,kBAAkB,cAAc;AAEhC,IAAM,UAAU,CAAC,EAAE,SAAS,MAAM;AAC9B,QAAM,gBAAY,0BAAW,YAAY;AACzC,MAAI,WAAW;AAGX,WAAO,cAAAA,QAAM,cAAc,cAAAA,QAAM,UAAU,MAAM,QAAQ;AAAA,EAC7D;AACA,SAAO,cAAAA,QAAM,cAAc,mBAAmB,MAAM,QAAQ;AAChE;AACA,QAAQ,cAAc;AAEtB,IAAM,mBAAmB;AAAA,EACrB,OAAO;AAAA,EACP,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,OAAO;AACX;AACA,IAAM,mBAAmB;AAAA,EACrB,SAAS;AAAA,EACT,UAAU;AAAA,EACV,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,cAAc;AAClB;AACA,IAAM,iBAAiB,CAAC,GAAG,CAAC;AAC5B,IAAM,eAAe,CAAC,IAAI,EAAE;AAC5B,IAAM,sBAAsB,EAAE,GAAG,GAAG,GAAG,GAAG,MAAM,EAAE;AAClD,IAAM,eAAe;AAAA,EACjB,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,UAAU;AAAA,EACV,QAAQ;AACZ;AACA,IAAM,gBAAY,0BAAW,CAAC,EAAE,OAAO,OAAO,cAAc,cAAc,WAAW,YAAY,kBAAkB,YAAY,kBAAkB,aAAa,aAAa,QAAQ,QAAQ,aAAa,WAAW,WAAW,gBAAgB,cAAc,qBAAqB,mBAAmB,kBAAkB,iBAAiB,kBAAkB,mBAAmB,mBAAmB,iBAAiB,YAAY,gBAAgB,eAAe,eAAe,mBAAmB,sBAAsB,iBAAiB,qBAAqB,wBAAwB,kBAAkB,gBAAgB,iBAAiB,eAAe,QAAQ,qBAAqB,mBAAmB,QAAQ,qBAAqB,yBAAyB,8BAA8B,gBAAgB,aAAa,mBAAmB,SAAS,kBAAkB,OAAO,gBAAgB,cAAc,MAAM,uBAAuB,SAAS,wBAAwB,QAAQ,IAAI,SAAS,WAAW,wBAAwB,QAAQ,IAAI,SAAS,WAAW,aAAa,OAAO,WAAW,cAAc,4BAA4B,OAAO,oBAAoB,MAAM,gBAAgB,kBAAkB,gBAAgB,aAAa,gBAAgB,gBAAgB,gBAAgB,oBAAoB,kBAAkB,qBAAqB,UAAU,KAAK,UAAU,GAAG,kBAAkB,gBAAgB,mBAAmB,MAAM,YAAY,qBAAqB,WAAW,eAAe,MAAM,cAAc,MAAM,cAAc,OAAO,mBAAmB,KAAK,kBAAkB,gBAAgB,MAAM,oBAAoB,MAAM,YAAY,MAAM,aAAa,kBAAkB,iBAAiB,kBAAkB,cAAc,mBAAmB,UAAU,mBAAmB,mBAAmB,kBAAkB,iBAAiB,kBAAkB,cAAc,mBAAmB,iBAAiB,aAAa,kBAAkB,gBAAgB,kBAAkB,IAAI,oBAAoB,IAAI,eAAe,eAAe,kBAAkB,UAAU,mBAAmB,WAAW,iBAAiB,SAAS,SAAAa,WAAU,OAAO,gBAAgB,iBAAiB,MAAM,qBAAqB,YAAY,oBAAoB,uBAAuB,MAAM,uBAAuB,OAAO,sBAAsB,OAAO,mBAAmB,MAAM,oBAAoB,MAAM,mBAAmB,IAAI,mBAAmB,SAAS,OAAAd,QAAO,IAAI,mBAAmB,GAAG,KAAK,GAAG,QAAQ;AAC/1E,QAAM,OAAO,MAAM;AACnB,SAAQ,cAAAC,QAAM;AAAA,IAAc;AAAA,IAAO,EAAE,GAAG,MAAM,OAAO,EAAE,GAAGD,QAAO,GAAG,aAAa,GAAG,KAAU,WAAW,GAAG,CAAC,cAAc,SAAS,CAAC,GAAG,eAAe,eAAe,GAAO;AAAA,IACzK,cAAAC,QAAM;AAAA,MAAc;AAAA,MAAS;AAAA,MACzB,cAAAA,QAAM,cAAc,aAAa,EAAE,QAAgB,QAAgB,aAA0B,WAAsB,aAA0B,aAA0B,kBAAoC,iBAAkC,kBAAoC,mBAAsC,mBAAsC,WAAsB,WAAsB,oBAAwC,qBAA0C,yBAAkD,8BAA4D,kBAAoC,iBAAkC,eAA8B,eAA8B,uBAA8C,sBAA4C,uBAA8C,2BAAsD,mBAAsC,iBAAkC,iBAAkC,SAAkB,SAAkB,kBAAoC,cAA4B,aAA0B,mBAAsC,aAA0B,kBAAoC,iBAAkC,WAAsB,aAA0B,kBAAoC,iBAAkC,kBAAoC,cAA4B,mBAAsC,wBAAgD,kBAAoC,gBAAgC,mBAAsC,mBAAsC,kBAAoC,iBAAkC,kBAAoC,aAAa,eAAe,cAAc,kBAAkB,oBAAoB,mBAAmB,gBAAgB,kBAAkB,iBAAiB,iBAAiB,mBAAmB,mBAAmB,oBAAwC,iBAAkC,kBAAoC,gBAAgC,sBAA4C,MAAY,qBAA0C,YAAwB,WAAuB,CAAC;AAAA,MAC1uE,cAAAA,QAAM,cAAc,cAAc,EAAE,OAAc,OAAc,cAA4B,cAA4B,WAAsB,gBAAgC,cAA4B,qBAA0C,mBAAsC,gBAAgC,kBAAoC,gBAAgC,gBAAgC,gBAAgC,oBAAwC,sBAA4C,SAAkB,SAAkB,YAAwB,eAA8B,eAA8B,YAAwB,UAAoB,gBAAgC,iBAAkC,gBAAgC,oBAAwC,SAASa,UAAS,gBAAgC,eAA8B,eAA8B,iBAAkC,YAAwB,gBAAgC,iBAAkC,sBAA4C,qBAA0C,gBAAgC,YAAwB,MAAY,kBAAoC,mBAAsC,SAAkB,kBAAoC,mBAAsC,kBAAqC,CAAC;AAAA,MACh5C,cAAAb,QAAM,cAAc,WAAW,EAAE,kBAAqC,CAAC;AAAA,MACvE;AAAA,MACA,cAAAA,QAAM,cAAc,aAAa,EAAE,YAAwB,UAAU,oBAAoB,CAAC;AAAA,MAC1F,cAAAA,QAAM,cAAc,kBAAkB,EAAE,MAAY,oBAAyC,CAAC;AAAA,IAAC;AAAA,EAAC;AAC5G,CAAC;AACD,UAAU,cAAc;AAExB,IAAM,aAAa,CAAC,MAAG;AA5gIvB;AA4gI0B,iBAAE,YAAF,mBAAW,cAAc;AAAA;AACnD,SAAS,kBAAkB,EAAE,SAAS,GAAG;AACrC,QAAM,oBAAoB,SAAS,UAAU;AAC7C,MAAI,CAAC,mBAAmB;AACpB,WAAO;AAAA,EACX;AACA,aAAO,+BAAa,UAAU,iBAAiB;AACnD;AAEA,SAAS,yBAAyB;AAC9B,QAAM,QAAQ,YAAY;AAC1B,aAAO,2BAAY,CAAC,OAAO;AACvB,UAAM,EAAE,SAAS,qBAAqB,IAAI,MAAM,SAAS;AACzD,UAAM,YAAY,MAAM,QAAQ,EAAE,IAAI,KAAK,CAAC,EAAE;AAC9C,UAAM,UAAU,UAAU,OAAO,CAAC,KAAK,aAAa;AAChD,YAAM,cAAc,mCAAS,cAAc,8BAA8B,QAAQ;AACjF,UAAI,aAAa;AACb,YAAI,KAAK,EAAE,IAAI,UAAU,aAAa,aAAa,KAAK,CAAC;AAAA,MAC7D;AACA,aAAO;AAAA,IACX,GAAG,CAAC,CAAC;AACL,0BAAsB,MAAM,qBAAqB,OAAO,CAAC;AAAA,EAC7D,GAAG,CAAC,CAAC;AACT;AAEA,IAAM,gBAAgB,CAAC,UAAU,MAAM,SAAS;AAChD,SAAS,WAAW;AAChB,QAAM,QAAQ,SAAS,eAAe,SAAO;AAC7C,SAAO;AACX;AAEA,IAAM,gBAAgB,CAAC,UAAU,MAAM;AACvC,SAAS,WAAW;AAChB,QAAM,QAAQ,SAAS,eAAe,SAAO;AAC7C,SAAO;AACX;AAEA,IAAM,mBAAmB,CAAC,WAAW;AAAA,EACjC,GAAG,MAAM,UAAU,CAAC;AAAA,EACpB,GAAG,MAAM,UAAU,CAAC;AAAA,EACpB,MAAM,MAAM,UAAU,CAAC;AAC3B;AACA,SAAS,cAAc;AACnB,QAAM,WAAW,SAAS,kBAAkB,SAAO;AACnD,SAAO;AACX;AAGA,SAAS,oBAAoBsB,eAAc;AACvC,SAAO,CAAC,iBAAiB;AACrB,UAAM,CAAC,OAAO,QAAQ,QAAI,wBAAS,YAAY;AAC/C,UAAM,oBAAgB,2BAAY,CAAC,YAAY,SAAS,CAACC,WAAUD,cAAa,SAASC,MAAK,CAAC,GAAG,CAAC,CAAC;AACpG,WAAO,CAAC,OAAO,UAAU,aAAa;AAAA,EAC1C;AACJ;AACA,IAAM,gBAAgB,oBAAoB,gBAAgB;AAC1D,IAAM,gBAAgB,oBAAoB,gBAAgB;AAE1D,SAAS,oBAAoB,EAAE,SAAS,UAAU,MAAM,GAAG;AACvD,QAAM,QAAQ,YAAY;AAC1B,+BAAU,MAAM;AACZ,UAAM,SAAS,EAAE,uBAAuB,QAAQ,CAAC;AAAA,EACrD,GAAG,CAAC,OAAO,CAAC;AACZ,+BAAU,MAAM;AACZ,UAAM,SAAS,EAAE,kBAAkB,SAAS,CAAC;AAAA,EACjD,GAAG,CAAC,QAAQ,CAAC;AACb,+BAAU,MAAM;AACZ,UAAM,SAAS,EAAE,qBAAqB,MAAM,CAAC;AAAA,EACjD,GAAG,CAAC,KAAK,CAAC;AACd;AAEA,SAAS,qBAAqB,EAAE,SAAS,GAAG;AACxC,QAAM,QAAQ,YAAY;AAC1B,+BAAU,MAAM;AACZ,UAAM,8BAA8B,CAAC,GAAG,MAAM,SAAS,EAAE,mBAAmB,QAAQ;AACpF,UAAM,SAAS,EAAE,mBAAmB,4BAA4B,CAAC;AACjE,WAAO,MAAM;AACT,YAAM,eAAe,MAAM,SAAS,EAAE,kBAAkB,OAAO,CAAC,OAAO,OAAO,QAAQ;AACtF,YAAM,SAAS,EAAE,mBAAmB,aAAa,CAAC;AAAA,IACtD;AAAA,EACJ,GAAG,CAAC,QAAQ,CAAC;AACjB;AAEA,IAAM,WAAW,CAAC,YAAY,CAAC,MAAM;AACjC,MAAI,EAAE,cAAc,SAAS,GAAG;AAC5B,WAAO;AAAA,EACX;AACA,SAAO,EACF,SAAS,EACT,OAAO,CAAC,MAAO,QAAQ,qBAAqB,OAAO,CAAC,EAAE,MAAO,EAC7D,MAAM,CAAC,MAAG;AAtmInB;AAsmIsB,oBAAE,eAAe,MAAjB,mBAAoB,kBAAiB;AAAA,GAAS;AACpE;AACA,IAAM,iBAAiB;AAAA,EACnB,oBAAoB;AACxB;AACA,SAAS,oBAAoB,UAAU,gBAAgB;AACnD,QAAM,cAAc,SAAS,SAAS,OAAO,CAAC;AAC9C,SAAO;AACX;;;AK9mIA,IAAAC,gBAA+C;;;ACA/C,SAASC,WAAU,MAAM,MAAM;AAC7B,MAAI,OAAO,GAAG,MAAM,IAAI,GAAG;AACzB,WAAO;AAAA,EACT;AACA,MAAI,OAAO,SAAS,YAAY,SAAS,QAAQ,OAAO,SAAS,YAAY,SAAS,MAAM;AAC1F,WAAO;AAAA,EACT;AACA,MAAI,gBAAgB,OAAO,gBAAgB,KAAK;AAC9C,QAAI,KAAK,SAAS,KAAK,KAAM,QAAO;AACpC,eAAW,CAAC,KAAK,KAAK,KAAK,MAAM;AAC/B,UAAI,CAAC,OAAO,GAAG,OAAO,KAAK,IAAI,GAAG,CAAC,GAAG;AACpC,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,MAAI,gBAAgB,OAAO,gBAAgB,KAAK;AAC9C,QAAI,KAAK,SAAS,KAAK,KAAM,QAAO;AACpC,eAAW,SAAS,MAAM;AACxB,UAAI,CAAC,KAAK,IAAI,KAAK,GAAG;AACpB,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,QAAM,QAAQ,OAAO,KAAK,IAAI;AAC9B,MAAI,MAAM,WAAW,OAAO,KAAK,IAAI,EAAE,QAAQ;AAC7C,WAAO;AAAA,EACT;AACA,aAAW,QAAQ,OAAO;AACxB,QAAI,CAAC,OAAO,UAAU,eAAe,KAAK,MAAM,IAAI,KAAK,CAAC,OAAO,GAAG,KAAK,IAAI,GAAG,KAAK,IAAI,CAAC,GAAG;AAC3F,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;;;AD5BA,IAAM,cAAc,CAAC,EAAE,IAAI,GAAG,GAAG,OAAO,QAAQ,OAAAC,QAAO,OAAO,aAAa,aAAa,WAAW,cAAc,gBAAgB,SAAS,SAAU,MAAM;AACtJ,QAAM,EAAE,YAAY,gBAAgB,IAAIA,UAAS,CAAC;AAClD,QAAM,OAAQ,SAAS,cAAc;AACrC,SAAQ,cAAAC,QAAM,cAAc,QAAQ,EAAE,WAAW,GAAG,CAAC,4BAA4B,EAAE,SAAS,GAAG,SAAS,CAAC,GAAG,GAAM,GAAM,IAAI,cAAc,IAAI,cAAc,OAAc,QAAgB,MAAY,QAAQ,aAAa,aAA0B,gBAAgC,SAAS,UAAU,CAAC,UAAU,QAAQ,OAAO,EAAE,IAAI,OAAU,CAAC;AACvV;AACA,YAAY,cAAc;AAC1B,IAAI,oBAAgB,oBAAK,WAAW;AAGpC,IAAMC,cAAa,CAAC,MAAM,EAAE;AAC5B,IAAM,gBAAgB,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,CAAC,SAAS,CAAC,KAAK,UAAU,KAAK,SAAS,KAAK,MAAM;AACpG,IAAM,kBAAkB,CAAC,SAAU,gBAAgB,WAAW,OAAO,MAAM;AAC3E,SAAS,aAAa;AAAA,EAAE,kBAAkB;AAAA,EAAe,YAAY;AAAA,EAAW,gBAAgB;AAAA,EAAI,mBAAmB;AAAA,EAAG,kBAAkB;AAAA;AAAA;AAAA,EAG5I,eAAe,gBAAgB;AAAA,EAAe;AAAS,GAAG;AACtD,QAAM,QAAQ,SAAS,eAAeC,UAAO;AAC7C,QAAM,aAAa,SAASD,WAAU;AACtC,QAAM,gBAAgB,gBAAgB,SAAS;AAC/C,QAAM,sBAAsB,gBAAgB,eAAe;AAC3D,QAAM,oBAAoB,gBAAgB,aAAa;AACvD,QAAM,iBAAiB,OAAO,WAAW,eAAe,CAAC,CAAC,OAAO,SAAS,eAAe;AACzF,SAAQ,cAAAD,QAAM,cAAc,cAAAA,QAAM,UAAU,MAAM,MAAM,IAAI,CAAC,SAAS;AAClE,UAAM,EAAE,GAAG,EAAE,IAAI,0BAA0B,MAAM,UAAU,EAAE;AAC7D,WAAQ,cAAAA,QAAM,cAAc,eAAe,EAAE,KAAK,KAAK,IAAI,GAAM,GAAM,OAAO,KAAK,OAAO,QAAQ,KAAK,QAAQ,OAAO,KAAK,OAAO,UAAU,KAAK,UAAU,WAAW,kBAAkB,IAAI,GAAG,OAAO,cAAc,IAAI,GAAG,cAAc,kBAAkB,aAAa,oBAAoB,IAAI,GAAG,aAAa,iBAAiB,gBAAgC,SAAkB,IAAI,KAAK,GAAG,CAAC;AAAA,EACpY,CAAC,CAAC;AACN;AACA,IAAI,qBAAiB,oBAAK,YAAY;AAGtC,IAAM,eAAe;AACrB,IAAM,gBAAgB;AACtB,IAAMG,YAAW,CAAC,MAAM;AACpB,QAAM,QAAQ,EAAE,SAAS;AACzB,QAAM,SAAS;AAAA,IACX,GAAG,CAAC,EAAE,UAAU,CAAC,IAAI,EAAE,UAAU,CAAC;AAAA,IAClC,GAAG,CAAC,EAAE,UAAU,CAAC,IAAI,EAAE,UAAU,CAAC;AAAA,IAClC,OAAO,EAAE,QAAQ,EAAE,UAAU,CAAC;AAAA,IAC9B,QAAQ,EAAE,SAAS,EAAE,UAAU,CAAC;AAAA,EACpC;AACA,SAAO;AAAA,IACH;AAAA,IACA,cAAc,MAAM,SAAS,IAAI,iBAAiB,eAAe,OAAO,EAAE,UAAU,GAAG,MAAM,IAAI;AAAA,IACjG,MAAM,EAAE;AAAA,EACZ;AACJ;AACA,IAAM,iBAAiB;AACvB,SAAS,QAAQ;AAAA,EAAE,OAAAJ;AAAA,EAAO;AAAA,EAAW,kBAAkB;AAAA,EAAe,YAAY;AAAA,EAAW,gBAAgB;AAAA,EAAI,mBAAmB;AAAA,EAAG,kBAAkB;AAAA;AAAA;AAAA,EAGzJ;AAAA,EAAe,YAAY;AAAA,EAA2B,kBAAkB;AAAA,EAAQ,kBAAkB;AAAA,EAAG,WAAW;AAAA,EAAgB;AAAA,EAAS;AAAA,EAAa,WAAW;AAAA,EAAO,WAAW;AAAA,EAAO,YAAY;AAAA,EAAuB,aAAa;AAAA,EAAO,WAAW;AAAA,EAAI,cAAc;AAAG,GAAG;AAChR,QAAM,QAAQ,YAAY;AAC1B,QAAM,UAAM,sBAAO,IAAI;AACvB,QAAM,EAAE,cAAc,QAAQ,KAAK,IAAI,SAASI,WAAUD,UAAO;AACjE,QAAM,gBAAeH,UAAA,gBAAAA,OAAO,UAAS;AACrC,QAAM,iBAAgBA,UAAA,gBAAAA,OAAO,WAAU;AACvC,QAAM,cAAc,aAAa,QAAQ;AACzC,QAAM,eAAe,aAAa,SAAS;AAC3C,QAAM,YAAY,KAAK,IAAI,aAAa,YAAY;AACpD,QAAM,YAAY,YAAY;AAC9B,QAAM,aAAa,YAAY;AAC/B,QAAM,SAAS,cAAc;AAC7B,QAAM,IAAI,aAAa,KAAK,YAAY,aAAa,SAAS,IAAI;AAClE,QAAM,IAAI,aAAa,KAAK,aAAa,aAAa,UAAU,IAAI;AACpE,QAAM,QAAQ,YAAY,SAAS;AACnC,QAAM,SAAS,aAAa,SAAS;AACrC,QAAM,aAAa,GAAG,cAAc,IAAI,IAAI;AAC5C,QAAM,mBAAe,sBAAO,CAAC;AAC7B,eAAa,UAAU;AACvB,+BAAU,MAAM;AACZ,QAAI,IAAI,SAAS;AACb,YAAM,YAAY,eAAO,IAAI,OAAO;AACpC,YAAM,cAAc,CAAC,UAAU;AAC3B,cAAM,EAAE,WAAW,aAAa,OAAO,IAAI,MAAM,SAAS;AAC1D,YAAI,MAAM,YAAY,SAAS,WAAW,CAAC,eAAe,CAAC,QAAQ;AAC/D;AAAA,QACJ;AACA,cAAM,aAAa,CAAC,MAAM,YAAY,UACjC,MAAM,YAAY,cAAc,IAAI,OAAO,MAAM,YAAY,YAAY,IAAI,QAC9E;AACJ,cAAM,OAAO,UAAU,CAAC,IAAI,KAAK,IAAI,GAAG,UAAU;AAClD,eAAO,QAAQ,aAAa,IAAI;AAAA,MACpC;AACA,YAAM,aAAa,CAAC,UAAU;AAC1B,cAAM,EAAE,WAAW,aAAa,QAAQ,iBAAiB,OAAAK,QAAO,QAAAC,QAAO,IAAI,MAAM,SAAS;AAC1F,YAAI,MAAM,YAAY,SAAS,eAAe,CAAC,eAAe,CAAC,QAAQ;AACnE;AAAA,QACJ;AAEA,cAAM,YAAY,aAAa,UAAU,KAAK,IAAI,GAAG,UAAU,CAAC,CAAC,KAAK,aAAa,KAAK;AACxF,cAAMC,YAAW;AAAA,UACb,GAAG,UAAU,CAAC,IAAI,MAAM,YAAY,YAAY;AAAA,UAChD,GAAG,UAAU,CAAC,IAAI,MAAM,YAAY,YAAY;AAAA,QACpD;AACA,cAAM,SAAS;AAAA,UACX,CAAC,GAAG,CAAC;AAAA,UACL,CAACF,QAAOC,OAAM;AAAA,QAClB;AACA,cAAM,gBAAgB,SAAa,UAAUC,UAAS,GAAGA,UAAS,CAAC,EAAE,MAAM,UAAU,CAAC,CAAC;AACvF,cAAM,uBAAuB,OAAO,UAAU,EAAE,eAAe,QAAQ,eAAe;AACtF,eAAO,UAAU,aAAa,oBAAoB;AAAA,MACtD;AACA,YAAM,oBAAoB,aAAK,EAE1B,GAAG,QAAQ,WAAW,aAAa,IAAI,EAEvC,GAAG,cAAc,WAAW,cAAc,IAAI;AACnD,gBAAU,KAAK,iBAAiB;AAChC,aAAO,MAAM;AACT,kBAAU,GAAG,QAAQ,IAAI;AAAA,MAC7B;AAAA,IACJ;AAAA,EACJ,GAAG,CAAC,UAAU,UAAU,YAAY,QAAQ,CAAC;AAC7C,QAAM,aAAa,UACb,CAAC,UAAU;AACT,UAAM,UAAU,gBAAQ,KAAK;AAC7B,YAAQ,OAAO,EAAE,GAAG,QAAQ,CAAC,GAAG,GAAG,QAAQ,CAAC,EAAE,CAAC;AAAA,EACnD,IACE;AACN,QAAM,iBAAiB,cACjB,CAAC,OAAO,WAAW;AACjB,UAAM,OAAO,MAAM,SAAS,EAAE,cAAc,IAAI,MAAM;AACtD,gBAAY,OAAO,IAAI;AAAA,EAC3B,IACE;AACN,SAAQ,cAAAN,QAAM;AAAA,IAAc;AAAA,IAAO,EAAE,UAAoB,OAAOD,QAAO,WAAW,GAAG,CAAC,uBAAuB,SAAS,CAAC,GAAG,eAAe,cAAc;AAAA,IACnJ,cAAAC,QAAM;AAAA,MAAc;AAAA,MAAO,EAAE,OAAO,cAAc,QAAQ,eAAe,SAAS,GAAG,CAAC,IAAI,CAAC,IAAI,KAAK,IAAI,MAAM,IAAI,MAAM,OAAO,mBAAmB,YAAY,KAAK,KAAK,SAAS,WAAW;AAAA,MACxL,aAAa,cAAAA,QAAM,cAAc,SAAS,EAAE,IAAI,WAAW,GAAG,SAAS;AAAA,MACvE,cAAAA,QAAM,cAAc,gBAAgB,EAAE,SAAS,gBAAgB,WAAsB,iBAAkC,kBAAoC,eAA8B,iBAAkC,cAA6B,CAAC;AAAA,MACzP,cAAAA,QAAM,cAAc,QAAQ,EAAE,WAAW,4BAA4B,GAAG,IAAI,IAAI,MAAM,IAAI,IAAI,MAAM,IAAI,QAAQ,SAAS,CAAC,IAAI,SAAS,SAAS,CAAC,IAAI,CAAC,QAAQ,SAAS,CAAC;AAAA,WACzK,OAAO,CAAC,IAAI,OAAO,CAAC,IAAI,OAAO,KAAK,IAAI,OAAO,MAAM,IAAI,CAAC,OAAO,KAAK,KAAK,MAAM,WAAW,UAAU,WAAW,QAAQ,iBAAiB,aAAa,iBAAiB,eAAe,OAAO,CAAC;AAAA,IAAC;AAAA,EAAC;AAC5M;AACA,QAAQ,cAAc;AACtB,IAAI,gBAAY,oBAAK,OAAO;;;AE5I5B,IAAAO,gBAAiD;;;ACAjD,SAASC,WAAU,MAAM,MAAM;AAC7B,MAAI,OAAO,GAAG,MAAM,IAAI,GAAG;AACzB,WAAO;AAAA,EACT;AACA,MAAI,OAAO,SAAS,YAAY,SAAS,QAAQ,OAAO,SAAS,YAAY,SAAS,MAAM;AAC1F,WAAO;AAAA,EACT;AACA,MAAI,gBAAgB,OAAO,gBAAgB,KAAK;AAC9C,QAAI,KAAK,SAAS,KAAK,KAAM,QAAO;AACpC,eAAW,CAAC,KAAK,KAAK,KAAK,MAAM;AAC/B,UAAI,CAAC,OAAO,GAAG,OAAO,KAAK,IAAI,GAAG,CAAC,GAAG;AACpC,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,MAAI,gBAAgB,OAAO,gBAAgB,KAAK;AAC9C,QAAI,KAAK,SAAS,KAAK,KAAM,QAAO;AACpC,eAAW,SAAS,MAAM;AACxB,UAAI,CAAC,KAAK,IAAI,KAAK,GAAG;AACpB,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,QAAM,QAAQ,OAAO,KAAK,IAAI;AAC9B,MAAI,MAAM,WAAW,OAAO,KAAK,IAAI,EAAE,QAAQ;AAC7C,WAAO;AAAA,EACT;AACA,aAAW,QAAQ,OAAO;AACxB,QAAI,CAAC,OAAO,UAAU,eAAe,KAAK,MAAM,IAAI,KAAK,CAAC,OAAO,GAAG,KAAK,IAAI,GAAG,KAAK,IAAI,CAAC,GAAG;AAC3F,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;;;AD9BA,SAAS,WAAW;AAChB,SAAQ,cAAAC,QAAM;AAAA,IAAc;AAAA,IAAO,EAAE,OAAO,8BAA8B,SAAS,YAAY;AAAA,IAC3F,cAAAA,QAAM,cAAc,QAAQ,EAAE,GAAG,wEAAwE,CAAC;AAAA,EAAC;AACnH;AAEA,SAAS,YAAY;AACjB,SAAQ,cAAAA,QAAM;AAAA,IAAc;AAAA,IAAO,EAAE,OAAO,8BAA8B,SAAS,WAAW;AAAA,IAC1F,cAAAA,QAAM,cAAc,QAAQ,EAAE,GAAG,iBAAiB,CAAC;AAAA,EAAC;AAC5D;AAEA,SAAS,cAAc;AACnB,SAAQ,cAAAA,QAAM;AAAA,IAAc;AAAA,IAAO,EAAE,OAAO,8BAA8B,SAAS,YAAY;AAAA,IAC3F,cAAAA,QAAM,cAAc,QAAQ,EAAE,GAAG,8XAA8X,CAAC;AAAA,EAAC;AACza;AAEA,SAAS,WAAW;AAChB,SAAQ,cAAAA,QAAM;AAAA,IAAc;AAAA,IAAO,EAAE,OAAO,8BAA8B,SAAS,YAAY;AAAA,IAC3F,cAAAA,QAAM,cAAc,QAAQ,EAAE,GAAG,icAAic,CAAC;AAAA,EAAC;AAC5e;AAEA,SAAS,aAAa;AAClB,SAAQ,cAAAA,QAAM;AAAA,IAAc;AAAA,IAAO,EAAE,OAAO,8BAA8B,SAAS,YAAY;AAAA,IAC3F,cAAAA,QAAM,cAAc,QAAQ,EAAE,GAAG,uYAAuY,CAAC;AAAA,EAAC;AAClb;AAEA,IAAM,gBAAgB,CAAC,EAAE,UAAU,WAAW,GAAG,KAAK,MAAO,cAAAA,QAAM,cAAc,UAAU,EAAE,MAAM,UAAU,WAAW,GAAG,CAAC,+BAA+B,SAAS,CAAC,GAAG,GAAG,KAAK,GAAG,QAAQ;AAC3L,cAAc,cAAc;AAE5B,IAAMC,YAAW,CAAC,OAAO;AAAA,EACrB,eAAe,EAAE,kBAAkB,EAAE,oBAAoB,EAAE;AAAA,EAC3D,gBAAgB,EAAE,UAAU,CAAC,KAAK,EAAE;AAAA,EACpC,gBAAgB,EAAE,UAAU,CAAC,KAAK,EAAE;AACxC;AACA,IAAM,WAAW,CAAC,EAAE,OAAAC,QAAO,WAAW,MAAM,cAAc,MAAM,kBAAkB,MAAM,gBAAgB,UAAU,WAAW,WAAW,qBAAqB,WAAW,UAAU,WAAW,cAAe,MAAM;AAC9M,QAAM,QAAQ,YAAY;AAC1B,QAAM,CAAC,WAAW,YAAY,QAAI,wBAAS,KAAK;AAChD,QAAM,EAAE,eAAe,gBAAgB,eAAe,IAAI,SAASD,WAAUE,UAAO;AACpF,QAAM,EAAE,QAAQ,SAAS,SAAAC,SAAQ,IAAI,aAAa;AAClD,+BAAU,MAAM;AACZ,iBAAa,IAAI;AAAA,EACrB,GAAG,CAAC,CAAC;AACL,MAAI,CAAC,WAAW;AACZ,WAAO;AAAA,EACX;AACA,QAAM,kBAAkB,MAAM;AAC1B,WAAO;AACP;AAAA,EACJ;AACA,QAAM,mBAAmB,MAAM;AAC3B,YAAQ;AACR;AAAA,EACJ;AACA,QAAM,mBAAmB,MAAM;AAC3B,IAAAA,SAAQ,cAAc;AACtB;AAAA,EACJ;AACA,QAAM,wBAAwB,MAAM;AAChC,UAAM,SAAS;AAAA,MACX,gBAAgB,CAAC;AAAA,MACjB,kBAAkB,CAAC;AAAA,MACnB,oBAAoB,CAAC;AAAA,IACzB,CAAC;AACD,+DAAsB,CAAC;AAAA,EAC3B;AACA,SAAQ,cAAAJ,QAAM;AAAA,IAAc;AAAA,IAAO,EAAE,WAAW,GAAG,CAAC,wBAAwB,SAAS,CAAC,GAAG,UAAoB,OAAOE,QAAO,eAAe,eAAe;AAAA,IACrJ,YAAa,cAAAF,QAAM;AAAA,MAAc,cAAAA,QAAM;AAAA,MAAU;AAAA,MAC7C,cAAAA,QAAM;AAAA,QAAc;AAAA,QAAe,EAAE,SAAS,iBAAiB,WAAW,+BAA+B,OAAO,WAAW,cAAc,WAAW,UAAU,eAAe;AAAA,QACzK,cAAAA,QAAM,cAAc,UAAU,IAAI;AAAA,MAAC;AAAA,MACvC,cAAAA,QAAM;AAAA,QAAc;AAAA,QAAe,EAAE,SAAS,kBAAkB,WAAW,gCAAgC,OAAO,YAAY,cAAc,YAAY,UAAU,eAAe;AAAA,QAC7K,cAAAA,QAAM,cAAc,WAAW,IAAI;AAAA,MAAC;AAAA,IAAC;AAAA,IAC7C,eAAgB,cAAAA,QAAM;AAAA,MAAc;AAAA,MAAe,EAAE,WAAW,gCAAgC,SAAS,kBAAkB,OAAO,YAAY,cAAc,WAAW;AAAA,MACnK,cAAAA,QAAM,cAAc,aAAa,IAAI;AAAA,IAAC;AAAA,IAC1C,mBAAoB,cAAAA,QAAM,cAAc,eAAe,EAAE,WAAW,oCAAoC,SAAS,uBAAuB,OAAO,wBAAwB,cAAc,uBAAuB,GAAG,gBAAgB,cAAAA,QAAM,cAAc,YAAY,IAAI,IAAI,cAAAA,QAAM,cAAc,UAAU,IAAI,CAAC;AAAA,IAC1S;AAAA,EAAQ;AAChB;AACA,SAAS,cAAc;AACvB,IAAI,iBAAa,oBAAK,QAAQ;;;AEjF9B,IAAAK,gBAAoC;;;ACApC,SAASC,WAAU,MAAM,MAAM;AAC7B,MAAI,OAAO,GAAG,MAAM,IAAI,GAAG;AACzB,WAAO;AAAA,EACT;AACA,MAAI,OAAO,SAAS,YAAY,SAAS,QAAQ,OAAO,SAAS,YAAY,SAAS,MAAM;AAC1F,WAAO;AAAA,EACT;AACA,MAAI,gBAAgB,OAAO,gBAAgB,KAAK;AAC9C,QAAI,KAAK,SAAS,KAAK,KAAM,QAAO;AACpC,eAAW,CAAC,KAAK,KAAK,KAAK,MAAM;AAC/B,UAAI,CAAC,OAAO,GAAG,OAAO,KAAK,IAAI,GAAG,CAAC,GAAG;AACpC,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,MAAI,gBAAgB,OAAO,gBAAgB,KAAK;AAC9C,QAAI,KAAK,SAAS,KAAK,KAAM,QAAO;AACpC,eAAW,SAAS,MAAM;AACxB,UAAI,CAAC,KAAK,IAAI,KAAK,GAAG;AACpB,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,QAAM,QAAQ,OAAO,KAAK,IAAI;AAC9B,MAAI,MAAM,WAAW,OAAO,KAAK,IAAI,EAAE,QAAQ;AAC7C,WAAO;AAAA,EACT;AACA,aAAW,QAAQ,OAAO;AACxB,QAAI,CAAC,OAAO,UAAU,eAAe,KAAK,MAAM,IAAI,KAAK,CAAC,OAAO,GAAG,KAAK,IAAI,GAAG,KAAK,IAAI,CAAC,GAAG;AAC3F,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;;;AD9BA,IAAI;AAAA,CACH,SAAUC,oBAAmB;AAC1B,EAAAA,mBAAkB,OAAO,IAAI;AAC7B,EAAAA,mBAAkB,MAAM,IAAI;AAC5B,EAAAA,mBAAkB,OAAO,IAAI;AACjC,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAEhD,SAAS,YAAY,EAAE,OAAO,YAAY,UAAU,GAAG;AACnD,SAAQ,cAAAC,QAAM,cAAc,QAAQ,EAAE,QAAQ,OAAO,aAAa,WAAW,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,OAAO,WAAW,CAAC,CAAC,OAAO,WAAW,CAAC,IAAI,CAAC,KAAK,WAAW,CAAC,CAAC,GAAG,CAAC;AAC3K;AACA,SAAS,WAAW,EAAE,OAAO,OAAO,GAAG;AACnC,SAAO,cAAAA,QAAM,cAAc,UAAU,EAAE,IAAI,QAAQ,IAAI,QAAQ,GAAG,QAAQ,MAAM,MAAM,CAAC;AAC3F;AAEA,IAAM,eAAe;AAAA,EACjB,CAAC,kBAAkB,IAAI,GAAG;AAAA,EAC1B,CAAC,kBAAkB,KAAK,GAAG;AAAA,EAC3B,CAAC,kBAAkB,KAAK,GAAG;AAC/B;AACA,IAAM,cAAc;AAAA,EAChB,CAAC,kBAAkB,IAAI,GAAG;AAAA,EAC1B,CAAC,kBAAkB,KAAK,GAAG;AAAA,EAC3B,CAAC,kBAAkB,KAAK,GAAG;AAC/B;AACA,IAAMC,YAAW,CAAC,OAAO,EAAE,WAAW,EAAE,WAAW,WAAW,WAAW,EAAE,IAAI,GAAG;AAClF,SAAS,WAAW;AAAA,EAAE;AAAA,EAAI,UAAU,kBAAkB;AAAA;AAAA,EAEtD,MAAM;AAAA;AAAA,EAEN;AAAA,EAAM,YAAY;AAAA,EAAG,SAAS;AAAA,EAAG;AAAA,EAAO,OAAAC;AAAA,EAAO;AAAW,GAAG;AACzD,QAAM,UAAM,sBAAO,IAAI;AACvB,QAAM,EAAE,WAAW,UAAU,IAAI,SAASD,WAAUE,UAAO;AAC3D,QAAM,eAAe,SAAS,aAAa,OAAO;AAClD,QAAM,cAAc,QAAQ,YAAY,OAAO;AAC/C,QAAM,SAAS,YAAY,kBAAkB;AAC7C,QAAM,UAAU,YAAY,kBAAkB;AAC9C,QAAM,QAAQ,MAAM,QAAQ,GAAG,IAAI,MAAM,CAAC,KAAK,GAAG;AAClD,QAAM,YAAY,CAAC,MAAM,CAAC,IAAI,UAAU,CAAC,KAAK,GAAG,MAAM,CAAC,IAAI,UAAU,CAAC,KAAK,CAAC;AAC7E,QAAM,aAAa,cAAc,UAAU,CAAC;AAC5C,QAAM,oBAAoB,UAAU,CAAC,YAAY,UAAU,IAAI;AAC/D,QAAM,gBAAgB,SAChB,CAAC,aAAa,QAAQ,aAAa,MAAM,IACzC,CAAC,kBAAkB,CAAC,IAAI,QAAQ,kBAAkB,CAAC,IAAI,MAAM;AACnE,SAAQ,cAAAH,QAAM;AAAA,IAAc;AAAA,IAAO,EAAE,WAAW,GAAG,CAAC,0BAA0B,SAAS,CAAC,GAAG,OAAO;AAAA,MAC1F,GAAGE;AAAA,MACH,UAAU;AAAA,MACV,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,MAAM;AAAA,IACV,GAAG,KAAU,eAAe,iBAAiB;AAAA,IAC7C,cAAAF,QAAM,cAAc,WAAW,EAAE,IAAI,YAAY,IAAI,GAAG,UAAU,CAAC,IAAI,UAAU,CAAC,GAAG,GAAG,UAAU,CAAC,IAAI,UAAU,CAAC,GAAG,OAAO,UAAU,CAAC,GAAG,QAAQ,UAAU,CAAC,GAAG,cAAc,kBAAkB,kBAAkB,cAAc,cAAc,CAAC,CAAC,KAAK,cAAc,CAAC,CAAC,IAAI,GAAG,SAAU,cAAAA,QAAM,cAAc,YAAY,EAAE,OAAO,cAAc,QAAQ,aAAa,OAAO,CAAC,IAAM,cAAAA,QAAM,cAAc,aAAa,EAAE,YAAY,mBAAmB,OAAO,cAAc,UAAqB,CAAC,CAAE;AAAA,IAC/d,cAAAA,QAAM,cAAc,QAAQ,EAAE,GAAG,KAAK,GAAG,KAAK,OAAO,QAAQ,QAAQ,QAAQ,MAAM,QAAQ,YAAY,EAAE,IAAI,CAAC;AAAA,EAAC;AACvH;AACA,WAAW,cAAc;AACzB,IAAI,mBAAe,oBAAK,UAAU;;;AE5DlC,IAAAI,gBAAmC;;;ACAnC,SAASC,WAAU,MAAM,MAAM;AAC7B,MAAI,OAAO,GAAG,MAAM,IAAI,GAAG;AACzB,WAAO;AAAA,EACT;AACA,MAAI,OAAO,SAAS,YAAY,SAAS,QAAQ,OAAO,SAAS,YAAY,SAAS,MAAM;AAC1F,WAAO;AAAA,EACT;AACA,MAAI,gBAAgB,OAAO,gBAAgB,KAAK;AAC9C,QAAI,KAAK,SAAS,KAAK,KAAM,QAAO;AACpC,eAAW,CAAC,KAAK,KAAK,KAAK,MAAM;AAC/B,UAAI,CAAC,OAAO,GAAG,OAAO,KAAK,IAAI,GAAG,CAAC,GAAG;AACpC,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,MAAI,gBAAgB,OAAO,gBAAgB,KAAK;AAC9C,QAAI,KAAK,SAAS,KAAK,KAAM,QAAO;AACpC,eAAW,SAAS,MAAM;AACxB,UAAI,CAAC,KAAK,IAAI,KAAK,GAAG;AACpB,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,QAAM,QAAQ,OAAO,KAAK,IAAI;AAC9B,MAAI,MAAM,WAAW,OAAO,KAAK,IAAI,EAAE,QAAQ;AAC7C,WAAO;AAAA,EACT;AACA,aAAW,QAAQ,OAAO;AACxB,QAAI,CAAC,OAAO,UAAU,eAAe,KAAK,MAAM,IAAI,KAAK,CAAC,OAAO,GAAG,KAAK,IAAI,GAAG,KAAK,IAAI,CAAC,GAAG;AAC3F,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;;;AD/BA,IAAAC,oBAA6B;AAE7B,IAAMC,YAAW,CAAC,UAAO;AANzB;AAM4B,qBAAM,YAAN,mBAAe,cAAc;AAAA;AACzD,SAAS,kBAAkB,EAAE,SAAS,GAAG;AACrC,QAAM,aAAa,SAASA,SAAQ;AACpC,MAAI,CAAC,YAAY;AACb,WAAO;AAAA,EACX;AACA,aAAO,gCAAa,UAAU,UAAU;AAC5C;AAEA,IAAM,iBAAiB,CAAC,GAAG,MAAG;AAf9B;AAeiC,uCAAG,qBAAH,mBAAqB,SAAM,4BAAG,qBAAH,mBAAqB,QAC7E,4BAAG,qBAAH,mBAAqB,SAAM,4BAAG,qBAAH,mBAAqB,OAChD,uBAAG,YAAU,uBAAG,WAChB,uBAAG,aAAW,uBAAG,YACjB,uBAAG,eAAa,uBAAG,eACnB,4BAAI,qBAAJ,mBAAsB,SAAM,4BAAI,qBAAJ,mBAAsB;AAAA;AACtD,IAAM,kBAAkB,CAAC,GAAG,MAAM;AAC9B,SAAO,EAAE,WAAW,EAAE,UAAU,EAAE,MAAM,CAAC,MAAM,MAAM,eAAe,MAAM,EAAE,CAAC,CAAC,CAAC;AACnF;AACA,IAAM,gBAAgB,CAAC,WAAW;AAAA,EAC9B,WAAW,MAAM;AAAA,EACjB,YAAY,MAAM;AAAA,EAClB,oBAAoB,MAAM,SAAS,EAAE,OAAO,CAAC,SAAS,KAAK,QAAQ,EAAE;AACzE;AACA,SAAS,aAAa,UAAU,WAAW,UAAU,QAAQ,OAAO;AAChE,MAAI,kBAAkB;AACtB,MAAI,UAAU,SAAS;AACnB,sBAAkB;AAAA,EACtB,WACS,UAAU,OAAO;AACtB,sBAAkB;AAAA,EACtB;AAGA,MAAI,MAAM;AAAA,KACL,SAAS,IAAI,SAAS,QAAQ,mBAAmB,UAAU,CAAC,IAAI,UAAU,CAAC;AAAA,IAC5E,SAAS,IAAI,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI;AAAA,EAC/C;AAEA,MAAI,QAAQ,CAAC,OAAO,iBAAiB,IAAI;AACzC,UAAQ,UAAU;AAAA,IACd,KAAK,SAAS;AACV,YAAM;AAAA,SACD,SAAS,IAAI,SAAS,SAAS,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI;AAAA,SAC7D,SAAS,IAAI,SAAS,SAAS,mBAAmB,UAAU,CAAC,IAAI,UAAU,CAAC;AAAA,MACjF;AACA,cAAQ,CAAC,GAAG,OAAO,eAAe;AAClC;AAAA,IACJ,KAAK,SAAS;AACV,UAAI,CAAC,KAAK,SAAS,IAAI,SAAS,UAAU,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI;AACxE,YAAM,CAAC,IAAI;AACX;AAAA,IACJ,KAAK,SAAS;AACV,YAAM;AAAA,QACF,SAAS,IAAI,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI;AAAA,SAC1C,SAAS,IAAI,SAAS,SAAS,mBAAmB,UAAU,CAAC,IAAI,UAAU,CAAC;AAAA,MACjF;AACA,cAAQ,CAAC,MAAM,OAAO,eAAe;AACrC;AAAA,EACR;AACA,SAAO,aAAa,IAAI,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,iBAAiB,MAAM,CAAC,CAAC,MAAM,MAAM,CAAC,CAAC;AAClF;AACA,SAAS,YAAY,EAAE,QAAQ,UAAU,WAAW,OAAAC,QAAO,WAAW,WAAW,SAAS,KAAK,SAAS,IAAI,QAAQ,UAAU,GAAG,KAAK,GAAG;AACrI,QAAM,gBAAgB,UAAU;AAChC,QAAMC,qBAAgB,2BAAY,CAAC,UAAU;AACzC,UAAM,UAAU,MAAM,QAAQ,MAAM,IAAI,SAAS,CAAC,UAAU,iBAAiB,EAAE;AAC/E,WAAO,QAAQ,OAAO,CAAC,KAAK,OAAO;AAC/B,YAAM,OAAO,MAAM,cAAc,IAAI,EAAE;AACvC,UAAI,MAAM;AACN,YAAI,KAAK,IAAI;AAAA,MACjB;AACA,aAAO;AAAA,IACX,GAAG,CAAC,CAAC;AAAA,EACT,GAAG,CAAC,QAAQ,aAAa,CAAC;AAC1B,QAAM,QAAQ,SAASA,gBAAe,eAAe;AACrD,QAAM,EAAE,WAAW,YAAY,mBAAmB,IAAI,SAAS,eAAeC,UAAO;AACrF,QAAM,WAAW,OAAO,cAAc,YAAY,YAAY,MAAM,WAAW,KAAK,MAAM,CAAC,EAAE,YAAY,uBAAuB;AAChI,MAAI,CAAC,YAAY,CAAC,MAAM,QAAQ;AAC5B,WAAO;AAAA,EACX;AACA,QAAM,WAAW,eAAe,OAAO,UAAU;AACjD,QAAM,SAAS,KAAK,IAAI,GAAG,MAAM,IAAI,CAAC,SAAM;AAtFhD;AAsFoD,wBAAK,eAAe,MAApB,mBAAuB,MAAK,KAAK;AAAA,GAAC,CAAC;AACnF,QAAMC,gBAAe;AAAA,IACjB,UAAU;AAAA,IACV,WAAW,aAAa,UAAU,WAAW,UAAU,QAAQ,KAAK;AAAA,IACpE;AAAA,IACA,GAAGH;AAAA,EACP;AACA,SAAQ,cAAAI,QAAM;AAAA,IAAc;AAAA,IAAmB;AAAA,IAC3C,cAAAA,QAAM,cAAc,OAAO,EAAE,OAAOD,eAAc,WAAW,GAAG,CAAC,4BAA4B,SAAS,CAAC,GAAG,GAAG,KAAK,GAAG,QAAQ;AAAA,EAAC;AACtI;;;AE/FA,IAAAE,gBAA+C;AAM/C,IAAI;AAAA,CACH,SAAUC,uBAAsB;AAC7B,EAAAA,sBAAqB,MAAM,IAAI;AAC/B,EAAAA,sBAAqB,QAAQ,IAAI;AACrC,GAAG,yBAAyB,uBAAuB,CAAC,EAAE;AAItD,SAASC,cAAa,EAAE,OAAO,WAAW,QAAQ,YAAY,SAAS,QAAQ,GAAG;AAC9E,QAAM,aAAa,QAAQ;AAC3B,QAAM,cAAc,SAAS;AAC7B,QAAM,YAAY,CAAC,aAAa,IAAI,IAAI,aAAa,IAAI,KAAK,GAAG,cAAc,IAAI,IAAI,cAAc,IAAI,KAAK,CAAC;AAC/G,MAAI,cAAc,SAAS;AACvB,cAAU,CAAC,IAAI,UAAU,CAAC,IAAI;AAAA,EAClC;AACA,MAAI,eAAe,SAAS;AACxB,cAAU,CAAC,IAAI,UAAU,CAAC,IAAI;AAAA,EAClC;AACA,SAAO;AACX;AAEA,IAAM,iBAAiB,EAAE,OAAO,GAAG,QAAQ,GAAG,GAAG,GAAG,GAAG,EAAE;AACzD,IAAM,kBAAkB;AAAA,EACpB,GAAG;AAAA,EACH,UAAU;AAAA,EACV,UAAU;AAAA,EACV,aAAa;AACjB;AACA,SAAS,cAAc,EAAE,QAAQ,UAAU,UAAU,qBAAqB,QAAQ,WAAW,OAAAC,SAAQ,CAAC,GAAG,UAAU,OAAO,WAAW,IAAI,YAAY,IAAI,WAAW,OAAO,WAAW,YAAY,OAAO,WAAW,kBAAkB,OAAO,cAAc,eAAe,UAAU,YAAa,GAAG;AAChS,QAAM,gBAAgB,UAAU;AAChC,QAAM,KAAK,OAAO,WAAW,WAAW,SAAS;AACjD,QAAM,QAAQ,YAAY;AAC1B,QAAM,uBAAmB,sBAAO,IAAI;AACpC,QAAM,kBAAc,sBAAO,eAAe;AAC1C,QAAM,iBAAa,sBAAO,cAAc;AACxC,QAAM,qBAAqB,sBAAsB;AACjD,QAAM,kBAAkB,YAAY,qBAAqB,OAAO,UAAU;AAC1E,QAAM,kBAAkB,YAAY;AACpC,+BAAU,MAAM;AACZ,QAAI,CAAC,iBAAiB,WAAW,CAAC,IAAI;AAClC;AAAA,IACJ;AACA,UAAM,YAAY,eAAO,iBAAiB,OAAO;AACjD,UAAM,UAAU,gBAAgB,SAAS,OAAO,KAAK,gBAAgB,SAAS,MAAM;AACpF,UAAM,UAAU,gBAAgB,SAAS,QAAQ,KAAK,gBAAgB,SAAS,KAAK;AACpF,UAAM,UAAU,gBAAgB,SAAS,MAAM;AAC/C,UAAM,UAAU,gBAAgB,SAAS,KAAK;AAC9C,UAAM,cAAc,aAAK,EACpB,GAAG,SAAS,CAAC,UAAU;AACxB,YAAM,OAAO,MAAM,SAAS,EAAE,cAAc,IAAI,EAAE;AAClD,YAAM,EAAE,UAAU,SAAS,IAAI,mBAAmB,KAAK;AACvD,iBAAW,UAAU;AAAA,QACjB,QAAO,6BAAM,UAAS;AAAA,QACtB,SAAQ,6BAAM,WAAU;AAAA,QACxB,IAAG,6BAAM,SAAS,MAAK;AAAA,QACvB,IAAG,6BAAM,SAAS,MAAK;AAAA,MAC3B;AACA,kBAAY,UAAU;AAAA,QAClB,GAAG,WAAW;AAAA,QACd,UAAU;AAAA,QACV,UAAU;AAAA,QACV,aAAa,WAAW,QAAQ,QAAQ,WAAW,QAAQ;AAAA,MAC/D;AACA,qDAAgB,OAAO,EAAE,GAAG,WAAW,QAAQ;AAAA,IACnD,CAAC,EACI,GAAG,QAAQ,CAAC,UAAU;AACvB,YAAM,EAAE,eAAe,mBAAmB,IAAI,MAAM,SAAS;AAC7D,YAAM,EAAE,UAAU,SAAS,IAAI,mBAAmB,KAAK;AACvD,YAAM,OAAO,cAAc,IAAI,EAAE;AACjC,UAAI,MAAM;AACN,cAAM,UAAU,CAAC;AACjB,cAAM,EAAE,UAAU,QAAQ,UAAU,QAAQ,OAAO,YAAY,QAAQ,aAAa,GAAG,YAAY,GAAG,YAAY,YAAa,IAAI,YAAY;AAC/I,cAAM,EAAE,GAAG,OAAO,GAAG,OAAO,OAAO,WAAW,QAAQ,WAAW,IAAI,WAAW;AAChF,cAAM,QAAQ,KAAK,MAAM,UAAU,WAAW,SAAS,CAAC;AACxD,cAAM,QAAQ,KAAK,MAAM,UAAU,WAAW,SAAS,CAAC;AACxD,YAAI,QAAQ,MAAM,cAAc,UAAU,CAAC,QAAQ,QAAQ,UAAU,QAAQ;AAC7E,YAAI,SAAS,MAAM,eAAe,UAAU,CAAC,QAAQ,QAAQ,WAAW,SAAS;AACjF,YAAI,iBAAiB;AACjB,gBAAM,kBAAkB,QAAQ;AAChC,gBAAM,aAAa,WAAW;AAC9B,gBAAM,eAAe,WAAW,CAAC;AACjC,gBAAM,aAAa,WAAW,CAAC;AAC/B,kBAAS,mBAAmB,eAAe,cAAe,aAAa,SAAS,cAAc;AAC9F,mBAAU,kBAAkB,eAAe,cAAe,eAAe,QAAQ,cAAc;AAC/F,cAAI,SAAS,UAAU;AACnB,oBAAQ;AACR,qBAAS,WAAW;AAAA,UACxB,WACS,SAAS,UAAU;AACxB,oBAAQ;AACR,qBAAS,WAAW;AAAA,UACxB;AACA,cAAI,UAAU,WAAW;AACrB,qBAAS;AACT,oBAAQ,YAAY;AAAA,UACxB,WACS,UAAU,WAAW;AAC1B,qBAAS;AACT,oBAAQ,YAAY;AAAA,UACxB;AAAA,QACJ;AACA,cAAM,gBAAgB,UAAU;AAChC,cAAM,iBAAiB,WAAW;AAClC,YAAI,WAAW,SAAS;AACpB,gBAAM,IAAI,UAAU,cAAc,QAAQ,cAAc;AACxD,gBAAM,IAAI,UAAU,cAAc,SAAS,eAAe;AAE1D,gBAAM,eAAe,MAAM,SAAS;AACpC,gBAAM,eAAe,MAAM,SAAS;AACpC,cAAI,gBAAgB,cAAc;AAC9B,kBAAM,iBAAiB;AAAA,cACnB,IAAI,KAAK;AAAA,cACT,MAAM;AAAA,cACN,UAAU;AAAA,gBACN,GAAG,eAAe,IAAI;AAAA,gBACtB,GAAG,eAAe,IAAI;AAAA,cAC1B;AAAA,YACJ;AACA,oBAAQ,KAAK,cAAc;AAC3B,uBAAW,QAAQ,IAAI,eAAe,SAAS;AAC/C,uBAAW,QAAQ,IAAI,eAAe,SAAS;AAAA,UACnD;AAAA,QACJ;AACA,YAAI,iBAAiB,gBAAgB;AACjC,gBAAM,kBAAkB;AAAA,YACpB;AAAA,YACA,MAAM;AAAA,YACN,aAAa;AAAA,YACb,UAAU;AAAA,YACV,YAAY;AAAA,cACR;AAAA,cACA;AAAA,YACJ;AAAA,UACJ;AACA,kBAAQ,KAAK,eAAe;AAC5B,qBAAW,QAAQ,QAAQ;AAC3B,qBAAW,QAAQ,SAAS;AAAA,QAChC;AACA,YAAI,QAAQ,WAAW,GAAG;AACtB;AAAA,QACJ;AACA,cAAM,YAAYD,cAAa;AAAA,UAC3B,OAAO,WAAW,QAAQ;AAAA,UAC1B;AAAA,UACA,QAAQ,WAAW,QAAQ;AAAA,UAC3B;AAAA,UACA;AAAA,UACA;AAAA,QACJ,CAAC;AACD,cAAM,aAAa,EAAE,GAAG,WAAW,SAAS,UAAU;AACtD,cAAM,aAAa,6CAAe,OAAO;AACzC,YAAI,eAAe,OAAO;AACtB;AAAA,QACJ;AACA,6CAAW,OAAO;AAClB,2BAAmB,OAAO;AAAA,MAC9B;AAAA,IACJ,CAAC,EACI,GAAG,OAAO,CAAC,UAAU;AACtB,YAAM,kBAAkB;AAAA,QACpB;AAAA,QACA,MAAM;AAAA,QACN,UAAU;AAAA,MACd;AACA,iDAAc,OAAO,EAAE,GAAG,WAAW,QAAQ;AAC7C,YAAM,SAAS,EAAE,mBAAmB,CAAC,eAAe,CAAC;AAAA,IACzD,CAAC;AACD,cAAU,KAAK,WAAW;AAC1B,WAAO,MAAM;AACT,gBAAU,GAAG,SAAS,IAAI;AAAA,IAC9B;AAAA,EACJ,GAAG;AAAA,IACC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;AACD,QAAM,qBAAqB,gBAAgB,MAAM,GAAG;AACpD,QAAM,iBAAiB,YAAY,qBAAqB,OAAO,gBAAgB;AAC/E,QAAM,eAAe,QAAQ,EAAE,GAAGC,QAAO,CAAC,cAAc,GAAG,MAAM,IAAIA;AACrE,SAAQ,cAAAC,QAAM,cAAc,OAAO,EAAE,WAAW,GAAG,CAAC,8BAA8B,UAAU,GAAG,oBAAoB,SAAS,SAAS,CAAC,GAAG,KAAK,kBAAkB,OAAO,aAAa,GAAG,QAAQ;AACnM;AACA,IAAI,sBAAkB,oBAAK,aAAa;AAExC,IAAM,iBAAiB,CAAC,YAAY,aAAa,eAAe,cAAc;AAC9E,IAAM,eAAe,CAAC,OAAO,SAAS,UAAU,MAAM;AACtD,SAAS,YAAY,EAAE,QAAQ,YAAY,MAAM,iBAAiB,aAAa,eAAe,WAAW,OAAO,WAAW,IAAI,YAAY,IAAI,WAAW,OAAO,WAAW,YAAY,OAAO,WAAW,kBAAkB,OAAO,cAAc,eAAe,UAAU,YAAa,GAAG;AACtR,MAAI,CAAC,WAAW;AACZ,WAAO;AAAA,EACX;AACA,SAAQ,cAAAA,QAAM;AAAA,IAAc,cAAAA,QAAM;AAAA,IAAU;AAAA,IACxC,aAAa,IAAI,CAAC,MAAO,cAAAA,QAAM,cAAc,iBAAiB,EAAE,KAAK,GAAG,WAAW,eAAe,OAAO,WAAW,QAAgB,UAAU,GAAG,SAAS,qBAAqB,MAAM,OAAc,UAAoB,WAAsB,UAAoB,WAAsB,eAA8B,iBAAkC,cAA4B,UAAoB,YAAyB,CAAC,CAAE;AAAA,IACna,eAAe,IAAI,CAAC,MAAO,cAAAA,QAAM,cAAc,iBAAiB,EAAE,KAAK,GAAG,WAAW,iBAAiB,OAAO,aAAa,QAAgB,UAAU,GAAG,OAAc,UAAoB,WAAsB,UAAoB,WAAsB,eAA8B,iBAAkC,cAA4B,UAAoB,YAAyB,CAAC,CAAE;AAAA,EAAC;AAC9Y;", "names": ["React", "useState", "useEffect", "useDebugValue", "React", "useRef", "useEffect", "useMemo", "useDebugValue", "selector", "import_react", "initialState", "ReactExports", "useSyncExternalStoreExports", "identity", "selector", "selector", "style", "React", "ConnectionMode", "PanOnScrollMode", "SelectionMode", "ConnectionLineType", "MarkerType", "Position", "xDir", "yDir", "_a", "distance", "event", "handle", "<PERSON><PERSON><PERSON><PERSON>", "change", "getNodes", "setNodes", "set<PERSON><PERSON>", "edges", "Symbol", "defaultColor", "containerStyle", "applyChanges", "items", "import_react", "shallow$1", "style", "React", "selector$1", "shallow$1", "selector", "width", "height", "position", "import_react", "shallow$1", "React", "selector", "style", "shallow$1", "<PERSON><PERSON><PERSON><PERSON>", "import_react", "shallow$1", "<PERSON><PERSON><PERSON><PERSON>", "React", "selector", "style", "shallow$1", "import_react", "shallow$1", "import_react_dom", "selector", "style", "nodesSelector", "shallow$1", "wrapperStyle", "React", "import_react", "ResizeControlVariant", "getDirection", "style", "React"]}