{"version": 3, "sources": ["../../refractor/lang/magma.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = magma\nmagma.displayName = 'magma'\nmagma.aliases = []\nfunction magma(Prism) {\n  Prism.languages.magma = {\n    output: {\n      pattern:\n        /^(>.*(?:\\r(?:\\n|(?!\\n))|\\n))(?!>)(?:.+|(?:\\r(?:\\n|(?!\\n))|\\n)(?!>).*)(?:(?:\\r(?:\\n|(?!\\n))|\\n)(?!>).*)*/m,\n      lookbehind: true,\n      greedy: true\n    },\n    comment: {\n      pattern: /\\/\\/.*|\\/\\*[\\s\\S]*?\\*\\//,\n      greedy: true\n    },\n    string: {\n      pattern: /(^|[^\\\\\"])\"(?:[^\\r\\n\\\\\"]|\\\\.)*\"/,\n      lookbehind: true,\n      greedy: true\n    },\n    // http://magma.maths.usyd.edu.au/magma/handbook/text/82\n    keyword:\n      /\\b(?:_|adj|and|assert|assert2|assert3|assigned|break|by|case|cat|catch|clear|cmpeq|cmpne|continue|declare|default|delete|diff|div|do|elif|else|end|eq|error|eval|exists|exit|for|forall|forward|fprintf|freeze|function|ge|gt|if|iload|import|in|intrinsic|is|join|le|load|local|lt|meet|mod|ne|not|notadj|notin|notsubset|or|print|printf|procedure|quit|random|read|readi|repeat|require|requirege|requirerange|restore|return|save|sdiff|select|subset|then|time|to|try|until|vprint|vprintf|vtime|when|where|while|xor)\\b/,\n    boolean: /\\b(?:false|true)\\b/,\n    generator: {\n      pattern: /\\b[a-z_]\\w*(?=\\s*<)/i,\n      alias: 'class-name'\n    },\n    function: /\\b[a-z_]\\w*(?=\\s*\\()/i,\n    number: {\n      pattern:\n        /(^|[^\\w.]|\\.\\.)(?:\\d+(?:\\.\\d*)?|\\.\\d+)(?:[eE][+-]?\\d+)?(?:_[a-z]?)?(?=$|[^\\w.]|\\.\\.)/,\n      lookbehind: true\n    },\n    operator: /->|[-+*/^~!|#=]|:=|\\.\\./,\n    punctuation: /[()[\\]{}<>,;.:]/\n  }\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,UAAM,cAAc;AACpB,UAAM,UAAU,CAAC;AACjB,aAAS,MAAM,OAAO;AACpB,YAAM,UAAU,QAAQ;AAAA,QACtB,QAAQ;AAAA,UACN,SACE;AAAA,UACF,YAAY;AAAA,UACZ,QAAQ;AAAA,QACV;AAAA,QACA,SAAS;AAAA,UACP,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,QAAQ;AAAA,QACV;AAAA;AAAA,QAEA,SACE;AAAA,QACF,SAAS;AAAA,QACT,WAAW;AAAA,UACT,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,UAAU;AAAA,QACV,QAAQ;AAAA,UACN,SACE;AAAA,UACF,YAAY;AAAA,QACd;AAAA,QACA,UAAU;AAAA,QACV,aAAa;AAAA,MACf;AAAA,IACF;AAAA;AAAA;", "names": []}