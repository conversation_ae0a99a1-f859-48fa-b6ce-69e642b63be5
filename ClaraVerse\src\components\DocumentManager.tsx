/**
 * Document Manager - Version complète avec OCR intégré et visualisation
 * Système unifié de gestion des documents avec traitement OCR premium
 */

import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  FileText,
  Upload,
  Search,
  Filter,
  Edit3,
  Trash2,
  Eye,
  Download,
  RefreshCw,
  Loader2,
  AlertCircle,
  CheckCircle,
  XCircle,
  Plus,
  Settings,
  Database,
  FileImage,
  Languages,
  Layout
} from 'lucide-react';

// Types
interface DocumentInfo {
  id: number;
  filename: string;
  fileType: string;
  collection_name: string;
  metadata: {
    processed_with_ocr?: boolean;
    ocr_language?: string;
    total_pages?: number;
    ocr_confidence?: number;
    text_blocks_count?: number;
    processing_time?: number;
    source_file?: string;
    file_type?: string;
  };
  created_at: string;
  chunk_count: number;
  content?: string;
}

interface Collection {
  name: string;
  documentCount: number;
  createdAt: string;
  description: string;
}

interface DocumentManagerProps {
  onPageChange: (page: string) => void;
}

interface UploadSettings {
  useOcr: boolean;
  ocrLanguage: string;
  preserveLayout: boolean;
  selectedCollection: string;
}

const DocumentManager: React.FC<DocumentManagerProps> = ({ onPageChange }) => {
  // États principaux
  const [documents, setDocuments] = useState<DocumentInfo[]>([]);
  const [collections, setCollections] = useState<Collection[]>([]);
  const [selectedDocument, setSelectedDocument] = useState<DocumentInfo | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // États de filtrage et recherche
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCollection, setSelectedCollection] = useState<string>('');
  const [showFilters, setShowFilters] = useState(false);
  
  // États d'upload
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [uploadSettings, setUploadSettings] = useState<UploadSettings>({
    useOcr: true,
    ocrLanguage: 'fra',
    preserveLayout: true,
    selectedCollection: 'default_collection'
  });
  
  // États de visualisation
  const [viewMode, setViewMode] = useState<'list' | 'preview' | 'edit'>('list');
  const [editContent, setEditContent] = useState('');
  
  // Références
  const fileInputRef = useRef<HTMLInputElement>(null);

  // ============================================================================
  // Chargement des données
  // ============================================================================

  const loadCollections = useCallback(async () => {
    try {
      const response = await fetch('http://localhost:8000/collections');
      if (response.ok) {
        const data = await response.json();
        setCollections(data.collections || []);
      }
    } catch (error) {
      console.error('Erreur chargement collections:', error);
    }
  }, []);

  const loadDocuments = useCallback(async (collectionName?: string) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const url = collectionName
        ? `http://localhost:5001/documents?collection_name=${encodeURIComponent(collectionName)}`
        : 'http://localhost:5001/documents';
        
      const response = await fetch(url);
      if (response.ok) {
        const data = await response.json();
        setDocuments(data.documents || []);
      } else {
        setError('Erreur lors du chargement des documents');
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Erreur inconnue');
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Chargement initial
  useEffect(() => {
    loadCollections();
    loadDocuments();
  }, [loadCollections, loadDocuments]);

  // ============================================================================
  // Gestion de l'upload
  // ============================================================================

  const handleFileUpload = useCallback(async (files: FileList) => {
    if (!files.length) return;
    
    setIsUploading(true);
    setUploadProgress(0);
    
    try {
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const formData = new FormData();
        
        formData.append('file', file);
        formData.append('collection_name', uploadSettings.selectedCollection);
        formData.append('use_ocr', uploadSettings.useOcr.toString());
        formData.append('ocr_language', uploadSettings.ocrLanguage);
        formData.append('preserve_layout', uploadSettings.preserveLayout.toString());
        formData.append('metadata', JSON.stringify({
          uploaded_via: 'document_manager',
          upload_timestamp: new Date().toISOString()
        }));
        
        const response = await fetch('http://localhost:5001/documents/upload', {
          method: 'POST',
          body: formData
        });
        
        if (!response.ok) {
          throw new Error(`Erreur upload ${file.name}: ${response.statusText}`);
        }
        
        const result = await response.json();
        console.log(`Document ${file.name} uploadé:`, result);

        // ✅ NOUVEAU SYSTÈME UNIFIÉ : L'upload fait déjà tout automatiquement !
        // - OCR Premium (extraction texte optimisée)
        // - SQLite (métadonnées et index)
        // - RAG Premium (LightRAG + Qdrant + BGE-M3)
        // Plus besoin d'appels séparés - tout est intégré dans /documents/upload

        console.log(`✅ ${file.name} traité par le système unifié:`, {
          processed_with_ocr: result.processed_with_ocr,
          text_length: result.text_length,
          collection: uploadSettings.selectedCollection
        });

        // Mise à jour du progrès
        setUploadProgress(((i + 1) / files.length) * 100);
      }

      // 🔄 RECHARGEMENT UNIFIÉ : Une seule fois après tous les uploads
      await loadDocuments(uploadSettings.selectedCollection);
      await loadCollections();

      // 🔄 SYNCHRONISATION STORE : Après rechargement local
      try {
        const { useDocumentStore } = await import('../stores/documentStore');
        const reloadDocuments = useDocumentStore.getState().reloadDocuments;
        await reloadDocuments();
        console.log('🔄 Store synchronisé après upload batch');
      } catch (storeError) {
        console.warn('⚠️ Échec synchronisation store:', storeError);
      }
      
      setShowUploadModal(false);
      
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Erreur upload');
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  }, [uploadSettings, loadDocuments, loadCollections]);

  const triggerFileUpload = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  // ============================================================================
  // Gestion des documents
  // ============================================================================

  const handleDocumentSelect = useCallback(async (document: DocumentInfo) => {
    setSelectedDocument(document);
    setIsLoading(true);
    
    try {
      const response = await fetch(`http://localhost:5001/documents/${document.id}`);
      if (response.ok) {
        const fullDocument = await response.json();
        setSelectedDocument(fullDocument);
        setEditContent(fullDocument.content || '');
        setViewMode('preview');
      }
    } catch (error) {
      console.error('Erreur chargement document:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const handleDocumentDelete = useCallback(async (document: DocumentInfo) => {
    console.log('🗑️ Tentative de suppression du document:', document);
    console.log('🔍 Document ID:', document.id, 'Type:', typeof document.id);

    if (!document.id) {
      setError('Erreur: ID du document manquant');
      return;
    }

    if (!confirm(`Supprimer le document "${document.filename}" ?`)) return;

    try {
      const deleteUrl = `http://localhost:8000/documents/${document.id}`;
      console.log('🌐 URL de suppression:', deleteUrl);

      const response = await fetch(deleteUrl, {
        method: 'DELETE'
      });

      console.log('📡 Réponse suppression:', response.status, response.statusText);

      if (response.ok) {
        console.log('✅ Document supprimé avec succès');

        // 🔄 RECHARGEMENT UNIFIÉ : Local puis store
        await loadDocuments(selectedCollection || undefined);
        await loadCollections();

        try {
          const { useDocumentStore } = await import('../stores/documentStore');
          const reloadDocuments = useDocumentStore.getState().reloadDocuments;
          await reloadDocuments();
          console.log('🔄 Store synchronisé après suppression');
        } catch (storeError) {
          console.warn('⚠️ Échec synchronisation store:', storeError);
        }

        if (selectedDocument?.id === document.id) {
          setSelectedDocument(null);
          setViewMode('list');
        }
      } else {
        const errorText = await response.text();
        console.error('❌ Erreur suppression:', response.status, errorText);
        setError(`Erreur lors de la suppression: ${response.status} ${errorText}`);
      }
    } catch (error) {
      console.error('❌ Exception suppression:', error);
      setError(error instanceof Error ? error.message : 'Erreur suppression');
    }
  }, [selectedCollection, loadDocuments, loadCollections, selectedDocument]);

  // ============================================================================
  // Filtrage et recherche
  // ============================================================================

  const filteredDocuments = documents.filter(doc => {
    const matchesSearch = !searchQuery || 
      doc.filename.toLowerCase().includes(searchQuery.toLowerCase()) ||
      doc.fileType.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesCollection = !selectedCollection || 
      doc.collection_name === selectedCollection;
    
    return matchesSearch && matchesCollection;
  });

  // ============================================================================
  // Rendu des composants
  // ============================================================================

  const renderHeader = () => (
    <div className="flex items-center justify-between mb-6">
      <div className="flex items-center gap-3">
        <Database className="w-8 h-8 text-wema-500" />
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Document Manager
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Gérez vos documents avec OCR intégré et visualisation avancée
          </p>
        </div>
      </div>
      
      <div className="flex items-center gap-2">
        <button
          onClick={() => setShowUploadModal(true)}
          className="flex items-center gap-2 px-4 py-2 bg-wema-500 text-white rounded-lg hover:bg-wema-600 transition-colors"
        >
          <Plus className="w-4 h-4" />
          Ajouter Document
        </button>
        
        <button
          onClick={() => loadDocuments(selectedCollection || undefined)}
          className="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors"
          disabled={isLoading}
        >
          <RefreshCw className={`w-5 h-5 ${isLoading ? 'animate-spin' : ''}`} />
        </button>
      </div>
    </div>
  );

  const renderUploadModal = () => {
    if (!showUploadModal) return null;
    
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white dark:bg-gray-800 rounded-xl p-6 w-full max-w-md">
          <h3 className="text-lg font-semibold mb-4">Upload de Documents</h3>
          
          {/* Paramètres d'upload */}
          <div className="space-y-4 mb-6">
            <div>
              <label className="block text-sm font-medium mb-2">Collection</label>
              <select
                value={uploadSettings.selectedCollection}
                onChange={(e) => setUploadSettings(prev => ({ ...prev, selectedCollection: e.target.value }))}
                className="w-full px-3 py-2 border rounded-lg"
              >
                <option value="default_collection">Collection par défaut</option>
                {collections.map(col => (
                  <option key={col.name} value={col.name}>{col.name}</option>
                ))}
              </select>
            </div>
            
            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="useOcr"
                checked={uploadSettings.useOcr}
                onChange={(e) => setUploadSettings(prev => ({ ...prev, useOcr: e.target.checked }))}
              />
              <label htmlFor="useOcr" className="text-sm">Utiliser OCR Premium</label>
            </div>
            
            {uploadSettings.useOcr && (
              <>
                <div>
                  <label className="block text-sm font-medium mb-2">Langue OCR</label>
                  <select
                    value={uploadSettings.ocrLanguage}
                    onChange={(e) => setUploadSettings(prev => ({ ...prev, ocrLanguage: e.target.value }))}
                    className="w-full px-3 py-2 border rounded-lg"
                  >
                    <option value="fra">Français</option>
                    <option value="eng">Anglais</option>
                    <option value="deu">Allemand</option>
                    <option value="spa">Espagnol</option>
                    <option value="ita">Italien</option>
                  </select>
                </div>
                
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id="preserveLayout"
                    checked={uploadSettings.preserveLayout}
                    onChange={(e) => setUploadSettings(prev => ({ ...prev, preserveLayout: e.target.checked }))}
                  />
                  <label htmlFor="preserveLayout" className="text-sm">Préserver la mise en page</label>
                </div>
              </>
            )}
          </div>
          
          {/* Zone d'upload */}
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center mb-4">
            <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
            <p className="text-gray-600 mb-2">Glissez vos fichiers ici ou</p>
            <button
              onClick={triggerFileUpload}
              className="px-4 py-2 bg-wema-500 text-white rounded-lg hover:bg-wema-600"
              disabled={isUploading}
            >
              Parcourir
            </button>
          </div>
          
          {/* Progrès d'upload */}
          {isUploading && (
            <div className="mb-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm">Upload en cours...</span>
                <span className="text-sm">{Math.round(uploadProgress)}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-wema-500 h-2 rounded-full transition-all"
                  style={{ width: `${uploadProgress}%` }}
                />
              </div>
            </div>
          )}
          
          {/* Boutons */}
          <div className="flex justify-end gap-2">
            <button
              onClick={() => setShowUploadModal(false)}
              className="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg"
              disabled={isUploading}
            >
              Annuler
            </button>
          </div>
          
          <input
            ref={fileInputRef}
            type="file"
            multiple
            accept=".pdf,.txt,.md,.csv,.png,.jpg,.jpeg"
            onChange={(e) => e.target.files && handleFileUpload(e.target.files)}
            className="hidden"
          />
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
      <div className="max-w-7xl mx-auto">
        {renderHeader()}
        {renderUploadModal()}
        
        {/* Filtres et recherche */}
        <div className="mb-6 flex items-center gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Rechercher des documents..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-wema-500 focus:border-transparent"
            />
          </div>

          <select
            value={selectedCollection}
            onChange={(e) => setSelectedCollection(e.target.value)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-wema-500"
          >
            <option value="">Toutes les collections</option>
            {collections.map((collection) => (
              <option key={collection.name} value={collection.name}>
                {collection.name} ({collection.documentCount} docs)
              </option>
            ))}
          </select>

          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`p-2 rounded-lg transition-colors ${
              showFilters
                ? 'bg-wema-100 dark:bg-wema-900/30 text-wema-600 dark:text-wema-400'
                : 'hover:bg-gray-100 dark:hover:bg-gray-800'
            }`}
          >
            <Filter className="w-5 h-5" />
          </button>
        </div>

        {/* État de chargement */}
        {isLoading && (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="w-8 h-8 animate-spin text-wema-500" />
            <span className="ml-2 text-gray-600 dark:text-gray-400">Chargement...</span>
          </div>
        )}

        {/* État d'erreur */}
        {error && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
            <div className="flex items-center gap-2">
              <AlertCircle className="w-5 h-5 text-red-500" />
              <span className="text-red-700 dark:text-red-400">{error}</span>
            </div>
          </div>
        )}

        {/* Layout principal */}
        {!isLoading && !error && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Liste des documents */}
            <div className="lg:col-span-2">
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                    Documents ({filteredDocuments.length})
                  </h2>
                </div>

                <div className="p-4">
                  {filteredDocuments.length === 0 ? (
                    <div className="text-center py-12">
                      <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-600 dark:text-gray-400">Aucun document trouvé</p>
                      <button
                        onClick={() => setShowUploadModal(true)}
                        className="mt-4 px-4 py-2 bg-wema-500 text-white rounded-lg hover:bg-wema-600 transition-colors"
                      >
                        Ajouter votre premier document
                      </button>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      {filteredDocuments.map((document) => (
                        <div
                          key={document.id}
                          className={`flex items-center justify-between p-3 rounded-lg transition-colors cursor-pointer ${
                            selectedDocument?.id === document.id
                              ? 'bg-wema-50 dark:bg-wema-900/20 border border-wema-200 dark:border-wema-800'
                              : 'hover:bg-gray-50 dark:hover:bg-gray-700'
                          }`}
                          onClick={() => handleDocumentSelect(document)}
                        >
                          <div className="flex items-center gap-3">
                            <div className="relative">
                              <FileText className="w-5 h-5 text-gray-400" />
                              {document.metadata.processed_with_ocr && (
                                <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full" title="Traité avec OCR" />
                              )}
                            </div>
                            <div>
                              <h3 className="font-medium text-gray-900 dark:text-white">
                                {document.filename}
                              </h3>
                              <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                                <span>{document.fileType?.toUpperCase() || 'UNKNOWN'}</span>
                                <span>{document.chunk_count} chunks</span>
                                {document.metadata.processed_with_ocr && (
                                  <span className="flex items-center gap-1 text-green-600 dark:text-green-400">
                                    <FileImage className="w-3 h-3" />
                                    OCR
                                  </span>
                                )}
                                {document.metadata.total_pages && (
                                  <span>{document.metadata.total_pages} pages</span>
                                )}
                              </div>
                            </div>
                          </div>

                          <div className="flex items-center gap-2">
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDocumentSelect(document);
                              }}
                              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg transition-colors"
                              title="Voir le document"
                            >
                              <Eye className="w-4 h-4" />
                            </button>

                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                setSelectedDocument(document);
                                setViewMode('edit');
                              }}
                              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg transition-colors"
                              title="Éditer le document"
                            >
                              <Edit3 className="w-4 h-4" />
                            </button>

                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDocumentDelete(document);
                              }}
                              className="p-2 hover:bg-red-100 dark:hover:bg-red-900/30 text-red-600 dark:text-red-400 rounded-lg transition-colors"
                              title="Supprimer le document"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Panneau de visualisation */}
            <div className="lg:col-span-1">
              {selectedDocument ? (
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                  <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                    <div className="flex items-center justify-between">
                      <h3 className="font-semibold text-gray-900 dark:text-white">
                        {selectedDocument.filename}
                      </h3>
                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => setViewMode('preview')}
                          className={`p-2 rounded-lg transition-colors ${
                            viewMode === 'preview'
                              ? 'bg-wema-100 dark:bg-wema-900/30 text-wema-600 dark:text-wema-400'
                              : 'hover:bg-gray-100 dark:hover:bg-gray-800'
                          }`}
                          title="Aperçu"
                        >
                          <Eye className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => setViewMode('edit')}
                          className={`p-2 rounded-lg transition-colors ${
                            viewMode === 'edit'
                              ? 'bg-wema-100 dark:bg-wema-900/30 text-wema-600 dark:text-wema-400'
                              : 'hover:bg-gray-100 dark:hover:bg-gray-800'
                          }`}
                          title="Édition"
                        >
                          <Edit3 className="w-4 h-4" />
                        </button>
                      </div>
                    </div>

                    {/* Métadonnées du document */}
                    <div className="mt-3 space-y-2 text-sm text-gray-600 dark:text-gray-400">
                      <div className="flex justify-between">
                        <span>Type:</span>
                        <span>{selectedDocument.fileType?.toUpperCase() || 'UNKNOWN'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Collection:</span>
                        <span>{selectedDocument.collection_name}</span>
                      </div>
                      {selectedDocument.metadata.processed_with_ocr && (
                        <>
                          <div className="flex justify-between">
                            <span>OCR:</span>
                            <span className="text-green-600 dark:text-green-400">✓ Activé</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Langue:</span>
                            <span>{selectedDocument.metadata.ocr_language?.toUpperCase() || 'FR'}</span>
                          </div>
                          {selectedDocument.metadata.ocr_confidence && (
                            <div className="flex justify-between">
                              <span>Confiance:</span>
                              <span>{Math.round(selectedDocument.metadata.ocr_confidence)}%</span>
                            </div>
                          )}
                        </>
                      )}
                    </div>
                  </div>

                  {/* Contenu du document */}
                  <div className="p-4 h-96 overflow-y-auto">
                    {viewMode === 'preview' ? (
                      <pre className="whitespace-pre-wrap text-sm text-gray-900 dark:text-white font-mono">
                        {selectedDocument.content || 'Chargement du contenu...'}
                      </pre>
                    ) : (
                      <textarea
                        value={editContent}
                        onChange={(e) => setEditContent(e.target.value)}
                        className="w-full h-full resize-none border-0 focus:ring-0 bg-transparent text-gray-900 dark:text-white font-mono text-sm"
                        placeholder="Contenu du document..."
                      />
                    )}
                  </div>

                  {viewMode === 'edit' && (
                    <div className="p-4 border-t border-gray-200 dark:border-gray-700">
                      <button
                        onClick={() => {
                          // TODO: Implémenter la sauvegarde
                          console.log('Sauvegarde:', editContent);
                        }}
                        className="w-full px-4 py-2 bg-wema-500 text-white rounded-lg hover:bg-wema-600 transition-colors"
                      >
                        Sauvegarder
                      </button>
                    </div>
                  )}
                </div>
              ) : (
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-8 text-center">
                  <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600 dark:text-gray-400">
                    Sélectionnez un document pour le visualiser
                  </p>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DocumentManager;
