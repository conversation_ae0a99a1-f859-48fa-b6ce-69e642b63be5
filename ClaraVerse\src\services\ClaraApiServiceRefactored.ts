/**
 * 🚀 CLARA API SERVICE - REFACTORISÉ
 * Service principal simplifié utilisant des modules spécialisés
 */

import type { 
  ClaraMessage, 
  ClaraFileAttachment, 
  ClaraProvider, 
  ClaraModel, 
  ClaraAIConfig 
} from '../types/clara_assistant_types';

// Import des modules spécialisés
import { providerManager } from './providers/ProviderManager';
import { chatManager } from './chat/ChatManager';
import { toolExecutor } from './tools/ToolExecutor';
import { db } from '../db';
import type { Tool } from '../db';
import { claraMCPService } from './claraMCPService';

export class ClaraApiServiceRefactored {
  private currentProvider: ClaraProvider | null = null;

  constructor() {
    this.initializeFromConfig();
  }

  /**
   * Initialize from configuration
   */
  private async initializeFromConfig() {
    try {
      const primaryProvider = await providerManager.getPrimaryProvider();
      if (primaryProvider) {
        this.updateProvider(primaryProvider);
      }
    } catch (error) {
      console.warn('Failed to load primary provider:', error);
    }
  }

  /**
   * Update current provider
   */
  public updateProvider(provider: ClaraProvider) {
    this.currentProvider = provider;
    chatManager.updateProvider(provider);
  }

  // ============================================================================
  // PROVIDER MANAGEMENT (délégué à ProviderManager)
  // ============================================================================

  public async getProviders(): Promise<ClaraProvider[]> {
    return providerManager.getProviders();
  }

  public async getModels(providerId?: string): Promise<ClaraModel[]> {
    return providerManager.getModels(providerId);
  }

  public async getCurrentProviderModels(): Promise<ClaraModel[]> {
    if (!this.currentProvider) {
      return [];
    }
    return providerManager.getModels(this.currentProvider.id);
  }

  public async getPrimaryProvider(): Promise<ClaraProvider | null> {
    return providerManager.getPrimaryProvider();
  }

  public async testProvider(provider: ClaraProvider): Promise<boolean> {
    return providerManager.testProvider(provider);
  }

  public clearCache(): void {
    providerManager.clearCache();
  }

  // ============================================================================
  // CHAT MANAGEMENT (délégué à ChatManager)
  // ============================================================================

  /**
   * Send a chat message with full feature support
   */
  public async sendChatMessage(
    message: string,
    config: ClaraAIConfig,
    attachments?: ClaraFileAttachment[],
    systemPrompt?: string,
    conversationHistory?: ClaraMessage[],
    onContentChunk?: (content: string) => void
  ): Promise<ClaraMessage> {
    if (!this.currentProvider) {
      throw new Error('No provider configured. Please select a provider.');
    }

    // Switch provider if needed
    if (config.provider && config.provider !== this.currentProvider.id) {
      await this.switchToProvider(config.provider);
    }

    // Prepare chat context
    const context = chatManager.prepareConversationContext(
      message,
      config,
      systemPrompt,
      conversationHistory,
      attachments
    );

    // Process file attachments if any
    const processedAttachments = await this.processFileAttachments(attachments || []);
    context.attachments = processedAttachments;

    // Determine if tools are enabled
    const toolsEnabled = config.features.enableTools;
    
    if (toolsEnabled) {
      // Get available tools
      const tools = await this.getAvailableTools(config);
      
      if (tools.length > 0) {
        console.log(`🛠️ Using ${tools.length} tools for enhanced capabilities`);
        return chatManager.sendMessageWithTools(message, context, tools, onContentChunk);
      }
    }

    // Send standard message without tools
    console.log('💬 Sending standard message without tools');
    return chatManager.sendStandardMessage(message, context, onContentChunk);
  }

  /**
   * Switch to a specific provider
   */
  private async switchToProvider(providerId: string): Promise<void> {
    console.log(`🔄 Switching to provider: ${providerId}`);
    
    const providers = await this.getProviders();
    const requestedProvider = providers.find(p => p.id === providerId);
    
    if (!requestedProvider) {
      throw new Error(`Provider ${providerId} not found`);
    }
    
    if (!requestedProvider.isEnabled) {
      throw new Error(`Provider ${requestedProvider.name} is not enabled`);
    }
    
    this.updateProvider(requestedProvider);
    console.log(`✅ Switched to provider: ${requestedProvider.name}`);
  }

  /**
   * Get available tools based on configuration
   */
  private async getAvailableTools(config: ClaraAIConfig): Promise<any[]> {
    const tools: Tool[] = [];

    // Get database tools
    if (config.features.enableTools) {
      const dbTools = await db.getEnabledTools();
      tools.push(...dbTools);
    }

    // Get MCP tools if enabled
    if (config.features.enableMCP && config.mcp?.enableTools) {
      console.log('🔧 MCP is enabled, adding MCP tools...');
      
      if (claraMCPService.isReady()) {
        const enabledServers = config.mcp.enabledServers || [];
        
        if (enabledServers.length > 0) {
          const mcpTools = claraMCPService.getToolsFromEnabledServers(enabledServers);
          console.log(`🛠️ Found ${mcpTools.length} MCP tools`);
          
          // Convert MCP tools to standard format
          const mcpToolsFormatted: Tool[] = mcpTools.map(tool => ({
            id: tool.name,
            name: tool.name,
            description: tool.description || '',
            parameters: [], // Simplified for now
            implementation: 'mcp',
            isEnabled: true
          }));
          
          tools.push(...mcpToolsFormatted);
        }
      }
    }

    // Convert to OpenAI tool format
    return tools.map(tool => ({
      type: 'function',
      function: {
        name: tool.name,
        description: tool.description,
        parameters: {
          type: 'object',
          properties: {},
          required: []
        }
      }
    }));
  }

  /**
   * Process file attachments
   */
  private async processFileAttachments(attachments: ClaraFileAttachment[]): Promise<ClaraFileAttachment[]> {
    // For now, return attachments as-is
    // TODO: Implement file processing logic
    return attachments;
  }

  // ============================================================================
  // UTILITY METHODS
  // ============================================================================

  /**
   * Health check
   */
  public async healthCheck(): Promise<boolean> {
    if (!this.currentProvider) {
      return false;
    }
    
    return this.testProvider(this.currentProvider);
  }

  /**
   * Abort current stream
   */
  public abortStream(): void {
    // TODO: Implement stream abortion
    console.log('🛑 Stream abortion requested');
  }

  /**
   * Preload model for better performance
   */
  public async preloadModel(config: ClaraAIConfig): Promise<void> {
    if (!this.currentProvider || !config.models.text) {
      return;
    }

    try {
      console.log(`🚀 Preloading model: ${config.models.text}`);
      
      // Simple preload with minimal request
      await this.sendChatMessage(
        'Hello',
        { ...config, maxTokens: 1 },
        [],
        undefined,
        [],
        undefined
      );
      
      console.log('✅ Model preloaded successfully');
    } catch (error) {
      console.log(`🔄 Model preload failed (non-critical): ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}

// Export singleton instance
export const claraApiServiceRefactored = new ClaraApiServiceRefactored();
