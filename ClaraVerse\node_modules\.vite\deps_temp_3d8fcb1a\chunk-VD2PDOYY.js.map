{"version": 3, "sources": ["../../highlight.js/lib/languages/autohotkey.js"], "sourcesContent": ["/*\nLanguage: AutoHotkey\nAuthor: <PERSON><PERSON><PERSON> <<EMAIL>>\nDescription: AutoHotkey language definition\nCategory: scripting\n*/\n\n/** @type LanguageFn */\nfunction autohotkey(hljs) {\n  const BACKTICK_ESCAPE = {\n    begin: '`[\\\\s\\\\S]'\n  };\n\n  return {\n    name: 'AutoHotkey',\n    case_insensitive: true,\n    aliases: ['ahk'],\n    keywords: {\n      keyword: 'Break Continue Critical Exit ExitApp Gosub Goto New OnExit Pause return SetBatchLines SetTimer Suspend Thread Throw Until ahk_id ahk_class ahk_pid ahk_exe ahk_group',\n      literal: 'true false NOT AND OR',\n      built_in: 'ComSpec Clipboard ClipboardAll ErrorLevel'\n    },\n    contains: [\n      BACKTICK_ESCAPE,\n      hljs.inherit(hljs.QUOTE_STRING_MODE, {\n        contains: [BACKTICK_ESCAPE]\n      }),\n      hljs.COMMENT(';', '$', {\n        relevance: 0\n      }),\n      hljs.C_BLOCK_COMMENT_MODE,\n      {\n        className: 'number',\n        begin: hljs.NUMBER_RE,\n        relevance: 0\n      },\n      {\n        // subst would be the most accurate however fails the point of\n        // highlighting. variable is comparably the most accurate that actually\n        // has some effect\n        className: 'variable',\n        begin: '%[a-zA-Z0-9#_$@]+%'\n      },\n      {\n        className: 'built_in',\n        begin: '^\\\\s*\\\\w+\\\\s*(,|%)'\n        // I don't really know if this is totally relevant\n      },\n      {\n        // symbol would be most accurate however is highlighted just like\n        // built_in and that makes up a lot of AutoHotkey code meaning that it\n        // would fail to highlight anything\n        className: 'title',\n        variants: [\n          {\n            begin: '^[^\\\\n\";]+::(?!=)'\n          },\n          {\n            begin: '^[^\\\\n\";]+:(?!=)',\n            // zero relevance as it catches a lot of things\n            // followed by a single ':' in many languages\n            relevance: 0\n          }\n        ]\n      },\n      {\n        className: 'meta',\n        begin: '^\\\\s*#\\\\w+',\n        end: '$',\n        relevance: 0\n      },\n      {\n        className: 'built_in',\n        begin: 'A_[a-zA-Z0-9]+'\n      },\n      {\n        // consecutive commas, not for highlighting but just for relevance\n        begin: ',\\\\s*,'\n      }\n    ]\n  };\n}\n\nmodule.exports = autohotkey;\n"], "mappings": ";;;;;AAAA;AAAA;AAQA,aAAS,WAAW,MAAM;AACxB,YAAM,kBAAkB;AAAA,QACtB,OAAO;AAAA,MACT;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,kBAAkB;AAAA,QAClB,SAAS,CAAC,KAAK;AAAA,QACf,UAAU;AAAA,UACR,SAAS;AAAA,UACT,SAAS;AAAA,UACT,UAAU;AAAA,QACZ;AAAA,QACA,UAAU;AAAA,UACR;AAAA,UACA,KAAK,QAAQ,KAAK,mBAAmB;AAAA,YACnC,UAAU,CAAC,eAAe;AAAA,UAC5B,CAAC;AAAA,UACD,KAAK,QAAQ,KAAK,KAAK;AAAA,YACrB,WAAW;AAAA,UACb,CAAC;AAAA,UACD,KAAK;AAAA,UACL;AAAA,YACE,WAAW;AAAA,YACX,OAAO,KAAK;AAAA,YACZ,WAAW;AAAA,UACb;AAAA,UACA;AAAA;AAAA;AAAA;AAAA,YAIE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA;AAAA,UAET;AAAA,UACA;AAAA;AAAA;AAAA;AAAA,YAIE,WAAW;AAAA,YACX,UAAU;AAAA,cACR;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA;AAAA;AAAA,gBAGP,WAAW;AAAA,cACb;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA,UACA;AAAA;AAAA,YAEE,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}