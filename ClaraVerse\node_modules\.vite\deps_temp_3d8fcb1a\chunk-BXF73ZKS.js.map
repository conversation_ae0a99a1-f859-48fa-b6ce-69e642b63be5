{"version": 3, "sources": ["../../refractor/lang/jsstacktrace.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = jsstacktrace\njsstacktrace.displayName = 'jsstacktrace'\njsstacktrace.aliases = []\nfunction jsstacktrace(Prism) {\n  Prism.languages.jsstacktrace = {\n    'error-message': {\n      pattern: /^\\S.*/m,\n      alias: 'string'\n    },\n    'stack-frame': {\n      pattern: /(^[ \\t]+)at[ \\t].*/m,\n      lookbehind: true,\n      inside: {\n        'not-my-code': {\n          pattern:\n            /^at[ \\t]+(?!\\s)(?:node\\.js|<unknown>|.*(?:node_modules|\\(<anonymous>\\)|\\(<unknown>|<anonymous>$|\\(internal\\/|\\(node\\.js)).*/m,\n          alias: 'comment'\n        },\n        filename: {\n          pattern: /(\\bat\\s+(?!\\s)|\\()(?:[a-zA-Z]:)?[^():]+(?=:)/,\n          lookbehind: true,\n          alias: 'url'\n        },\n        function: {\n          pattern:\n            /(\\bat\\s+(?:new\\s+)?)(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF<][.$\\w\\xA0-\\uFFFF<>]*/,\n          lookbehind: true,\n          inside: {\n            punctuation: /\\./\n          }\n        },\n        punctuation: /[()]/,\n        keyword: /\\b(?:at|new)\\b/,\n        alias: {\n          pattern: /\\[(?:as\\s+)?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF][$\\w\\xA0-\\uFFFF]*\\]/,\n          alias: 'variable'\n        },\n        'line-number': {\n          pattern: /:\\d+(?::\\d+)?\\b/,\n          alias: 'number',\n          inside: {\n            punctuation: /:/\n          }\n        }\n      }\n    }\n  }\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,iBAAa,cAAc;AAC3B,iBAAa,UAAU,CAAC;AACxB,aAAS,aAAa,OAAO;AAC3B,YAAM,UAAU,eAAe;AAAA,QAC7B,iBAAiB;AAAA,UACf,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,eAAe;AAAA,UACb,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,QAAQ;AAAA,YACN,eAAe;AAAA,cACb,SACE;AAAA,cACF,OAAO;AAAA,YACT;AAAA,YACA,UAAU;AAAA,cACR,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,OAAO;AAAA,YACT;AAAA,YACA,UAAU;AAAA,cACR,SACE;AAAA,cACF,YAAY;AAAA,cACZ,QAAQ;AAAA,gBACN,aAAa;AAAA,cACf;AAAA,YACF;AAAA,YACA,aAAa;AAAA,YACb,SAAS;AAAA,YACT,OAAO;AAAA,cACL,SAAS;AAAA,cACT,OAAO;AAAA,YACT;AAAA,YACA,eAAe;AAAA,cACb,SAAS;AAAA,cACT,OAAO;AAAA,cACP,QAAQ;AAAA,gBACN,aAAa;AAAA,cACf;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA;AAAA;", "names": []}