# 🔧 Solution temporaire pour l'erreur de streaming

## Problème identifié
- Erreur `ERR_INCOMPLETE_CHUNKED_ENCODING` lors du streaming
- Le proxy FastAPI → LM Studio a des problèmes de chunked encoding

## Solution immédiate
1. **Désactivez temporairement le streaming** dans WeMa IA :
   - Allez dans les paramètres de l'assistant
   - Désactivez "Enable Streaming"
   - Utilisez le mode non-streaming

2. **Ou testez avec un modèle plus petit** :
   - Le qwen3-4b peut être trop lent pour le streaming
   - Essayez avec gemma-3-4b qui est plus rapide

## Correction appliquée
- Amélioration du proxy streaming dans main.py
- Meilleure gestion des chunks
- Headers optimisés pour le streaming

## Test
Redémarrez le backend et testez une requête simple sans streaming d'abord.
