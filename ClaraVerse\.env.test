# Configuration de test WeMa IA
# Pour tester en local sur ton PC

# 🧠 SERVEUR D'INFÉRENCE LOCAL (pour tes tests)
INFERENCE_SERVER_IP=localhost
INFERENCE_SERVER_PORT=1235

# 🏠 FALLBACK LOCAL ACTIVÉ (pour tes tests)
INFERENCE_FALLBACK_LOCAL=true

# 📊 CONFIGURATION RAG LOCAL
RAG_ENABLED=true
RAG_FAST_MODE=false

# 🔍 CONFIGURATION OCR LOCAL
OCR_ENABLED=true
OCR_LANGUAGE=fra
OCR_PRESERVE_LAYOUT=true

# 💾 BASE DE DONNÉES LOCALE
DATABASE_PATH=./data/clara.db

# 🔧 CONFIGURATION BACKEND LOCAL
CLARA_HOST=127.0.0.1
CLARA_PORT=8000

# 📝 LOGS
LOG_LEVEL=INFO
DEBUG_INFERENCE=true

# 🎛️ MODE ADMIN (pour tes tests)
REACT_APP_ADMIN_MODE=true
