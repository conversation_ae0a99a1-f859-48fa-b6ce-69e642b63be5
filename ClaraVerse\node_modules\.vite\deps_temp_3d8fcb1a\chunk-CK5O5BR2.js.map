{"version": 3, "sources": ["../../refractor/lang/nasm.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = nasm\nnasm.displayName = 'nasm'\nnasm.aliases = []\nfunction nasm(Prism) {\n  Prism.languages.nasm = {\n    comment: /;.*$/m,\n    string: /([\"'`])(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/,\n    label: {\n      pattern: /(^\\s*)[A-Za-z._?$][\\w.?$@~#]*:/m,\n      lookbehind: true,\n      alias: 'function'\n    },\n    keyword: [\n      /\\[?BITS (?:16|32|64)\\]?/,\n      {\n        pattern: /(^\\s*)section\\s*[a-z.]+:?/im,\n        lookbehind: true\n      },\n      /(?:extern|global)[^;\\r\\n]*/i,\n      /(?:CPU|DEFAULT|FLOAT).*$/m\n    ],\n    register: {\n      pattern:\n        /\\b(?:st\\d|[xyz]mm\\d\\d?|[cdt]r\\d|r\\d\\d?[bwd]?|[er]?[abcd]x|[abcd][hl]|[er]?(?:bp|di|si|sp)|[cdefgs]s)\\b/i,\n      alias: 'variable'\n    },\n    number:\n      /(?:\\b|(?=\\$))(?:0[hx](?:\\.[\\da-f]+|[\\da-f]+(?:\\.[\\da-f]+)?)(?:p[+-]?\\d+)?|\\d[\\da-f]+[hx]|\\$\\d[\\da-f]*|0[oq][0-7]+|[0-7]+[oq]|0[by][01]+|[01]+[by]|0[dt]\\d+|(?:\\d+(?:\\.\\d+)?|\\.\\d+)(?:\\.?e[+-]?\\d+)?[dt]?)\\b/i,\n    operator: /[\\[\\]*+\\-\\/%<>=&|$!]/\n  }\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,SAAK,cAAc;AACnB,SAAK,UAAU,CAAC;AAChB,aAAS,KAAK,OAAO;AACnB,YAAM,UAAU,OAAO;AAAA,QACrB,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,OAAO;AAAA,UACL,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,QACA,SAAS;AAAA,UACP;AAAA,UACA;AAAA,YACE,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,UAAU;AAAA,UACR,SACE;AAAA,UACF,OAAO;AAAA,QACT;AAAA,QACA,QACE;AAAA,QACF,UAAU;AAAA,MACZ;AAAA,IACF;AAAA;AAAA;", "names": []}