{"version": 3, "sources": ["../../highlight.js/lib/languages/ada.js"], "sourcesContent": ["/*\nLanguage: Ada\nAuthor: <PERSON> <<EMAIL>>\nDescription: Ada is a general-purpose programming language that has great support for saftey critical and real-time applications.\n             It has been developed by the DoD and thus has been used in military and safety-critical applications (like civil aviation).\n             The first version appeared in the 80s, but it's still actively developed today with\n             the newest standard being Ada2012.\n*/\n\n// We try to support full Ada2012\n//\n// We highlight all appearances of types, keywords, literals (string, char, number, bool)\n// and titles (user defined function/procedure/package)\n// CSS classes are set accordingly\n//\n// Languages causing problems for language detection:\n// xml (broken by Foo : Bar type), elm (broken by Foo : Bar type), vbscript-html (broken by body keyword)\n// sql (ada default.txt has a lot of sql keywords)\n\n/** @type LanguageFn */\nfunction ada(hljs) {\n  // Regular expression for Ada numeric literals.\n  // stolen form the VHDL highlighter\n\n  // Decimal literal:\n  const INTEGER_RE = '\\\\d(_|\\\\d)*';\n  const EXPONENT_RE = '[eE][-+]?' + INTEGER_RE;\n  const DECIMAL_LITERAL_RE = INTEGER_RE + '(\\\\.' + INTEGER_RE + ')?' + '(' + EXPONENT_RE + ')?';\n\n  // Based literal:\n  const BASED_INTEGER_RE = '\\\\w+';\n  const BASED_LITERAL_RE = INTEGER_RE + '#' + BASED_INTEGER_RE + '(\\\\.' + BASED_INTEGER_RE + ')?' + '#' + '(' + EXPONENT_RE + ')?';\n\n  const NUMBER_RE = '\\\\b(' + BASED_LITERAL_RE + '|' + DECIMAL_LITERAL_RE + ')';\n\n  // Identifier regex\n  const ID_REGEX = '[A-Za-z](_?[A-Za-z0-9.])*';\n\n  // bad chars, only allowed in literals\n  const BAD_CHARS = `[]\\\\{\\\\}%#'\"`;\n\n  // Ada doesn't have block comments, only line comments\n  const COMMENTS = hljs.COMMENT('--', '$');\n\n  // variable declarations of the form\n  // Foo : Bar := Baz;\n  // where only Bar will be highlighted\n  const VAR_DECLS = {\n    // TODO: These spaces are not required by the Ada syntax\n    // however, I have yet to see handwritten Ada code where\n    // someone does not put spaces around :\n    begin: '\\\\s+:\\\\s+',\n    end: '\\\\s*(:=|;|\\\\)|=>|$)',\n    // endsWithParent: true,\n    // returnBegin: true,\n    illegal: BAD_CHARS,\n    contains: [\n      {\n        // workaround to avoid highlighting\n        // named loops and declare blocks\n        beginKeywords: 'loop for declare others',\n        endsParent: true\n      },\n      {\n        // properly highlight all modifiers\n        className: 'keyword',\n        beginKeywords: 'not null constant access function procedure in out aliased exception'\n      },\n      {\n        className: 'type',\n        begin: ID_REGEX,\n        endsParent: true,\n        relevance: 0\n      }\n    ]\n  };\n\n  return {\n    name: 'Ada',\n    case_insensitive: true,\n    keywords: {\n      keyword:\n                'abort else new return abs elsif not reverse abstract end ' +\n                'accept entry select access exception of separate aliased exit or some ' +\n                'all others subtype and for out synchronized array function overriding ' +\n                'at tagged generic package task begin goto pragma terminate ' +\n                'body private then if procedure type case in protected constant interface ' +\n                'is raise use declare range delay limited record when delta loop rem while ' +\n                'digits renames with do mod requeue xor',\n      literal:\n                'True False'\n    },\n    contains: [\n      COMMENTS,\n      // strings \"foobar\"\n      {\n        className: 'string',\n        begin: /\"/,\n        end: /\"/,\n        contains: [{\n          begin: /\"\"/,\n          relevance: 0\n        }]\n      },\n      // characters ''\n      {\n        // character literals always contain one char\n        className: 'string',\n        begin: /'.'/\n      },\n      {\n        // number literals\n        className: 'number',\n        begin: NUMBER_RE,\n        relevance: 0\n      },\n      {\n        // Attributes\n        className: 'symbol',\n        begin: \"'\" + ID_REGEX\n      },\n      {\n        // package definition, maybe inside generic\n        className: 'title',\n        begin: '(\\\\bwith\\\\s+)?(\\\\bprivate\\\\s+)?\\\\bpackage\\\\s+(\\\\bbody\\\\s+)?',\n        end: '(is|$)',\n        keywords: 'package body',\n        excludeBegin: true,\n        excludeEnd: true,\n        illegal: BAD_CHARS\n      },\n      {\n        // function/procedure declaration/definition\n        // maybe inside generic\n        begin: '(\\\\b(with|overriding)\\\\s+)?\\\\b(function|procedure)\\\\s+',\n        end: '(\\\\bis|\\\\bwith|\\\\brenames|\\\\)\\\\s*;)',\n        keywords: 'overriding function procedure with is renames return',\n        // we need to re-match the 'function' keyword, so that\n        // the title mode below matches only exactly once\n        returnBegin: true,\n        contains:\n                [\n                  COMMENTS,\n                  {\n                    // name of the function/procedure\n                    className: 'title',\n                    begin: '(\\\\bwith\\\\s+)?\\\\b(function|procedure)\\\\s+',\n                    end: '(\\\\(|\\\\s+|$)',\n                    excludeBegin: true,\n                    excludeEnd: true,\n                    illegal: BAD_CHARS\n                  },\n                  // 'self'\n                  // // parameter types\n                  VAR_DECLS,\n                  {\n                    // return type\n                    className: 'type',\n                    begin: '\\\\breturn\\\\s+',\n                    end: '(\\\\s+|;|$)',\n                    keywords: 'return',\n                    excludeBegin: true,\n                    excludeEnd: true,\n                    // we are done with functions\n                    endsParent: true,\n                    illegal: BAD_CHARS\n\n                  }\n                ]\n      },\n      {\n        // new type declarations\n        // maybe inside generic\n        className: 'type',\n        begin: '\\\\b(sub)?type\\\\s+',\n        end: '\\\\s+',\n        keywords: 'type',\n        excludeBegin: true,\n        illegal: BAD_CHARS\n      },\n\n      // see comment above the definition\n      VAR_DECLS\n\n      // no markup\n      // relevance boosters for small snippets\n      // {begin: '\\\\s*=>\\\\s*'},\n      // {begin: '\\\\s*:=\\\\s*'},\n      // {begin: '\\\\s+:=\\\\s+'},\n    ]\n  };\n}\n\nmodule.exports = ada;\n"], "mappings": ";;;;;AAAA;AAAA;AAoBA,aAAS,IAAI,MAAM;AAKjB,YAAM,aAAa;AACnB,YAAM,cAAc,cAAc;AAClC,YAAM,qBAAqB,aAAa,SAAS,aAAa,QAAa,cAAc;AAGzF,YAAM,mBAAmB;AACzB,YAAM,mBAAmB,aAAa,MAAM,mBAAmB,SAAS,mBAAmB,SAAmB,cAAc;AAE5H,YAAM,YAAY,SAAS,mBAAmB,MAAM,qBAAqB;AAGzE,YAAM,WAAW;AAGjB,YAAM,YAAY;AAGlB,YAAM,WAAW,KAAK,QAAQ,MAAM,GAAG;AAKvC,YAAM,YAAY;AAAA;AAAA;AAAA;AAAA,QAIhB,OAAO;AAAA,QACP,KAAK;AAAA;AAAA;AAAA,QAGL,SAAS;AAAA,QACT,UAAU;AAAA,UACR;AAAA;AAAA;AAAA,YAGE,eAAe;AAAA,YACf,YAAY;AAAA,UACd;AAAA,UACA;AAAA;AAAA,YAEE,WAAW;AAAA,YACX,eAAe;AAAA,UACjB;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,YAAY;AAAA,YACZ,WAAW;AAAA,UACb;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,kBAAkB;AAAA,QAClB,UAAU;AAAA,UACR,SACU;AAAA,UAOV,SACU;AAAA,QACZ;AAAA,QACA,UAAU;AAAA,UACR;AAAA;AAAA,UAEA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU,CAAC;AAAA,cACT,OAAO;AAAA,cACP,WAAW;AAAA,YACb,CAAC;AAAA,UACH;AAAA;AAAA,UAEA;AAAA;AAAA,YAEE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA,UACA;AAAA;AAAA,YAEE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA;AAAA;AAAA,YAEE,WAAW;AAAA,YACX,OAAO,MAAM;AAAA,UACf;AAAA,UACA;AAAA;AAAA,YAEE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,YACV,cAAc;AAAA,YACd,YAAY;AAAA,YACZ,SAAS;AAAA,UACX;AAAA,UACA;AAAA;AAAA;AAAA,YAGE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA;AAAA;AAAA,YAGV,aAAa;AAAA,YACb,UACQ;AAAA,cACE;AAAA,cACA;AAAA;AAAA,gBAEE,WAAW;AAAA,gBACX,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,cAAc;AAAA,gBACd,YAAY;AAAA,gBACZ,SAAS;AAAA,cACX;AAAA;AAAA;AAAA,cAGA;AAAA,cACA;AAAA;AAAA,gBAEE,WAAW;AAAA,gBACX,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,UAAU;AAAA,gBACV,cAAc;AAAA,gBACd,YAAY;AAAA;AAAA,gBAEZ,YAAY;AAAA,gBACZ,SAAS;AAAA,cAEX;AAAA,YACF;AAAA,UACV;AAAA,UACA;AAAA;AAAA;AAAA,YAGE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,YACV,cAAc;AAAA,YACd,SAAS;AAAA,UACX;AAAA;AAAA,UAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAOF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}