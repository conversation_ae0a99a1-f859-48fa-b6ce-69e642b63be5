/**
 * ⚙️ CONFIG MANAGER
 * Gère la configuration de l'assistant de manière isolée
 */

import { useState, useCallback, useEffect } from 'react';
import { claraApiService } from '../../../services/claraApiService';
import { claraMCPService } from '../../../services/claraMCPService';
import { db } from '../../../db';
import type { 
  ClaraSessionConfig, 
  ClaraProvider, 
  ClaraModel,
  ClaraAIConfig 
} from '../../../types/clara_assistant_types';

export interface ConfigManagerState {
  sessionConfig: ClaraSessionConfig;
  providers: ClaraProvider[];
  models: ClaraModel[];
  isLoadingProviders: boolean;
  isRefreshing: boolean;
  lastRefreshTime: number;
  providerHealthCache: Map<string, { isHealthy: boolean; timestamp: number }>;
}

export interface ConfigManagerActions {
  updateSessionConfig: (updates: Partial<ClaraSessionConfig>) => void;
  handleProviderChange: (providerId: string) => Promise<void>;
  handleModelChange: (modelId: string, type: 'text' | 'vision' | 'code') => void;
  refreshProvidersAndServices: (force?: boolean) => Promise<void>;
  checkProviderHealthCached: (provider: ClaraProvider) => Promise<boolean>;
  clearProviderHealthCache: (providerId?: string) => void;
  preloadModel: () => Promise<void>;
}

const HEALTH_CHECK_CACHE_TIME = 30000; // 30 secondes
const REFRESH_COOLDOWN = 30000; // 30 secondes

const getDefaultSystemPrompt = (provider: ClaraProvider): string => {
  const providerName = provider?.name || 'AI Assistant';
  
  switch (provider?.type) {
    case 'ollama':
      return `You are Clara, a helpful AI assistant powered by ${providerName}. You are knowledgeable, friendly, and provide accurate information. You can help with various tasks including analysis, coding, writing, and general questions. When using tools, be thorough and explain your actions clearly.`;
      
    case 'openai':
      return `You are Clara, an intelligent AI assistant powered by OpenAI. You are helpful, harmless, and honest. You excel at reasoning, analysis, creative tasks, and problem-solving. Always strive to provide accurate, well-structured responses and use available tools effectively when needed.`;
      
    case 'openrouter':
      return `You are Clara, a versatile AI assistant with access to various models through OpenRouter. You adapt your communication style based on the task at hand and leverage the strengths of different AI models. Be helpful, accurate, and efficient in your responses.`;
      
    case 'lmstudio':
      return `You are Clara, a powerful AI assistant running through LM Studio. You provide intelligent, helpful responses while maintaining high performance. You excel at complex reasoning, creative tasks, and technical assistance.`;
      
    default:
      return `You are Clara, a helpful AI assistant. You are knowledgeable, friendly, and provide accurate information. You can help with various tasks including analysis, coding, writing, and general questions. Always be helpful and respectful in your interactions.`;
  }
};

export const useConfigManager = (): ConfigManagerState & ConfigManagerActions => {
  // État de configuration
  const [sessionConfig, setSessionConfig] = useState<ClaraSessionConfig>({
    aiConfig: {
      models: {
        text: '',
        vision: '',
        code: ''
      },
      provider: '',
      parameters: {
        temperature: 0.7,
        maxTokens: 8000,
        topP: 1.0,
        topK: 40
      },
      features: {
        enableTools: false,
        enableRAG: true,
        enableStreaming: true,
        enableVision: true,
        autoModelSelection: true,
        enableMCP: false
      },
      mcp: {
        enableTools: true,
        enableResources: true,
        enabledServers: [],
        autoDiscoverTools: true,
        maxToolCalls: 5
      },
      autonomousAgent: {
        enabled: false,
        maxRetries: 1,
        retryDelay: 0,
        enableSelfCorrection: true,
        enableToolGuidance: true,
        enableProgressTracking: true,
        maxToolCalls: 10,
        confidenceThreshold: 0.7,
        enableChainOfThought: true,
        enableErrorLearning: true
      }
    },
    contextWindow: 50
  });

  // État des providers et modèles
  const [providers, setProviders] = useState<ClaraProvider[]>([]);
  const [models, setModels] = useState<ClaraModel[]>([]);
  const [isLoadingProviders, setIsLoadingProviders] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [lastRefreshTime, setLastRefreshTime] = useState<number>(0);
  const [providerHealthCache, setProviderHealthCache] = useState<Map<string, { isHealthy: boolean; timestamp: number }>>(new Map());

  // Mettre à jour la configuration de session
  const updateSessionConfig = useCallback((updates: Partial<ClaraSessionConfig>) => {
    setSessionConfig(prev => {
      const newConfig = { ...prev, ...updates };
      
      // Fusionner les configurations AI si nécessaire
      if (updates.aiConfig) {
        newConfig.aiConfig = { ...prev.aiConfig, ...updates.aiConfig };
      }
      
      return newConfig;
    });
  }, []);

  // Vérification de santé des providers avec cache
  const checkProviderHealthCached = useCallback(async (provider: ClaraProvider): Promise<boolean> => {
    const now = Date.now();
    const cached = providerHealthCache.get(provider.id);
    
    // Retourner le résultat en cache s'il est encore valide
    if (cached && (now - cached.timestamp < HEALTH_CHECK_CACHE_TIME)) {
      console.log(`✅ Using cached health status for ${provider.name}: ${cached.isHealthy}`);
      return cached.isHealthy;
    }
    
    // Effectuer une vérification de santé réelle
    console.log(`🏥 Performing health check for ${provider.name}...`);
    try {
      const isHealthy = await claraApiService.testProvider(provider);
      
      // Mettre en cache le résultat
      setProviderHealthCache(prev => {
        const newCache = new Map(prev);
        newCache.set(provider.id, { isHealthy, timestamp: now });
        return newCache;
      });
      
      console.log(`${isHealthy ? '✅' : '❌'} Health check result for ${provider.name}: ${isHealthy}`);
      return isHealthy;
    } catch (error) {
      console.warn(`⚠️ Health check failed for ${provider.name}:`, error);
      
      // Mettre en cache l'échec
      setProviderHealthCache(prev => {
        const newCache = new Map(prev);
        newCache.set(provider.id, { isHealthy: false, timestamp: now });
        return newCache;
      });
      
      return false;
    }
  }, [providerHealthCache]);

  // Rafraîchir les providers et services
  const refreshProvidersAndServices = useCallback(async (force: boolean = false) => {
    const now = Date.now();
    const timeSinceLastRefresh = now - lastRefreshTime;
    
    // Éviter les rafraîchissements trop fréquents sauf si forcé
    if (!force && timeSinceLastRefresh < REFRESH_COOLDOWN) {
      console.log(`⏳ Skipping refresh - last refresh was ${Math.round(timeSinceLastRefresh / 1000)}s ago`);
      return;
    }
    
    if (isRefreshing) {
      console.log('🔄 Refresh already in progress, skipping...');
      return;
    }
    
    setIsRefreshing(true);
    setLastRefreshTime(now);
    
    try {
      console.log('🔄 Refreshing providers, models, and services...');
      
      // Rafraîchir le service MCP
      try {
        console.log('🔧 Refreshing MCP services...');
        await claraMCPService.refresh();
        console.log('✅ MCP services refreshed');
      } catch (mcpError) {
        console.warn('⚠️ MCP refresh failed:', mcpError);
      }

      // Recharger les providers
      console.log('🏢 Refreshing providers...');
      const refreshedProviders = await claraApiService.getProviders();
      setProviders(refreshedProviders);
      console.log(`✅ Loaded ${refreshedProviders.length} providers`);

      // Charger les modèles de TOUS les providers
      let allModels: ClaraModel[] = [];
      for (const provider of refreshedProviders) {
        try {
          const providerModels = await claraApiService.getModels(provider.id);
          allModels = [...allModels, ...providerModels];
          console.log(`📦 Loaded ${providerModels.length} models from ${provider.name}`);
        } catch (error) {
          console.warn(`⚠️ Failed to load models from ${provider.name}:`, error);
        }
      }
      
      setModels(allModels);
      console.log(`✅ Total models refreshed: ${allModels.length}`);

      // Mettre à jour le provider actuel si nécessaire
      const currentProviderId = sessionConfig.aiConfig?.provider;
      if (currentProviderId) {
        const currentProvider = refreshedProviders.find(p => p.id === currentProviderId);
        if (currentProvider) {
          claraApiService.updateProvider(currentProvider);
          console.log(`🔧 Updated current provider: ${currentProvider.name}`);
        }
      }

      console.log('✅ Providers and services refresh complete');
      
    } catch (error) {
      console.error('❌ Failed to refresh providers and services:', error);
    } finally {
      setIsRefreshing(false);
    }
  }, [lastRefreshTime, isRefreshing, sessionConfig.aiConfig?.provider]);

  // Gérer le changement de provider
  const handleProviderChange = useCallback(async (providerId: string) => {
    try {
      console.log(`🔄 Switching to provider: ${providerId}`);
      
      const selectedProvider = providers.find(p => p.id === providerId);
      if (!selectedProvider) {
        console.error(`❌ Provider not found: ${providerId}`);
        return;
      }

      // Mettre à jour la configuration
      updateSessionConfig({
        aiConfig: {
          ...sessionConfig.aiConfig,
          provider: providerId,
          systemPrompt: getDefaultSystemPrompt(selectedProvider)
        }
      });

      // Mettre à jour le service API
      claraApiService.updateProvider(selectedProvider);
      
      console.log(`✅ Switched to provider: ${selectedProvider.name}`);
    } catch (error) {
      console.error('❌ Failed to change provider:', error);
    }
  }, [providers, sessionConfig.aiConfig, updateSessionConfig]);

  // Gérer le changement de modèle
  const handleModelChange = useCallback((modelId: string, type: 'text' | 'vision' | 'code') => {
    updateSessionConfig({
      aiConfig: {
        ...sessionConfig.aiConfig,
        models: {
          ...sessionConfig.aiConfig.models,
          [type]: modelId
        }
      }
    });
    
    console.log(`✅ Model changed for ${type}: ${modelId}`);
  }, [sessionConfig.aiConfig, updateSessionConfig]);

  // Vider le cache de santé des providers
  const clearProviderHealthCache = useCallback((providerId?: string) => {
    if (providerId) {
      setProviderHealthCache(prev => {
        const newCache = new Map(prev);
        newCache.delete(providerId);
        return newCache;
      });
    } else {
      setProviderHealthCache(new Map());
    }
  }, []);

  // Précharger le modèle
  const preloadModel = useCallback(async () => {
    if (!sessionConfig.aiConfig) return;

    try {
      console.log('🚀 Preloading model...');
      await claraApiService.preloadModel(sessionConfig.aiConfig);
      console.log('✅ Model preloaded successfully');
    } catch (error) {
      console.log(`🔄 Model preload failed (non-critical): ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }, [sessionConfig.aiConfig]);

  // Charger les providers et modèles au démarrage
  useEffect(() => {
    const loadProvidersAndModels = async () => {
      try {
        setIsLoadingProviders(true);
        await refreshProvidersAndServices(true);
      } catch (error) {
        console.error('❌ Failed to load providers and models:', error);
      } finally {
        setIsLoadingProviders(false);
      }
    };

    loadProvidersAndModels();
  }, []);

  return {
    // État
    sessionConfig,
    providers,
    models,
    isLoadingProviders,
    isRefreshing,
    lastRefreshTime,
    providerHealthCache,
    
    // Actions
    updateSessionConfig,
    handleProviderChange,
    handleModelChange,
    refreshProvidersAndServices,
    checkProviderHealthCached,
    clearProviderHealthCache,
    preloadModel
  };
};
