{"version": 3, "sources": ["../../refractor/lang/go.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = go\ngo.displayName = 'go'\ngo.aliases = []\nfunction go(Prism) {\n  Prism.languages.go = Prism.languages.extend('clike', {\n    string: {\n      pattern: /(^|[^\\\\])\"(?:\\\\.|[^\"\\\\\\r\\n])*\"|`[^`]*`/,\n      lookbehind: true,\n      greedy: true\n    },\n    keyword:\n      /\\b(?:break|case|chan|const|continue|default|defer|else|fallthrough|for|func|go(?:to)?|if|import|interface|map|package|range|return|select|struct|switch|type|var)\\b/,\n    boolean: /\\b(?:_|false|iota|nil|true)\\b/,\n    number: [\n      // binary and octal integers\n      /\\b0(?:b[01_]+|o[0-7_]+)i?\\b/i, // hexadecimal integers and floats\n      /\\b0x(?:[a-f\\d_]+(?:\\.[a-f\\d_]*)?|\\.[a-f\\d_]+)(?:p[+-]?\\d+(?:_\\d+)*)?i?(?!\\w)/i, // decimal integers and floats\n      /(?:\\b\\d[\\d_]*(?:\\.[\\d_]*)?|\\B\\.\\d[\\d_]*)(?:e[+-]?[\\d_]+)?i?(?!\\w)/i\n    ],\n    operator:\n      /[*\\/%^!=]=?|\\+[=+]?|-[=-]?|\\|[=|]?|&(?:=|&|\\^=?)?|>(?:>=?|=)?|<(?:<=?|=|-)?|:=|\\.\\.\\./,\n    builtin:\n      /\\b(?:append|bool|byte|cap|close|complex|complex(?:64|128)|copy|delete|error|float(?:32|64)|u?int(?:8|16|32|64)?|imag|len|make|new|panic|print(?:ln)?|real|recover|rune|string|uintptr)\\b/\n  })\n  Prism.languages.insertBefore('go', 'string', {\n    char: {\n      pattern: /'(?:\\\\.|[^'\\\\\\r\\n]){0,10}'/,\n      greedy: true\n    }\n  })\n  delete Prism.languages.go['class-name']\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,OAAG,cAAc;AACjB,OAAG,UAAU,CAAC;AACd,aAAS,GAAG,OAAO;AACjB,YAAM,UAAU,KAAK,MAAM,UAAU,OAAO,SAAS;AAAA,QACnD,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,QAAQ;AAAA,QACV;AAAA,QACA,SACE;AAAA,QACF,SAAS;AAAA,QACT,QAAQ;AAAA;AAAA,UAEN;AAAA;AAAA,UACA;AAAA;AAAA,UACA;AAAA,QACF;AAAA,QACA,UACE;AAAA,QACF,SACE;AAAA,MACJ,CAAC;AACD,YAAM,UAAU,aAAa,MAAM,UAAU;AAAA,QAC3C,MAAM;AAAA,UACJ,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,MACF,CAAC;AACD,aAAO,MAAM,UAAU,GAAG,YAAY;AAAA,IACxC;AAAA;AAAA;", "names": []}