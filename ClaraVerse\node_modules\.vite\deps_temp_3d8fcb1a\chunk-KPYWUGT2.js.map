{"version": 3, "sources": ["../../highlight.js/lib/languages/processing.js"], "sourcesContent": ["/*\nLanguage: Processing\nDescription: Processing is a flexible software sketchbook and a language for learning how to code within the context of the visual arts.\nAuthor: <PERSON> <<EMAIL>>\nWebsite: https://processing.org\nCategory: graphics\n*/\n\nfunction processing(hljs) {\n  return {\n    name: 'Processing',\n    keywords: {\n      keyword: 'BufferedReader PVector PFont PImage PGraphics HashMap boolean byte char color ' +\n        'double float int long String Array FloatDict FloatList IntDict IntList JSONArray JSONObject ' +\n        'Object StringDict StringList Table TableRow XML ' +\n        // Java keywords\n        'false synchronized int abstract float private char boolean static null if const ' +\n        'for true while long throw strictfp finally protected import native final return void ' +\n        'enum else break transient new catch instanceof byte super volatile case assert short ' +\n        'package default double public try this switch continue throws protected public private',\n      literal: 'P2D P3D HALF_PI PI QUARTER_PI TAU TWO_PI',\n      title: 'setup draw',\n      built_in: 'displayHeight displayWidth mouseY mouseX mousePressed pmouseX pmouseY key ' +\n        'keyCode pixels focused frameCount frameRate height width ' +\n        'size createGraphics beginDraw createShape loadShape PShape arc ellipse line point ' +\n        'quad rect triangle bezier bezierDetail bezierPoint bezierTangent curve curveDetail curvePoint ' +\n        'curveTangent curveTightness shape shapeMode beginContour beginShape bezierVertex curveVertex ' +\n        'endContour endShape quadraticVertex vertex ellipseMode noSmooth rectMode smooth strokeCap ' +\n        'strokeJoin strokeWeight mouseClicked mouseDragged mouseMoved mousePressed mouseReleased ' +\n        'mouseWheel keyPressed keyPressedkeyReleased keyTyped print println save saveFrame day hour ' +\n        'millis minute month second year background clear colorMode fill noFill noStroke stroke alpha ' +\n        'blue brightness color green hue lerpColor red saturation modelX modelY modelZ screenX screenY ' +\n        'screenZ ambient emissive shininess specular add createImage beginCamera camera endCamera frustum ' +\n        'ortho perspective printCamera printProjection cursor frameRate noCursor exit loop noLoop popStyle ' +\n        'pushStyle redraw binary boolean byte char float hex int str unbinary unhex join match matchAll nf ' +\n        'nfc nfp nfs split splitTokens trim append arrayCopy concat expand reverse shorten sort splice subset ' +\n        'box sphere sphereDetail createInput createReader loadBytes loadJSONArray loadJSONObject loadStrings ' +\n        'loadTable loadXML open parseXML saveTable selectFolder selectInput beginRaw beginRecord createOutput ' +\n        'createWriter endRaw endRecord PrintWritersaveBytes saveJSONArray saveJSONObject saveStream saveStrings ' +\n        'saveXML selectOutput popMatrix printMatrix pushMatrix resetMatrix rotate rotateX rotateY rotateZ scale ' +\n        'shearX shearY translate ambientLight directionalLight lightFalloff lights lightSpecular noLights normal ' +\n        'pointLight spotLight image imageMode loadImage noTint requestImage tint texture textureMode textureWrap ' +\n        'blend copy filter get loadPixels set updatePixels blendMode loadShader PShaderresetShader shader createFont ' +\n        'loadFont text textFont textAlign textLeading textMode textSize textWidth textAscent textDescent abs ceil ' +\n        'constrain dist exp floor lerp log mag map max min norm pow round sq sqrt acos asin atan atan2 cos degrees ' +\n        'radians sin tan noise noiseDetail noiseSeed random randomGaussian randomSeed'\n    },\n    contains: [\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE,\n      hljs.C_NUMBER_MODE\n    ]\n  };\n}\n\nmodule.exports = processing;\n"], "mappings": ";;;;;AAAA;AAAA;AAQA,aAAS,WAAW,MAAM;AACxB,aAAO;AAAA,QACL,MAAM;AAAA,QACN,UAAU;AAAA,UACR,SAAS;AAAA,UAQT,SAAS;AAAA,UACT,OAAO;AAAA,UACP,UAAU;AAAA,QAwBZ;AAAA,QACA,UAAU;AAAA,UACR,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,QACP;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}