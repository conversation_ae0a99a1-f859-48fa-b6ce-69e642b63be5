{"version": 3, "sources": ["../../highlight.js/lib/languages/apache.js"], "sourcesContent": ["/*\nLanguage: Apache config\nAuthor: <PERSON><PERSON><PERSON> <<EMAIL>>\nContributors: <PERSON> <<EMAIL>>\nWebsite: https://httpd.apache.org\nDescription: language definition for Apache configuration files (httpd.conf & .htaccess)\nCategory: common, config\nAudit: 2020\n*/\n\n/** @type LanguageFn */\nfunction apache(hljs) {\n  const NUMBER_REF = {\n    className: 'number',\n    begin: /[$%]\\d+/\n  };\n  const NUMBER = {\n    className: 'number',\n    begin: /\\d+/\n  };\n  const IP_ADDRESS = {\n    className: \"number\",\n    begin: /\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}(:\\d{1,5})?/\n  };\n  const PORT_NUMBER = {\n    className: \"number\",\n    begin: /:\\d{1,5}/\n  };\n  return {\n    name: 'Apache config',\n    aliases: [ 'apacheconf' ],\n    case_insensitive: true,\n    contains: [\n      hljs.HASH_COMMENT_MODE,\n      {\n        className: 'section',\n        begin: /<\\/?/,\n        end: />/,\n        contains: [\n          IP_ADDRESS,\n          PORT_NUMBER,\n          // low relevance prevents us from claming XML/HTML where this rule would\n          // match strings inside of XML tags\n          hljs.inherit(hljs.QUOTE_STRING_MODE, { relevance: 0 })\n        ]\n      },\n      {\n        className: 'attribute',\n        begin: /\\w+/,\n        relevance: 0,\n        // keywords aren’t needed for highlighting per se, they only boost relevance\n        // for a very generally defined mode (starts with a word, ends with line-end\n        keywords: {\n          nomarkup:\n            'order deny allow setenv rewriterule rewriteengine rewritecond documentroot ' +\n            'sethandler errordocument loadmodule options header listen serverroot ' +\n            'servername'\n        },\n        starts: {\n          end: /$/,\n          relevance: 0,\n          keywords: { literal: 'on off all deny allow' },\n          contains: [\n            {\n              className: 'meta',\n              begin: /\\s\\[/,\n              end: /\\]$/\n            },\n            {\n              className: 'variable',\n              begin: /[\\$%]\\{/,\n              end: /\\}/,\n              contains: [\n                'self',\n                NUMBER_REF\n              ]\n            },\n            IP_ADDRESS,\n            NUMBER,\n            hljs.QUOTE_STRING_MODE\n          ]\n        }\n      }\n    ],\n    illegal: /\\S/\n  };\n}\n\nmodule.exports = apache;\n"], "mappings": ";;;;;AAAA;AAAA;AAWA,aAAS,OAAO,MAAM;AACpB,YAAM,aAAa;AAAA,QACjB,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AACA,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AACA,YAAM,aAAa;AAAA,QACjB,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AACA,YAAM,cAAc;AAAA,QAClB,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAE,YAAa;AAAA,QACxB,kBAAkB;AAAA,QAClB,UAAU;AAAA,UACR,KAAK;AAAA,UACL;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,cACR;AAAA,cACA;AAAA;AAAA;AAAA,cAGA,KAAK,QAAQ,KAAK,mBAAmB,EAAE,WAAW,EAAE,CAAC;AAAA,YACvD;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,WAAW;AAAA;AAAA;AAAA,YAGX,UAAU;AAAA,cACR,UACE;AAAA,YAGJ;AAAA,YACA,QAAQ;AAAA,cACN,KAAK;AAAA,cACL,WAAW;AAAA,cACX,UAAU,EAAE,SAAS,wBAAwB;AAAA,cAC7C,UAAU;AAAA,gBACR;AAAA,kBACE,WAAW;AAAA,kBACX,OAAO;AAAA,kBACP,KAAK;AAAA,gBACP;AAAA,gBACA;AAAA,kBACE,WAAW;AAAA,kBACX,OAAO;AAAA,kBACP,KAAK;AAAA,kBACL,UAAU;AAAA,oBACR;AAAA,oBACA;AAAA,kBACF;AAAA,gBACF;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA,KAAK;AAAA,cACP;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,SAAS;AAAA,MACX;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}