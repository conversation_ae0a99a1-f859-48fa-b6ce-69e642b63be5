{"version": 3, "sources": ["../../highlight.js/lib/languages/perl.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/**\n * Any of the passed expresssions may match\n *\n * Creates a huge this | this | that | that match\n * @param {(RegExp | string)[] } args\n * @returns {string}\n */\nfunction either(...args) {\n  const joined = '(' + args.map((x) => source(x)).join(\"|\") + \")\";\n  return joined;\n}\n\n/*\nLanguage: Perl\nAuthor: <PERSON> <<EMAIL>>\nWebsite: https://www.perl.org\nCategory: common\n*/\n\n/** @type LanguageFn */\nfunction perl(hljs) {\n  const KEYWORDS = [\n    'abs',\n    'accept',\n    'alarm',\n    'and',\n    'atan2',\n    'bind',\n    'binmode',\n    'bless',\n    'break',\n    'caller',\n    'chdir',\n    'chmod',\n    'chomp',\n    'chop',\n    'chown',\n    'chr',\n    'chroot',\n    'close',\n    'closedir',\n    'connect',\n    'continue',\n    'cos',\n    'crypt',\n    'dbmclose',\n    'dbmopen',\n    'defined',\n    'delete',\n    'die',\n    'do',\n    'dump',\n    'each',\n    'else',\n    'elsif',\n    'endgrent',\n    'endhostent',\n    'endnetent',\n    'endprotoent',\n    'endpwent',\n    'endservent',\n    'eof',\n    'eval',\n    'exec',\n    'exists',\n    'exit',\n    'exp',\n    'fcntl',\n    'fileno',\n    'flock',\n    'for',\n    'foreach',\n    'fork',\n    'format',\n    'formline',\n    'getc',\n    'getgrent',\n    'getgrgid',\n    'getgrnam',\n    'gethostbyaddr',\n    'gethostbyname',\n    'gethostent',\n    'getlogin',\n    'getnetbyaddr',\n    'getnetbyname',\n    'getnetent',\n    'getpeername',\n    'getpgrp',\n    'getpriority',\n    'getprotobyname',\n    'getprotobynumber',\n    'getprotoent',\n    'getpwent',\n    'getpwnam',\n    'getpwuid',\n    'getservbyname',\n    'getservbyport',\n    'getservent',\n    'getsockname',\n    'getsockopt',\n    'given',\n    'glob',\n    'gmtime',\n    'goto',\n    'grep',\n    'gt',\n    'hex',\n    'if',\n    'index',\n    'int',\n    'ioctl',\n    'join',\n    'keys',\n    'kill',\n    'last',\n    'lc',\n    'lcfirst',\n    'length',\n    'link',\n    'listen',\n    'local',\n    'localtime',\n    'log',\n    'lstat',\n    'lt',\n    'ma',\n    'map',\n    'mkdir',\n    'msgctl',\n    'msgget',\n    'msgrcv',\n    'msgsnd',\n    'my',\n    'ne',\n    'next',\n    'no',\n    'not',\n    'oct',\n    'open',\n    'opendir',\n    'or',\n    'ord',\n    'our',\n    'pack',\n    'package',\n    'pipe',\n    'pop',\n    'pos',\n    'print',\n    'printf',\n    'prototype',\n    'push',\n    'q|0',\n    'qq',\n    'quotemeta',\n    'qw',\n    'qx',\n    'rand',\n    'read',\n    'readdir',\n    'readline',\n    'readlink',\n    'readpipe',\n    'recv',\n    'redo',\n    'ref',\n    'rename',\n    'require',\n    'reset',\n    'return',\n    'reverse',\n    'rewinddir',\n    'rindex',\n    'rmdir',\n    'say',\n    'scalar',\n    'seek',\n    'seekdir',\n    'select',\n    'semctl',\n    'semget',\n    'semop',\n    'send',\n    'setgrent',\n    'sethostent',\n    'setnetent',\n    'setpgrp',\n    'setpriority',\n    'setprotoent',\n    'setpwent',\n    'setservent',\n    'setsockopt',\n    'shift',\n    'shmctl',\n    'shmget',\n    'shmread',\n    'shmwrite',\n    'shutdown',\n    'sin',\n    'sleep',\n    'socket',\n    'socketpair',\n    'sort',\n    'splice',\n    'split',\n    'sprintf',\n    'sqrt',\n    'srand',\n    'stat',\n    'state',\n    'study',\n    'sub',\n    'substr',\n    'symlink',\n    'syscall',\n    'sysopen',\n    'sysread',\n    'sysseek',\n    'system',\n    'syswrite',\n    'tell',\n    'telldir',\n    'tie',\n    'tied',\n    'time',\n    'times',\n    'tr',\n    'truncate',\n    'uc',\n    'ucfirst',\n    'umask',\n    'undef',\n    'unless',\n    'unlink',\n    'unpack',\n    'unshift',\n    'untie',\n    'until',\n    'use',\n    'utime',\n    'values',\n    'vec',\n    'wait',\n    'waitpid',\n    'wantarray',\n    'warn',\n    'when',\n    'while',\n    'write',\n    'x|0',\n    'xor',\n    'y|0'\n  ];\n\n  // https://perldoc.perl.org/perlre#Modifiers\n  const REGEX_MODIFIERS = /[dualxmsipngr]{0,12}/; // aa and xx are valid, making max length 12\n  const PERL_KEYWORDS = {\n    $pattern: /[\\w.]+/,\n    keyword: KEYWORDS.join(\" \")\n  };\n  const SUBST = {\n    className: 'subst',\n    begin: '[$@]\\\\{',\n    end: '\\\\}',\n    keywords: PERL_KEYWORDS\n  };\n  const METHOD = {\n    begin: /->\\{/,\n    end: /\\}/\n    // contains defined later\n  };\n  const VAR = {\n    variants: [\n      {\n        begin: /\\$\\d/\n      },\n      {\n        begin: concat(\n          /[$%@](\\^\\w\\b|#\\w+(::\\w+)*|\\{\\w+\\}|\\w+(::\\w*)*)/,\n          // negative look-ahead tries to avoid matching patterns that are not\n          // Perl at all like $ident$, @ident@, etc.\n          `(?![A-Za-z])(?![@$%])`\n        )\n      },\n      {\n        begin: /[$%@][^\\s\\w{]/,\n        relevance: 0\n      }\n    ]\n  };\n  const STRING_CONTAINS = [\n    hljs.BACKSLASH_ESCAPE,\n    SUBST,\n    VAR\n  ];\n  const REGEX_DELIMS = [\n    /!/,\n    /\\//,\n    /\\|/,\n    /\\?/,\n    /'/,\n    /\"/, // valid but infrequent and weird\n    /#/ // valid but infrequent and weird\n  ];\n  /**\n   * @param {string|RegExp} prefix\n   * @param {string|RegExp} open\n   * @param {string|RegExp} close\n   */\n  const PAIRED_DOUBLE_RE = (prefix, open, close = '\\\\1') => {\n    const middle = (close === '\\\\1')\n      ? close\n      : concat(close, open);\n    return concat(\n      concat(\"(?:\", prefix, \")\"),\n      open,\n      /(?:\\\\.|[^\\\\\\/])*?/,\n      middle,\n      /(?:\\\\.|[^\\\\\\/])*?/,\n      close,\n      REGEX_MODIFIERS\n    );\n  };\n  /**\n   * @param {string|RegExp} prefix\n   * @param {string|RegExp} open\n   * @param {string|RegExp} close\n   */\n  const PAIRED_RE = (prefix, open, close) => {\n    return concat(\n      concat(\"(?:\", prefix, \")\"),\n      open,\n      /(?:\\\\.|[^\\\\\\/])*?/,\n      close,\n      REGEX_MODIFIERS\n    );\n  };\n  const PERL_DEFAULT_CONTAINS = [\n    VAR,\n    hljs.HASH_COMMENT_MODE,\n    hljs.COMMENT(\n      /^=\\w/,\n      /=cut/,\n      {\n        endsWithParent: true\n      }\n    ),\n    METHOD,\n    {\n      className: 'string',\n      contains: STRING_CONTAINS,\n      variants: [\n        {\n          begin: 'q[qwxr]?\\\\s*\\\\(',\n          end: '\\\\)',\n          relevance: 5\n        },\n        {\n          begin: 'q[qwxr]?\\\\s*\\\\[',\n          end: '\\\\]',\n          relevance: 5\n        },\n        {\n          begin: 'q[qwxr]?\\\\s*\\\\{',\n          end: '\\\\}',\n          relevance: 5\n        },\n        {\n          begin: 'q[qwxr]?\\\\s*\\\\|',\n          end: '\\\\|',\n          relevance: 5\n        },\n        {\n          begin: 'q[qwxr]?\\\\s*<',\n          end: '>',\n          relevance: 5\n        },\n        {\n          begin: 'qw\\\\s+q',\n          end: 'q',\n          relevance: 5\n        },\n        {\n          begin: '\\'',\n          end: '\\'',\n          contains: [ hljs.BACKSLASH_ESCAPE ]\n        },\n        {\n          begin: '\"',\n          end: '\"'\n        },\n        {\n          begin: '`',\n          end: '`',\n          contains: [ hljs.BACKSLASH_ESCAPE ]\n        },\n        {\n          begin: /\\{\\w+\\}/,\n          relevance: 0\n        },\n        {\n          begin: '-?\\\\w+\\\\s*=>',\n          relevance: 0\n        }\n      ]\n    },\n    {\n      className: 'number',\n      begin: '(\\\\b0[0-7_]+)|(\\\\b0x[0-9a-fA-F_]+)|(\\\\b[1-9][0-9_]*(\\\\.[0-9_]+)?)|[0_]\\\\b',\n      relevance: 0\n    },\n    { // regexp container\n      begin: '(\\\\/\\\\/|' + hljs.RE_STARTERS_RE + '|\\\\b(split|return|print|reverse|grep)\\\\b)\\\\s*',\n      keywords: 'split return print reverse grep',\n      relevance: 0,\n      contains: [\n        hljs.HASH_COMMENT_MODE,\n        {\n          className: 'regexp',\n          variants: [\n            // allow matching common delimiters\n            { begin: PAIRED_DOUBLE_RE(\"s|tr|y\", either(...REGEX_DELIMS)) },\n            // and then paired delmis\n            { begin: PAIRED_DOUBLE_RE(\"s|tr|y\", \"\\\\(\", \"\\\\)\") },\n            { begin: PAIRED_DOUBLE_RE(\"s|tr|y\", \"\\\\[\", \"\\\\]\") },\n            { begin: PAIRED_DOUBLE_RE(\"s|tr|y\", \"\\\\{\", \"\\\\}\") }\n          ],\n          relevance: 2\n        },\n        {\n          className: 'regexp',\n          variants: [\n            {\n              // could be a comment in many languages so do not count\n              // as relevant\n              begin: /(m|qr)\\/\\//,\n              relevance: 0\n            },\n            // prefix is optional with /regex/\n            { begin: PAIRED_RE(\"(?:m|qr)?\", /\\//, /\\//)},\n            // allow matching common delimiters\n            { begin: PAIRED_RE(\"m|qr\", either(...REGEX_DELIMS), /\\1/)},\n            // allow common paired delmins\n            { begin: PAIRED_RE(\"m|qr\", /\\(/, /\\)/)},\n            { begin: PAIRED_RE(\"m|qr\", /\\[/, /\\]/)},\n            { begin: PAIRED_RE(\"m|qr\", /\\{/, /\\}/)}\n          ]\n        }\n      ]\n    },\n    {\n      className: 'function',\n      beginKeywords: 'sub',\n      end: '(\\\\s*\\\\(.*?\\\\))?[;{]',\n      excludeEnd: true,\n      relevance: 5,\n      contains: [ hljs.TITLE_MODE ]\n    },\n    {\n      begin: '-\\\\w\\\\b',\n      relevance: 0\n    },\n    {\n      begin: \"^__DATA__$\",\n      end: \"^__END__$\",\n      subLanguage: 'mojolicious',\n      contains: [\n        {\n          begin: \"^@@.*\",\n          end: \"$\",\n          className: \"comment\"\n        }\n      ]\n    }\n  ];\n  SUBST.contains = PERL_DEFAULT_CONTAINS;\n  METHOD.contains = PERL_DEFAULT_CONTAINS;\n\n  return {\n    name: 'Perl',\n    aliases: [\n      'pl',\n      'pm'\n    ],\n    keywords: PERL_KEYWORDS,\n    contains: PERL_DEFAULT_CONTAINS\n  };\n}\n\nmodule.exports = perl;\n"], "mappings": ";;;;;AAAA;AAAA;AASA,aAAS,OAAO,IAAI;AAClB,UAAI,CAAC,GAAI,QAAO;AAChB,UAAI,OAAO,OAAO,SAAU,QAAO;AAEnC,aAAO,GAAG;AAAA,IACZ;AAMA,aAAS,UAAU,MAAM;AACvB,YAAM,SAAS,KAAK,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE;AACjD,aAAO;AAAA,IACT;AASA,aAAS,UAAU,MAAM;AACvB,YAAM,SAAS,MAAM,KAAK,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC,EAAE,KAAK,GAAG,IAAI;AAC5D,aAAO;AAAA,IACT;AAUA,aAAS,KAAK,MAAM;AAClB,YAAM,WAAW;AAAA,QACf;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAGA,YAAM,kBAAkB;AACxB,YAAM,gBAAgB;AAAA,QACpB,UAAU;AAAA,QACV,SAAS,SAAS,KAAK,GAAG;AAAA,MAC5B;AACA,YAAM,QAAQ;AAAA,QACZ,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,MACZ;AACA,YAAM,SAAS;AAAA,QACb,OAAO;AAAA,QACP,KAAK;AAAA;AAAA,MAEP;AACA,YAAM,MAAM;AAAA,QACV,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,OAAO;AAAA,cACL;AAAA;AAAA;AAAA,cAGA;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,QACF;AAAA,MACF;AACA,YAAM,kBAAkB;AAAA,QACtB,KAAK;AAAA,QACL;AAAA,QACA;AAAA,MACF;AACA,YAAM,eAAe;AAAA,QACnB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA;AAAA,QACA;AAAA;AAAA,MACF;AAMA,YAAM,mBAAmB,CAAC,QAAQ,MAAM,QAAQ,UAAU;AACxD,cAAM,SAAU,UAAU,QACtB,QACA,OAAO,OAAO,IAAI;AACtB,eAAO;AAAA,UACL,OAAO,OAAO,QAAQ,GAAG;AAAA,UACzB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAMA,YAAM,YAAY,CAAC,QAAQ,MAAM,UAAU;AACzC,eAAO;AAAA,UACL,OAAO,OAAO,QAAQ,GAAG;AAAA,UACzB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AACA,YAAM,wBAAwB;AAAA,QAC5B;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,UACH;AAAA,UACA;AAAA,UACA;AAAA,YACE,gBAAgB;AAAA,UAClB;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,UACE,WAAW;AAAA,UACX,UAAU;AAAA,UACV,UAAU;AAAA,YACR;AAAA,cACE,OAAO;AAAA,cACP,KAAK;AAAA,cACL,WAAW;AAAA,YACb;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,KAAK;AAAA,cACL,WAAW;AAAA,YACb;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,KAAK;AAAA,cACL,WAAW;AAAA,YACb;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,KAAK;AAAA,cACL,WAAW;AAAA,YACb;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,KAAK;AAAA,cACL,WAAW;AAAA,YACb;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,KAAK;AAAA,cACL,WAAW;AAAA,YACb;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,KAAK;AAAA,cACL,UAAU,CAAE,KAAK,gBAAiB;AAAA,YACpC;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,KAAK;AAAA,YACP;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,KAAK;AAAA,cACL,UAAU,CAAE,KAAK,gBAAiB;AAAA,YACpC;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,WAAW;AAAA,YACb;AAAA,YACA;AAAA,cACE,OAAO;AAAA,cACP,WAAW;AAAA,YACb;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE,WAAW;AAAA,UACX,OAAO;AAAA,UACP,WAAW;AAAA,QACb;AAAA,QACA;AAAA;AAAA,UACE,OAAO,aAAa,KAAK,iBAAiB;AAAA,UAC1C,UAAU;AAAA,UACV,WAAW;AAAA,UACX,UAAU;AAAA,YACR,KAAK;AAAA,YACL;AAAA,cACE,WAAW;AAAA,cACX,UAAU;AAAA;AAAA,gBAER,EAAE,OAAO,iBAAiB,UAAU,OAAO,GAAG,YAAY,CAAC,EAAE;AAAA;AAAA,gBAE7D,EAAE,OAAO,iBAAiB,UAAU,OAAO,KAAK,EAAE;AAAA,gBAClD,EAAE,OAAO,iBAAiB,UAAU,OAAO,KAAK,EAAE;AAAA,gBAClD,EAAE,OAAO,iBAAiB,UAAU,OAAO,KAAK,EAAE;AAAA,cACpD;AAAA,cACA,WAAW;AAAA,YACb;AAAA,YACA;AAAA,cACE,WAAW;AAAA,cACX,UAAU;AAAA,gBACR;AAAA;AAAA;AAAA,kBAGE,OAAO;AAAA,kBACP,WAAW;AAAA,gBACb;AAAA;AAAA,gBAEA,EAAE,OAAO,UAAU,aAAa,MAAM,IAAI,EAAC;AAAA;AAAA,gBAE3C,EAAE,OAAO,UAAU,QAAQ,OAAO,GAAG,YAAY,GAAG,IAAI,EAAC;AAAA;AAAA,gBAEzD,EAAE,OAAO,UAAU,QAAQ,MAAM,IAAI,EAAC;AAAA,gBACtC,EAAE,OAAO,UAAU,QAAQ,MAAM,IAAI,EAAC;AAAA,gBACtC,EAAE,OAAO,UAAU,QAAQ,MAAM,IAAI,EAAC;AAAA,cACxC;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE,WAAW;AAAA,UACX,eAAe;AAAA,UACf,KAAK;AAAA,UACL,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,UAAU,CAAE,KAAK,UAAW;AAAA,QAC9B;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,WAAW;AAAA,QACb;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,KAAK;AAAA,UACL,aAAa;AAAA,UACb,UAAU;AAAA,YACR;AAAA,cACE,OAAO;AAAA,cACP,KAAK;AAAA,cACL,WAAW;AAAA,YACb;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,YAAM,WAAW;AACjB,aAAO,WAAW;AAElB,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,UACP;AAAA,UACA;AAAA,QACF;AAAA,QACA,UAAU;AAAA,QACV,UAAU;AAAA,MACZ;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}