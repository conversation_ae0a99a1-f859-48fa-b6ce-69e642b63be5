{"version": 3, "sources": ["../../highlight.js/lib/languages/mipsasm.js"], "sourcesContent": ["/*\nLanguage: MIPS Assembly\nAuthor: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\nDescription: MIPS Assembly (up to MIPS32R2)\nWebsite: https://en.wikipedia.org/wiki/MIPS_architecture\nCategory: assembler\n*/\n\nfunction mipsasm(hljs) {\n  // local labels: %?[FB]?[AT]?\\d{1,2}\\w+\n  return {\n    name: 'MIPS Assembly',\n    case_insensitive: true,\n    aliases: [ 'mips' ],\n    keywords: {\n      $pattern: '\\\\.?' + hljs.IDENT_RE,\n      meta:\n        // GNU preprocs\n        '.2byte .4byte .align .ascii .asciz .balign .byte .code .data .else .end .endif .endm .endr .equ .err .exitm .extern .global .hword .if .ifdef .ifndef .include .irp .long .macro .rept .req .section .set .skip .space .text .word .ltorg ',\n      built_in:\n        '$0 $1 $2 $3 $4 $5 $6 $7 $8 $9 $10 $11 $12 $13 $14 $15 ' + // integer registers\n        '$16 $17 $18 $19 $20 $21 $22 $23 $24 $25 $26 $27 $28 $29 $30 $31 ' + // integer registers\n        'zero at v0 v1 a0 a1 a2 a3 a4 a5 a6 a7 ' + // integer register aliases\n        't0 t1 t2 t3 t4 t5 t6 t7 t8 t9 s0 s1 s2 s3 s4 s5 s6 s7 s8 ' + // integer register aliases\n        'k0 k1 gp sp fp ra ' + // integer register aliases\n        '$f0 $f1 $f2 $f2 $f4 $f5 $f6 $f7 $f8 $f9 $f10 $f11 $f12 $f13 $f14 $f15 ' + // floating-point registers\n        '$f16 $f17 $f18 $f19 $f20 $f21 $f22 $f23 $f24 $f25 $f26 $f27 $f28 $f29 $f30 $f31 ' + // floating-point registers\n        'Context Random EntryLo0 EntryLo1 Context PageMask Wired EntryHi ' + // Coprocessor 0 registers\n        'HWREna BadVAddr Count Compare SR IntCtl SRSCtl SRSMap Cause EPC PRId ' + // Coprocessor 0 registers\n        'EBase Config Config1 Config2 Config3 LLAddr Debug DEPC DESAVE CacheErr ' + // Coprocessor 0 registers\n        'ECC ErrorEPC TagLo DataLo TagHi DataHi WatchLo WatchHi PerfCtl PerfCnt ' // Coprocessor 0 registers\n    },\n    contains: [\n      {\n        className: 'keyword',\n        begin: '\\\\b(' + // mnemonics\n            // 32-bit integer instructions\n            'addi?u?|andi?|b(al)?|beql?|bgez(al)?l?|bgtzl?|blezl?|bltz(al)?l?|' +\n            'bnel?|cl[oz]|divu?|ext|ins|j(al)?|jalr(\\\\.hb)?|jr(\\\\.hb)?|lbu?|lhu?|' +\n            'll|lui|lw[lr]?|maddu?|mfhi|mflo|movn|movz|move|msubu?|mthi|mtlo|mul|' +\n            'multu?|nop|nor|ori?|rotrv?|sb|sc|se[bh]|sh|sllv?|slti?u?|srav?|' +\n            'srlv?|subu?|sw[lr]?|xori?|wsbh|' +\n            // floating-point instructions\n            'abs\\\\.[sd]|add\\\\.[sd]|alnv.ps|bc1[ft]l?|' +\n            'c\\\\.(s?f|un|u?eq|[ou]lt|[ou]le|ngle?|seq|l[et]|ng[et])\\\\.[sd]|' +\n            '(ceil|floor|round|trunc)\\\\.[lw]\\\\.[sd]|cfc1|cvt\\\\.d\\\\.[lsw]|' +\n            'cvt\\\\.l\\\\.[dsw]|cvt\\\\.ps\\\\.s|cvt\\\\.s\\\\.[dlw]|cvt\\\\.s\\\\.p[lu]|cvt\\\\.w\\\\.[dls]|' +\n            'div\\\\.[ds]|ldx?c1|luxc1|lwx?c1|madd\\\\.[sd]|mfc1|mov[fntz]?\\\\.[ds]|' +\n            'msub\\\\.[sd]|mth?c1|mul\\\\.[ds]|neg\\\\.[ds]|nmadd\\\\.[ds]|nmsub\\\\.[ds]|' +\n            'p[lu][lu]\\\\.ps|recip\\\\.fmt|r?sqrt\\\\.[ds]|sdx?c1|sub\\\\.[ds]|suxc1|' +\n            'swx?c1|' +\n            // system control instructions\n            'break|cache|d?eret|[de]i|ehb|mfc0|mtc0|pause|prefx?|rdhwr|' +\n            'rdpgpr|sdbbp|ssnop|synci?|syscall|teqi?|tgei?u?|tlb(p|r|w[ir])|' +\n            'tlti?u?|tnei?|wait|wrpgpr' +\n        ')',\n        end: '\\\\s'\n      },\n      // lines ending with ; or # aren't really comments, probably auto-detect fail\n      hljs.COMMENT('[;#](?!\\\\s*$)', '$'),\n      hljs.C_BLOCK_COMMENT_MODE,\n      hljs.QUOTE_STRING_MODE,\n      {\n        className: 'string',\n        begin: '\\'',\n        end: '[^\\\\\\\\]\\'',\n        relevance: 0\n      },\n      {\n        className: 'title',\n        begin: '\\\\|',\n        end: '\\\\|',\n        illegal: '\\\\n',\n        relevance: 0\n      },\n      {\n        className: 'number',\n        variants: [\n          { // hex\n            begin: '0x[0-9a-f]+'\n          },\n          { // bare number\n            begin: '\\\\b-?\\\\d+'\n          }\n        ],\n        relevance: 0\n      },\n      {\n        className: 'symbol',\n        variants: [\n          { // GNU MIPS syntax\n            begin: '^\\\\s*[a-z_\\\\.\\\\$][a-z0-9_\\\\.\\\\$]+:'\n          },\n          { // numbered local labels\n            begin: '^\\\\s*[0-9]+:'\n          },\n          { // number local label reference (backwards, forwards)\n            begin: '[0-9]+[bf]'\n          }\n        ],\n        relevance: 0\n      }\n    ],\n    // forward slashes are not allowed\n    illegal: /\\//\n  };\n}\n\nmodule.exports = mipsasm;\n"], "mappings": ";;;;;AAAA;AAAA;AAQA,aAAS,QAAQ,MAAM;AAErB,aAAO;AAAA,QACL,MAAM;AAAA,QACN,kBAAkB;AAAA,QAClB,SAAS,CAAE,MAAO;AAAA,QAClB,UAAU;AAAA,UACR,UAAU,SAAS,KAAK;AAAA,UACxB;AAAA;AAAA,YAEE;AAAA;AAAA,UACF,UACE;AAAA;AAAA,QAWJ;AAAA,QACA,UAAU;AAAA,UACR;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YAqBP,KAAK;AAAA,UACP;AAAA;AAAA,UAEA,KAAK,QAAQ,iBAAiB,GAAG;AAAA,UACjC,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,SAAS;AAAA,YACT,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,UAAU;AAAA,cACR;AAAA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,YACF;AAAA,YACA,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,UAAU;AAAA,cACR;AAAA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,YACF;AAAA,YACA,WAAW;AAAA,UACb;AAAA,QACF;AAAA;AAAA,QAEA,SAAS;AAAA,MACX;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}