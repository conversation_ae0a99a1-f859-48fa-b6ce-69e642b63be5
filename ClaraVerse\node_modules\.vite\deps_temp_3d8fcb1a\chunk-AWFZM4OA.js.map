{"version": 3, "sources": ["../../refractor/lang/shell-session.js"], "sourcesContent": ["'use strict'\nvar refractorBash = require('./bash.js')\nmodule.exports = shellSession\nshellSession.displayName = 'shellSession'\nshellSession.aliases = []\nfunction shellSession(Prism) {\n  Prism.register(refractorBash)\n  ;(function (Prism) {\n    // CAREFUL!\n    // The following patterns are concatenated, so the group referenced by a back reference is non-obvious!\n    var strings = [\n      // normal string\n      /\"(?:\\\\[\\s\\S]|\\$\\([^)]+\\)|\\$(?!\\()|`[^`]+`|[^\"\\\\`$])*\"/.source,\n      /'[^']*'/.source,\n      /\\$'(?:[^'\\\\]|\\\\[\\s\\S])*'/.source, // here doc\n      // 2 capturing groups\n      /<<-?\\s*([\"']?)(\\w+)\\1\\s[\\s\\S]*?[\\r\\n]\\2/.source\n    ].join('|')\n    Prism.languages['shell-session'] = {\n      command: {\n        pattern: RegExp(\n          // user info\n          /^/.source +\n            '(?:' + // <user> \":\" ( <path> )?\n            (/[^\\s@:$#%*!/\\\\]+@[^\\r\\n@:$#%*!/\\\\]+(?::[^\\0-\\x1F$#%*?\"<>:;|]+)?/\n              .source +\n              '|' + // <path>\n              // Since the path pattern is quite general, we will require it to start with a special character to\n              // prevent false positives.\n              /[/~.][^\\0-\\x1F$#%*?\"<>@:;|]*/.source) +\n            ')?' + // shell symbol\n            /[$#%](?=\\s)/.source + // bash command\n            /(?:[^\\\\\\r\\n \\t'\"<$]|[ \\t](?:(?!#)|#.*$)|\\\\(?:[^\\r]|\\r\\n?)|\\$(?!')|<(?!<)|<<str>>)+/.source.replace(\n              /<<str>>/g,\n              function () {\n                return strings\n              }\n            ),\n          'm'\n        ),\n        greedy: true,\n        inside: {\n          info: {\n            // foo@bar:~/files$ exit\n            // foo@bar$ exit\n            // ~/files$ exit\n            pattern: /^[^#$%]+/,\n            alias: 'punctuation',\n            inside: {\n              user: /^[^\\s@:$#%*!/\\\\]+@[^\\r\\n@:$#%*!/\\\\]+/,\n              punctuation: /:/,\n              path: /[\\s\\S]+/\n            }\n          },\n          bash: {\n            pattern: /(^[$#%]\\s*)\\S[\\s\\S]*/,\n            lookbehind: true,\n            alias: 'language-bash',\n            inside: Prism.languages.bash\n          },\n          'shell-symbol': {\n            pattern: /^[$#%]/,\n            alias: 'important'\n          }\n        }\n      },\n      output: /.(?:.*(?:[\\r\\n]|.$))*/\n    }\n    Prism.languages['sh-session'] = Prism.languages['shellsession'] =\n      Prism.languages['shell-session']\n  })(Prism)\n}\n"], "mappings": ";;;;;;;;AAAA;AAAA;AACA,QAAI,gBAAgB;AACpB,WAAO,UAAU;AACjB,iBAAa,cAAc;AAC3B,iBAAa,UAAU,CAAC;AACxB,aAAS,aAAa,OAAO;AAC3B,YAAM,SAAS,aAAa;AAC3B,OAAC,SAAUA,QAAO;AAGjB,YAAI,UAAU;AAAA;AAAA,UAEZ,wDAAwD;AAAA,UACxD,UAAU;AAAA,UACV,2BAA2B;AAAA;AAAA;AAAA,UAE3B,0CAA0C;AAAA,QAC5C,EAAE,KAAK,GAAG;AACV,QAAAA,OAAM,UAAU,eAAe,IAAI;AAAA,UACjC,SAAS;AAAA,YACP,SAAS;AAAA;AAAA,cAEP,IAAI,SACF;AAAA,eACC,kEACE,SACD;AAAA;AAAA;AAAA,cAGA,+BAA+B,UACjC;AAAA,cACA,cAAc;AAAA,cACd,qFAAqF,OAAO;AAAA,gBAC1F;AAAA,gBACA,WAAY;AACV,yBAAO;AAAA,gBACT;AAAA,cACF;AAAA,cACF;AAAA,YACF;AAAA,YACA,QAAQ;AAAA,YACR,QAAQ;AAAA,cACN,MAAM;AAAA;AAAA;AAAA;AAAA,gBAIJ,SAAS;AAAA,gBACT,OAAO;AAAA,gBACP,QAAQ;AAAA,kBACN,MAAM;AAAA,kBACN,aAAa;AAAA,kBACb,MAAM;AAAA,gBACR;AAAA,cACF;AAAA,cACA,MAAM;AAAA,gBACJ,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,OAAO;AAAA,gBACP,QAAQA,OAAM,UAAU;AAAA,cAC1B;AAAA,cACA,gBAAgB;AAAA,gBACd,SAAS;AAAA,gBACT,OAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AAAA,UACA,QAAQ;AAAA,QACV;AACA,QAAAA,OAAM,UAAU,YAAY,IAAIA,OAAM,UAAU,cAAc,IAC5DA,OAAM,UAAU,eAAe;AAAA,MACnC,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}