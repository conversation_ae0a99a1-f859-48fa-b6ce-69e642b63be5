{"version": 3, "sources": ["../../refractor/lang/purebasic.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = purebasic\npurebasic.displayName = 'purebasic'\npurebasic.aliases = []\nfunction purebasic(Prism) {\n  /*\nOriginal Code by <PERSON><PERSON>\n!!MANY THANKS!! I never would have made this, rege<PERSON> and me will never be best friends ;)\n==> https://codepen.io/ImagineProgramming/details/JYydBy/\nslightly changed to pass all tests\n*/\n  // PureBasic support, steal stuff from ansi-c\n  Prism.languages.purebasic = Prism.languages.extend('clike', {\n    comment: /;.*/,\n    keyword:\n      /\\b(?:align|and|as|break|calldebugger|case|compilercase|compilerdefault|compilerelse|compilerelseif|compilerendif|compilerendselect|compilererror|compilerif|compilerselect|continue|data|datasection|debug|debuglevel|declare|declarec|declarecdll|declaredll|declaremodule|default|define|dim|disableasm|disabledebugger|disableexplicit|else|elseif|enableasm|enabledebugger|enableexplicit|end|enddatasection|enddeclaremodule|endenumeration|endif|endimport|endinterface|endmacro|endmodule|endprocedure|endselect|endstructure|endstructureunion|endwith|enumeration|extends|fakereturn|for|foreach|forever|global|gosub|goto|if|import|importc|includebinary|includefile|includepath|interface|macro|module|newlist|newmap|next|not|or|procedure|procedurec|procedurecdll|proceduredll|procedurereturn|protected|prototype|prototypec|read|redim|repeat|restore|return|runtime|select|shared|static|step|structure|structureunion|swap|threaded|to|until|wend|while|with|xincludefile|xor)\\b/i,\n    function: /\\b\\w+(?:\\.\\w+)?\\s*(?=\\()/,\n    number: /(?:\\$[\\da-f]+|\\b-?(?:\\d+(?:\\.\\d+)?|\\.\\d+)(?:e[+-]?\\d+)?)\\b/i,\n    operator:\n      /(?:@\\*?|\\?|\\*)\\w+|-[>-]?|\\+\\+?|!=?|<<?=?|>>?=?|==?|&&?|\\|?\\||[~^%?*/@]/\n  })\n  Prism.languages.insertBefore('purebasic', 'keyword', {\n    tag: /#\\w+\\$?/,\n    asm: {\n      pattern: /(^[\\t ]*)!.*/m,\n      lookbehind: true,\n      alias: 'tag',\n      inside: {\n        comment: /;.*/,\n        string: {\n          pattern: /([\"'`])(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/,\n          greedy: true\n        },\n        // Anonymous label references, i.e.: jmp @b\n        'label-reference-anonymous': {\n          pattern: /(!\\s*j[a-z]+\\s+)@[fb]/i,\n          lookbehind: true,\n          alias: 'fasm-label'\n        },\n        // Named label reference, i.e.: jne label1\n        'label-reference-addressed': {\n          pattern: /(!\\s*j[a-z]+\\s+)[A-Z._?$@][\\w.?$@~#]*/i,\n          lookbehind: true,\n          alias: 'fasm-label'\n        },\n        keyword: [\n          /\\b(?:extern|global)\\b[^;\\r\\n]*/i,\n          /\\b(?:CPU|DEFAULT|FLOAT)\\b.*/\n        ],\n        function: {\n          pattern: /^([\\t ]*!\\s*)[\\da-z]+(?=\\s|$)/im,\n          lookbehind: true\n        },\n        'function-inline': {\n          pattern: /(:\\s*)[\\da-z]+(?=\\s)/i,\n          lookbehind: true,\n          alias: 'function'\n        },\n        label: {\n          pattern: /^([\\t ]*!\\s*)[A-Za-z._?$@][\\w.?$@~#]*(?=:)/m,\n          lookbehind: true,\n          alias: 'fasm-label'\n        },\n        register:\n          /\\b(?:st\\d|[xyz]mm\\d\\d?|[cdt]r\\d|r\\d\\d?[bwd]?|[er]?[abcd]x|[abcd][hl]|[er]?(?:bp|di|si|sp)|[cdefgs]s|mm\\d+)\\b/i,\n        number:\n          /(?:\\b|-|(?=\\$))(?:0[hx](?:[\\da-f]*\\.)?[\\da-f]+(?:p[+-]?\\d+)?|\\d[\\da-f]+[hx]|\\$\\d[\\da-f]*|0[oq][0-7]+|[0-7]+[oq]|0[by][01]+|[01]+[by]|0[dt]\\d+|(?:\\d+(?:\\.\\d+)?|\\.\\d+)(?:\\.?e[+-]?\\d+)?[dt]?)\\b/i,\n        operator: /[\\[\\]*+\\-/%<>=&|$!,.:]/\n      }\n    }\n  })\n  delete Prism.languages.purebasic['class-name']\n  delete Prism.languages.purebasic['boolean']\n  Prism.languages.pbfasm = Prism.languages['purebasic']\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,cAAU,cAAc;AACxB,cAAU,UAAU,CAAC;AACrB,aAAS,UAAU,OAAO;AAQxB,YAAM,UAAU,YAAY,MAAM,UAAU,OAAO,SAAS;AAAA,QAC1D,SAAS;AAAA,QACT,SACE;AAAA,QACF,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,UACE;AAAA,MACJ,CAAC;AACD,YAAM,UAAU,aAAa,aAAa,WAAW;AAAA,QACnD,KAAK;AAAA,QACL,KAAK;AAAA,UACH,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,OAAO;AAAA,UACP,QAAQ;AAAA,YACN,SAAS;AAAA,YACT,QAAQ;AAAA,cACN,SAAS;AAAA,cACT,QAAQ;AAAA,YACV;AAAA;AAAA,YAEA,6BAA6B;AAAA,cAC3B,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,OAAO;AAAA,YACT;AAAA;AAAA,YAEA,6BAA6B;AAAA,cAC3B,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,OAAO;AAAA,YACT;AAAA,YACA,SAAS;AAAA,cACP;AAAA,cACA;AAAA,YACF;AAAA,YACA,UAAU;AAAA,cACR,SAAS;AAAA,cACT,YAAY;AAAA,YACd;AAAA,YACA,mBAAmB;AAAA,cACjB,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,OAAO;AAAA,YACT;AAAA,YACA,OAAO;AAAA,cACL,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,OAAO;AAAA,YACT;AAAA,YACA,UACE;AAAA,YACF,QACE;AAAA,YACF,UAAU;AAAA,UACZ;AAAA,QACF;AAAA,MACF,CAAC;AACD,aAAO,MAAM,UAAU,UAAU,YAAY;AAC7C,aAAO,MAAM,UAAU,UAAU,SAAS;AAC1C,YAAM,UAAU,SAAS,MAAM,UAAU,WAAW;AAAA,IACtD;AAAA;AAAA;", "names": []}