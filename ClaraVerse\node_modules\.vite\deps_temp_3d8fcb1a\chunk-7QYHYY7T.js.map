{"version": 3, "sources": ["../../highlight.js/lib/languages/django.js"], "sourcesContent": ["/*\nLanguage: Django\nDescription: Django is a high-level Python Web framework that encourages rapid development and clean, pragmatic design.\nRequires: xml.js\nAuthor: <PERSON> <<EMAIL>>\nContributors: <PERSON><PERSON> <<EMAIL>>\nWebsite: https://www.djangoproject.com\nCategory: template\n*/\n\n/** @type LanguageFn */\nfunction django(hljs) {\n  const FILTER = {\n    begin: /\\|[A-Za-z]+:?/,\n    keywords: {\n      name:\n        'truncatewords removetags linebreaksbr yesno get_digit timesince random striptags ' +\n        'filesizeformat escape linebreaks length_is ljust rjust cut urlize fix_ampersands ' +\n        'title floatformat capfirst pprint divisibleby add make_list unordered_list urlencode ' +\n        'timeuntil urlizetrunc wordcount stringformat linenumbers slice date dictsort ' +\n        'dictsortreversed default_if_none pluralize lower join center default ' +\n        'truncatewords_html upper length phone2numeric wordwrap time addslashes slugify first ' +\n        'escapejs force_escape iriencode last safe safeseq truncatechars localize unlocalize ' +\n        'localtime utc timezone'\n    },\n    contains: [\n      hljs.QUOTE_STRING_MODE,\n      hljs.APOS_STRING_MODE\n    ]\n  };\n\n  return {\n    name: 'Django',\n    aliases: ['jinja'],\n    case_insensitive: true,\n    subLanguage: 'xml',\n    contains: [\n      hljs.COMMENT(/\\{%\\s*comment\\s*%\\}/, /\\{%\\s*endcomment\\s*%\\}/),\n      hljs.COMMENT(/\\{#/, /#\\}/),\n      {\n        className: 'template-tag',\n        begin: /\\{%/,\n        end: /%\\}/,\n        contains: [{\n          className: 'name',\n          begin: /\\w+/,\n          keywords: {\n            name:\n                'comment endcomment load templatetag ifchanged endifchanged if endif firstof for ' +\n                'endfor ifnotequal endifnotequal widthratio extends include spaceless ' +\n                'endspaceless regroup ifequal endifequal ssi now with cycle url filter ' +\n                'endfilter debug block endblock else autoescape endautoescape csrf_token empty elif ' +\n                'endwith static trans blocktrans endblocktrans get_static_prefix get_media_prefix ' +\n                'plural get_current_language language get_available_languages ' +\n                'get_current_language_bidi get_language_info get_language_info_list localize ' +\n                'endlocalize localtime endlocaltime timezone endtimezone get_current_timezone ' +\n                'verbatim'\n          },\n          starts: {\n            endsWithParent: true,\n            keywords: 'in by as',\n            contains: [FILTER],\n            relevance: 0\n          }\n        }]\n      },\n      {\n        className: 'template-variable',\n        begin: /\\{\\{/,\n        end: /\\}\\}/,\n        contains: [FILTER]\n      }\n    ]\n  };\n}\n\nmodule.exports = django;\n"], "mappings": ";;;;;AAAA;AAAA;AAWA,aAAS,OAAO,MAAM;AACpB,YAAM,SAAS;AAAA,QACb,OAAO;AAAA,QACP,UAAU;AAAA,UACR,MACE;AAAA,QAQJ;AAAA,QACA,UAAU;AAAA,UACR,KAAK;AAAA,UACL,KAAK;AAAA,QACP;AAAA,MACF;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAC,OAAO;AAAA,QACjB,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,UAAU;AAAA,UACR,KAAK,QAAQ,uBAAuB,wBAAwB;AAAA,UAC5D,KAAK,QAAQ,OAAO,KAAK;AAAA,UACzB;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU,CAAC;AAAA,cACT,WAAW;AAAA,cACX,OAAO;AAAA,cACP,UAAU;AAAA,gBACR,MACI;AAAA,cASN;AAAA,cACA,QAAQ;AAAA,gBACN,gBAAgB;AAAA,gBAChB,UAAU;AAAA,gBACV,UAAU,CAAC,MAAM;AAAA,gBACjB,WAAW;AAAA,cACb;AAAA,YACF,CAAC;AAAA,UACH;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU,CAAC,MAAM;AAAA,UACnB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}