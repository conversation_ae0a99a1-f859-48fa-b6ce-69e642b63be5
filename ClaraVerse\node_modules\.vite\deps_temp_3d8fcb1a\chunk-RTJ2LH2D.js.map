{"version": 3, "sources": ["../../refractor/lang/naniscript.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = naniscript\nnaniscript.displayName = 'naniscript'\nnaniscript.aliases = []\nfunction naniscript(Prism) {\n  ;(function (Prism) {\n    var expressionDef = /\\{[^\\r\\n\\[\\]{}]*\\}/\n    var params = {\n      'quoted-string': {\n        pattern: /\"(?:[^\"\\\\]|\\\\.)*\"/,\n        alias: 'operator'\n      },\n      'command-param-id': {\n        pattern: /(\\s)\\w+:/,\n        lookbehind: true,\n        alias: 'property'\n      },\n      'command-param-value': [\n        {\n          pattern: expressionDef,\n          alias: 'selector'\n        },\n        {\n          pattern: /([\\t ])\\S+/,\n          lookbehind: true,\n          greedy: true,\n          alias: 'operator'\n        },\n        {\n          pattern: /\\S(?:.*\\S)?/,\n          alias: 'operator'\n        }\n      ]\n    }\n    Prism.languages.naniscript = {\n      // ; ...\n      comment: {\n        pattern: /^([\\t ]*);.*/m,\n        lookbehind: true\n      },\n      // > ...\n      // Define is a control line starting with '>' followed by a word, a space and a text.\n      define: {\n        pattern: /^>.+/m,\n        alias: 'tag',\n        inside: {\n          value: {\n            pattern: /(^>\\w+[\\t ]+)(?!\\s)[^{}\\r\\n]+/,\n            lookbehind: true,\n            alias: 'operator'\n          },\n          key: {\n            pattern: /(^>)\\w+/,\n            lookbehind: true\n          }\n        }\n      },\n      // # ...\n      label: {\n        pattern: /^([\\t ]*)#[\\t ]*\\w+[\\t ]*$/m,\n        lookbehind: true,\n        alias: 'regex'\n      },\n      command: {\n        pattern: /^([\\t ]*)@\\w+(?=[\\t ]|$).*/m,\n        lookbehind: true,\n        alias: 'function',\n        inside: {\n          'command-name': /^@\\w+/,\n          expression: {\n            pattern: expressionDef,\n            greedy: true,\n            alias: 'selector'\n          },\n          'command-params': {\n            pattern: /\\s*\\S[\\s\\S]*/,\n            inside: params\n          }\n        }\n      },\n      // Generic is any line that doesn't start with operators: ;>#@\n      'generic-text': {\n        pattern: /(^[ \\t]*)[^#@>;\\s].*/m,\n        lookbehind: true,\n        alias: 'punctuation',\n        inside: {\n          // \\{ ... \\} ... \\[ ... \\] ... \\\"\n          'escaped-char': /\\\\[{}\\[\\]\"]/,\n          expression: {\n            pattern: expressionDef,\n            greedy: true,\n            alias: 'selector'\n          },\n          'inline-command': {\n            pattern: /\\[[\\t ]*\\w[^\\r\\n\\[\\]]*\\]/,\n            greedy: true,\n            alias: 'function',\n            inside: {\n              'command-params': {\n                pattern: /(^\\[[\\t ]*\\w+\\b)[\\s\\S]+(?=\\]$)/,\n                lookbehind: true,\n                inside: params\n              },\n              'command-param-name': {\n                pattern: /^(\\[[\\t ]*)\\w+/,\n                lookbehind: true,\n                alias: 'name'\n              },\n              'start-stop-char': /[\\[\\]]/\n            }\n          }\n        }\n      }\n    }\n    Prism.languages.nani = Prism.languages['naniscript']\n    /** @typedef {InstanceType<import(\"./prism-core\")[\"Token\"]>} Token */\n    /**\n     * This hook is used to validate generic-text tokens for balanced brackets.\n     * Mark token as bad-line when contains not balanced brackets: {},[]\n     */\n    Prism.hooks.add('after-tokenize', function (env) {\n      /** @type {(Token | string)[]} */\n      var tokens = env.tokens\n      tokens.forEach(function (token) {\n        if (typeof token !== 'string' && token.type === 'generic-text') {\n          var content = getTextContent(token)\n          if (!isBracketsBalanced(content)) {\n            token.type = 'bad-line'\n            token.content = content\n          }\n        }\n      })\n    })\n    /**\n     * @param {string} input\n     * @returns {boolean}\n     */\n    function isBracketsBalanced(input) {\n      var brackets = '[]{}'\n      var stack = []\n      for (var i = 0; i < input.length; i++) {\n        var bracket = input[i]\n        var bracketsIndex = brackets.indexOf(bracket)\n        if (bracketsIndex !== -1) {\n          if (bracketsIndex % 2 === 0) {\n            stack.push(bracketsIndex + 1)\n          } else if (stack.pop() !== bracketsIndex) {\n            return false\n          }\n        }\n      }\n      return stack.length === 0\n    }\n    /**\n     * @param {string | Token | (string | Token)[]} token\n     * @returns {string}\n     */\n    function getTextContent(token) {\n      if (typeof token === 'string') {\n        return token\n      } else if (Array.isArray(token)) {\n        return token.map(getTextContent).join('')\n      } else {\n        return getTextContent(token.content)\n      }\n    }\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,eAAW,cAAc;AACzB,eAAW,UAAU,CAAC;AACtB,aAAS,WAAW,OAAO;AACzB;AAAC,OAAC,SAAUA,QAAO;AACjB,YAAI,gBAAgB;AACpB,YAAI,SAAS;AAAA,UACX,iBAAiB;AAAA,YACf,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA,oBAAoB;AAAA,YAClB,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,UACA,uBAAuB;AAAA,YACrB;AAAA,cACE,SAAS;AAAA,cACT,OAAO;AAAA,YACT;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,QAAQ;AAAA,cACR,OAAO;AAAA,YACT;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,OAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF;AACA,QAAAA,OAAM,UAAU,aAAa;AAAA;AAAA,UAE3B,SAAS;AAAA,YACP,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA;AAAA;AAAA,UAGA,QAAQ;AAAA,YACN,SAAS;AAAA,YACT,OAAO;AAAA,YACP,QAAQ;AAAA,cACN,OAAO;AAAA,gBACL,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,OAAO;AAAA,cACT;AAAA,cACA,KAAK;AAAA,gBACH,SAAS;AAAA,gBACT,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA;AAAA,UAEA,OAAO;AAAA,YACL,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,UACA,SAAS;AAAA,YACP,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,OAAO;AAAA,YACP,QAAQ;AAAA,cACN,gBAAgB;AAAA,cAChB,YAAY;AAAA,gBACV,SAAS;AAAA,gBACT,QAAQ;AAAA,gBACR,OAAO;AAAA,cACT;AAAA,cACA,kBAAkB;AAAA,gBAChB,SAAS;AAAA,gBACT,QAAQ;AAAA,cACV;AAAA,YACF;AAAA,UACF;AAAA;AAAA,UAEA,gBAAgB;AAAA,YACd,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,OAAO;AAAA,YACP,QAAQ;AAAA;AAAA,cAEN,gBAAgB;AAAA,cAChB,YAAY;AAAA,gBACV,SAAS;AAAA,gBACT,QAAQ;AAAA,gBACR,OAAO;AAAA,cACT;AAAA,cACA,kBAAkB;AAAA,gBAChB,SAAS;AAAA,gBACT,QAAQ;AAAA,gBACR,OAAO;AAAA,gBACP,QAAQ;AAAA,kBACN,kBAAkB;AAAA,oBAChB,SAAS;AAAA,oBACT,YAAY;AAAA,oBACZ,QAAQ;AAAA,kBACV;AAAA,kBACA,sBAAsB;AAAA,oBACpB,SAAS;AAAA,oBACT,YAAY;AAAA,oBACZ,OAAO;AAAA,kBACT;AAAA,kBACA,mBAAmB;AAAA,gBACrB;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,QAAAA,OAAM,UAAU,OAAOA,OAAM,UAAU,YAAY;AAMnD,QAAAA,OAAM,MAAM,IAAI,kBAAkB,SAAU,KAAK;AAE/C,cAAI,SAAS,IAAI;AACjB,iBAAO,QAAQ,SAAU,OAAO;AAC9B,gBAAI,OAAO,UAAU,YAAY,MAAM,SAAS,gBAAgB;AAC9D,kBAAI,UAAU,eAAe,KAAK;AAClC,kBAAI,CAAC,mBAAmB,OAAO,GAAG;AAChC,sBAAM,OAAO;AACb,sBAAM,UAAU;AAAA,cAClB;AAAA,YACF;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAKD,iBAAS,mBAAmB,OAAO;AACjC,cAAI,WAAW;AACf,cAAI,QAAQ,CAAC;AACb,mBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,gBAAI,UAAU,MAAM,CAAC;AACrB,gBAAI,gBAAgB,SAAS,QAAQ,OAAO;AAC5C,gBAAI,kBAAkB,IAAI;AACxB,kBAAI,gBAAgB,MAAM,GAAG;AAC3B,sBAAM,KAAK,gBAAgB,CAAC;AAAA,cAC9B,WAAW,MAAM,IAAI,MAAM,eAAe;AACxC,uBAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AACA,iBAAO,MAAM,WAAW;AAAA,QAC1B;AAKA,iBAAS,eAAe,OAAO;AAC7B,cAAI,OAAO,UAAU,UAAU;AAC7B,mBAAO;AAAA,UACT,WAAW,MAAM,QAAQ,KAAK,GAAG;AAC/B,mBAAO,MAAM,IAAI,cAAc,EAAE,KAAK,EAAE;AAAA,UAC1C,OAAO;AACL,mBAAO,eAAe,MAAM,OAAO;AAAA,UACrC;AAAA,QACF;AAAA,MACF,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}