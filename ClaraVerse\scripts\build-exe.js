#!/usr/bin/env node
/**
 * 🔨 Builder EXE WeMa IA
 * Génère des EXE autonomes pour chaque pôle
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration des builds
const BUILD_CONFIG = {
  'user': {
    name: 'WeMa IA',
    icon: 'assets/icons/wema-ia.ico',
    serverIP: '*************',
    isAdmin: false
  },
  'admin': {
    name: 'WeMa IA - Administration',
    icon: 'assets/icons/admin.ico',
    serverIP: '*************',
    isAdmin: true
  }
};

class ExeBuilder {
  constructor() {
    this.buildsDir = 'exe-builds';
    this.distDir = 'dist-exe';
  }

  /**
   * 🔨 Build tous les EXE
   */
  async buildAll() {
    console.log('🔨 Build des EXE WeMa IA');
    console.log('=' * 50);

    // Nettoyer les dossiers
    this.cleanDirs();

    // Build utilisateur et admin
    await this.buildUserExe();
    await this.buildAdminExe();

    console.log('🎉 Tous les EXE sont prêts !');
    console.log(`📁 Dossier: ${this.distDir}/`);
    console.log('📋 Fichiers générés:');
    console.log('  - WeMa-IA.exe (pour tous les utilisateurs)');
    console.log('  - WeMa-IA-Admin.exe (pour toi)');
  }

  /**
   * 🔨 Build EXE utilisateur (même app pour tous)
   */
  async buildUserExe() {
    console.log('\n🔨 Build WeMa IA (utilisateurs)...');
    await this.buildAppExe('user', BUILD_CONFIG.user);
  }

  /**
   * 🔨 Build EXE admin
   */
  async buildAdminExe() {
    console.log('\n🔨 Build WeMa IA Admin...');
    await this.buildAppExe('admin', BUILD_CONFIG.admin);
  }

  /**
   * 🔨 Build EXE pour une app
   */
  async buildAppExe(appType, config) {
    console.log(`  🔨 Build ${config.name}...`);

    const buildDir = path.join(this.buildsDir, appType);

    // 1. Préparer le dossier de build
    this.prepareBuildDir(buildDir, config);

    // 2. Copier les sources
    this.copySources(buildDir);

    // 3. Configurer l'app
    this.configureApp(buildDir, config);

    // 4. Build React
    await this.buildReact(buildDir);

    // 5. Build Electron EXE
    await this.buildElectron(buildDir, config);

    // 6. Copier l'EXE final
    this.copyFinalExe(buildDir, config);

    console.log(`  ✅ ${config.name} terminé`);
  }

  /**
   * 📁 Préparer le dossier de build
   */
  prepareBuildDir(buildDir, config) {
    if (fs.existsSync(buildDir)) {
      fs.rmSync(buildDir, { recursive: true });
    }
    fs.mkdirSync(buildDir, { recursive: true });
  }

  /**
   * 📋 Copier les sources
   */
  copySources(buildDir) {
    const itemsToCopy = [
      'src',
      'public', 
      'py_backend',
      'package.json',
      'vite.config.ts',
      'tsconfig.json',
      'tailwind.config.js'
    ];

    itemsToCopy.forEach(item => {
      const src = path.join('.', item);
      const dest = path.join(buildDir, item);
      
      if (fs.existsSync(src)) {
        if (fs.statSync(src).isDirectory()) {
          fs.cpSync(src, dest, { recursive: true });
        } else {
          fs.copyFileSync(src, dest);
        }
      }
    });
  }

  /**
   * ⚙️ Configurer l'application
   */
  configureApp(buildDir, config) {
    // Configuration .env (même pour tous les utilisateurs)
    const envConfig = {
      REACT_APP_ADMIN_MODE: config.isAdmin ? 'true' : 'false',
      INFERENCE_SERVER_IP: config.serverIP,
      INFERENCE_SERVER_PORT: '1235',
      INFERENCE_FALLBACK_LOCAL: 'false',
      RAG_ENABLED: 'true',
      OCR_ENABLED: 'true',
      CLARA_HOST: '127.0.0.1',
      CLARA_PORT: '8000'
    };

    const envContent = Object.entries(envConfig)
      .map(([key, value]) => `${key}=${value}`)
      .join('\n');
    
    fs.writeFileSync(path.join(buildDir, '.env'), envContent);

    // Configuration package.json pour Electron
    const packagePath = path.join(buildDir, 'package.json');
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    
    // Configuration Electron
    packageJson.main = "public/electron.js";
    packageJson.homepage = "./";
    packageJson.build = {
      appId: config.isAdmin ? 'com.wema-ia.admin' : 'com.wema-ia.app',
      productName: config.name,
      directories: {
        output: "dist"
      },
      files: [
        "build/**/*",
        "public/electron.js",
        "public/preload.js",
        "py_backend/**/*",
        "!py_backend/__pycache__/**/*",
        "!py_backend/logs/**/*"
      ],
      win: {
        icon: config.icon,
        target: "nsis"
      },
      nsis: {
        oneClick: false,
        allowToChangeInstallationDirectory: true,
        createDesktopShortcut: true,
        createStartMenuShortcut: true
      }
    };

    // Scripts Electron
    packageJson.scripts = {
      ...packageJson.scripts,
      "electron": "electron .",
      "electron-dev": "ELECTRON_IS_DEV=true electron .",
      "build-electron": "npm run build && electron-builder",
      "dist": "npm run build && electron-builder --publish=never"
    };

    // Dépendances Electron
    packageJson.devDependencies = {
      ...packageJson.devDependencies,
      "electron": "^latest",
      "electron-builder": "^latest"
    };

    fs.writeFileSync(packagePath, JSON.stringify(packageJson, null, 2));

    // Créer electron.js
    this.createElectronMain(buildDir, config);
  }

  /**
   * ⚡ Créer le fichier electron.js
   */
  createElectronMain(buildDir, config) {
    const electronMainCode = `
const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const { spawn } = require('child_process');

let mainWindow;
let backendProcess;

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, '${config.icon}'),
    title: '${config.name}',
    show: false
  });

  const isDev = process.env.ELECTRON_IS_DEV === 'true';
  
  if (isDev) {
    mainWindow.loadURL('http://localhost:3000');
    mainWindow.webContents.openDevTools();
  } else {
    mainWindow.loadFile(path.join(__dirname, '../build/index.html'));
  }

  // Afficher la fenêtre quand elle est prête
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // Démarrer le backend Python
  startBackend();
}

function startBackend() {
  try {
    const backendPath = path.join(__dirname, '../py_backend');
    
    backendProcess = spawn('python', ['main.py'], {
      cwd: backendPath,
      stdio: 'pipe'
    });
    
    backendProcess.stdout.on('data', (data) => {
      console.log('Backend:', data.toString());
    });
    
    backendProcess.stderr.on('data', (data) => {
      console.error('Backend Error:', data.toString());
    });
    
  } catch (error) {
    console.error('Erreur démarrage backend:', error);
  }
}

app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
  // Arrêter le backend
  if (backendProcess && !backendProcess.killed) {
    backendProcess.kill();
  }
  
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});
`;

    fs.writeFileSync(path.join(buildDir, 'public', 'electron.js'), electronMainCode);

    // Créer preload.js
    const preloadCode = `
const { contextBridge } = require('electron');

contextBridge.exposeInMainWorld('electronAPI', {
  platform: process.platform,
  versions: process.versions
});
`;

    fs.writeFileSync(path.join(buildDir, 'public', 'preload.js'), preloadCode);
  }

  /**
   * ⚛️ Build React
   */
  async buildReact(buildDir) {
    const originalDir = process.cwd();
    
    try {
      process.chdir(buildDir);
      
      console.log('  📦 Installation dépendances...');
      execSync('npm install', { stdio: 'inherit' });
      
      console.log('  ⚛️ Build React...');
      execSync('npm run build', { stdio: 'inherit' });
      
    } finally {
      process.chdir(originalDir);
    }
  }

  /**
   * 🔨 Build Electron
   */
  async buildElectron(buildDir, config) {
    const originalDir = process.cwd();
    
    try {
      process.chdir(buildDir);
      
      console.log('  🔨 Build Electron EXE...');
      execSync('npm run dist', { stdio: 'inherit' });
      
    } finally {
      process.chdir(originalDir);
    }
  }

  /**
   * 📁 Copier l'EXE final
   */
  copyFinalExe(buildDir, config) {
    const exeName = config.isAdmin ? 'WeMa-IA-Admin.exe' : 'WeMa-IA.exe';
    const destExe = path.join(this.distDir, exeName);
    
    // Créer le dossier de destination
    if (!fs.existsSync(this.distDir)) {
      fs.mkdirSync(this.distDir, { recursive: true });
    }
    
    // Trouver l'EXE généré
    const distDir = path.join(buildDir, 'dist');
    if (fs.existsSync(distDir)) {
      const files = fs.readdirSync(distDir);
      const exeFile = files.find(f => f.endsWith('.exe'));
      
      if (exeFile) {
        const srcPath = path.join(distDir, exeFile);
        fs.copyFileSync(srcPath, destExe);
        console.log(`  📁 EXE copié: ${destExe}`);
      }
    }
  }

  /**
   * 🧹 Nettoyer les dossiers
   */
  cleanDirs() {
    [this.buildsDir, this.distDir].forEach(dir => {
      if (fs.existsSync(dir)) {
        fs.rmSync(dir, { recursive: true });
      }
      fs.mkdirSync(dir, { recursive: true });
    });
  }
}

// CLI
const builder = new ExeBuilder();
const command = process.argv[2];

switch (command) {
  case 'all':
    builder.buildAll();
    break;

  case 'user':
    builder.buildUserExe();
    break;

  case 'admin':
    builder.buildAdminExe();
    break;

  default:
    console.log(`
🔨 Builder EXE WeMa IA

Commandes:
  all      Build tous les EXE (utilisateur + admin)
  user     Build EXE utilisateur (WeMa-IA.exe)
  admin    Build EXE admin (WeMa-IA-Admin.exe)

Usage:
  node scripts/build-exe.js all
`);
}
`;
