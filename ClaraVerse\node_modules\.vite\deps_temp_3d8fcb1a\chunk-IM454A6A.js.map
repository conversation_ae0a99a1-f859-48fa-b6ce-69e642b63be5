{"version": 3, "sources": ["../../refractor/lang/dot.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = dot\ndot.displayName = 'dot'\ndot.aliases = ['gv']\nfunction dot(Prism) {\n  // https://www.graphviz.org/doc/info/lang.html\n  ;(function (Prism) {\n    var ID =\n      '(?:' +\n      [\n        // an identifier\n        /[a-zA-Z_\\x80-\\uFFFF][\\w\\x80-\\uFFFF]*/.source, // a number\n        /-?(?:\\.\\d+|\\d+(?:\\.\\d*)?)/.source, // a double-quoted string\n        /\"[^\"\\\\]*(?:\\\\[\\s\\S][^\"\\\\]*)*\"/.source, // HTML-like string\n        /<(?:[^<>]|(?!<!--)<(?:[^<>\"']|\"[^\"]*\"|'[^']*')+>|<!--(?:[^-]|-(?!->))*-->)*>/\n          .source\n      ].join('|') +\n      ')'\n    var IDInside = {\n      markup: {\n        pattern: /(^<)[\\s\\S]+(?=>$)/,\n        lookbehind: true,\n        alias: ['language-markup', 'language-html', 'language-xml'],\n        inside: Prism.languages.markup\n      }\n    }\n    /**\n     * @param {string} source\n     * @param {string} flags\n     * @returns {RegExp}\n     */\n    function withID(source, flags) {\n      return RegExp(\n        source.replace(/<ID>/g, function () {\n          return ID\n        }),\n        flags\n      )\n    }\n    Prism.languages.dot = {\n      comment: {\n        pattern: /\\/\\/.*|\\/\\*[\\s\\S]*?\\*\\/|^#.*/m,\n        greedy: true\n      },\n      'graph-name': {\n        pattern: withID(\n          /(\\b(?:digraph|graph|subgraph)[ \\t\\r\\n]+)<ID>/.source,\n          'i'\n        ),\n        lookbehind: true,\n        greedy: true,\n        alias: 'class-name',\n        inside: IDInside\n      },\n      'attr-value': {\n        pattern: withID(/(=[ \\t\\r\\n]*)<ID>/.source),\n        lookbehind: true,\n        greedy: true,\n        inside: IDInside\n      },\n      'attr-name': {\n        pattern: withID(/([\\[;, \\t\\r\\n])<ID>(?=[ \\t\\r\\n]*=)/.source),\n        lookbehind: true,\n        greedy: true,\n        inside: IDInside\n      },\n      keyword: /\\b(?:digraph|edge|graph|node|strict|subgraph)\\b/i,\n      'compass-point': {\n        pattern: /(:[ \\t\\r\\n]*)(?:[ewc_]|[ns][ew]?)(?![\\w\\x80-\\uFFFF])/,\n        lookbehind: true,\n        alias: 'builtin'\n      },\n      node: {\n        pattern: withID(/(^|[^-.\\w\\x80-\\uFFFF\\\\])<ID>/.source),\n        lookbehind: true,\n        greedy: true,\n        inside: IDInside\n      },\n      operator: /[=:]|-[->]/,\n      punctuation: /[\\[\\]{};,]/\n    }\n    Prism.languages.gv = Prism.languages.dot\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,QAAI,cAAc;AAClB,QAAI,UAAU,CAAC,IAAI;AACnB,aAAS,IAAI,OAAO;AAElB;AAAC,OAAC,SAAUA,QAAO;AACjB,YAAI,KACF,QACA;AAAA;AAAA,UAEE,uCAAuC;AAAA;AAAA,UACvC,4BAA4B;AAAA;AAAA,UAC5B,gCAAgC;AAAA;AAAA,UAChC,+EACG;AAAA,QACL,EAAE,KAAK,GAAG,IACV;AACF,YAAI,WAAW;AAAA,UACb,QAAQ;AAAA,YACN,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,OAAO,CAAC,mBAAmB,iBAAiB,cAAc;AAAA,YAC1D,QAAQA,OAAM,UAAU;AAAA,UAC1B;AAAA,QACF;AAMA,iBAAS,OAAO,QAAQ,OAAO;AAC7B,iBAAO;AAAA,YACL,OAAO,QAAQ,SAAS,WAAY;AAClC,qBAAO;AAAA,YACT,CAAC;AAAA,YACD;AAAA,UACF;AAAA,QACF;AACA,QAAAA,OAAM,UAAU,MAAM;AAAA,UACpB,SAAS;AAAA,YACP,SAAS;AAAA,YACT,QAAQ;AAAA,UACV;AAAA,UACA,cAAc;AAAA,YACZ,SAAS;AAAA,cACP,+CAA+C;AAAA,cAC/C;AAAA,YACF;AAAA,YACA,YAAY;AAAA,YACZ,QAAQ;AAAA,YACR,OAAO;AAAA,YACP,QAAQ;AAAA,UACV;AAAA,UACA,cAAc;AAAA,YACZ,SAAS,OAAO,oBAAoB,MAAM;AAAA,YAC1C,YAAY;AAAA,YACZ,QAAQ;AAAA,YACR,QAAQ;AAAA,UACV;AAAA,UACA,aAAa;AAAA,YACX,SAAS,OAAO,qCAAqC,MAAM;AAAA,YAC3D,YAAY;AAAA,YACZ,QAAQ;AAAA,YACR,QAAQ;AAAA,UACV;AAAA,UACA,SAAS;AAAA,UACT,iBAAiB;AAAA,YACf,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,UACA,MAAM;AAAA,YACJ,SAAS,OAAO,+BAA+B,MAAM;AAAA,YACrD,YAAY;AAAA,YACZ,QAAQ;AAAA,YACR,QAAQ;AAAA,UACV;AAAA,UACA,UAAU;AAAA,UACV,aAAa;AAAA,QACf;AACA,QAAAA,OAAM,UAAU,KAAKA,OAAM,UAAU;AAAA,MACvC,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}