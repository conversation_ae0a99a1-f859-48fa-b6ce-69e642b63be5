{"version": 3, "sources": ["../../refractor/lang/javadoclike.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = javadoclike\njavadoclike.displayName = 'javadoclike'\njavadoclike.aliases = []\nfunction javadoclike(Prism) {\n  ;(function (Prism) {\n    var javaDocLike = (Prism.languages.javadoclike = {\n      parameter: {\n        pattern:\n          /(^[\\t ]*(?:\\/{3}|\\*|\\/\\*\\*)\\s*@(?:arg|arguments|param)\\s+)\\w+/m,\n        lookbehind: true\n      },\n      keyword: {\n        // keywords are the first word in a line preceded be an `@` or surrounded by curly braces.\n        // @word, {@word}\n        pattern: /(^[\\t ]*(?:\\/{3}|\\*|\\/\\*\\*)\\s*|\\{)@[a-z][a-zA-Z-]+\\b/m,\n        lookbehind: true\n      },\n      punctuation: /[{}]/\n    })\n    /**\n     * Adds doc comment support to the given language and calls a given callback on each doc comment pattern.\n     *\n     * @param {string} lang the language add doc comment support to.\n     * @param {(pattern: {inside: {rest: undefined}}) => void} callback the function called with each doc comment pattern as argument.\n     */\n    function docCommentSupport(lang, callback) {\n      var tokenName = 'doc-comment'\n      var grammar = Prism.languages[lang]\n      if (!grammar) {\n        return\n      }\n      var token = grammar[tokenName]\n      if (!token) {\n        // add doc comment: /** */\n        var definition = {}\n        definition[tokenName] = {\n          pattern: /(^|[^\\\\])\\/\\*\\*[^/][\\s\\S]*?(?:\\*\\/|$)/,\n          lookbehind: true,\n          alias: 'comment'\n        }\n        grammar = Prism.languages.insertBefore(lang, 'comment', definition)\n        token = grammar[tokenName]\n      }\n      if (token instanceof RegExp) {\n        // convert regex to object\n        token = grammar[tokenName] = {\n          pattern: token\n        }\n      }\n      if (Array.isArray(token)) {\n        for (var i = 0, l = token.length; i < l; i++) {\n          if (token[i] instanceof RegExp) {\n            token[i] = {\n              pattern: token[i]\n            }\n          }\n          callback(token[i])\n        }\n      } else {\n        callback(token)\n      }\n    }\n    /**\n     * Adds doc-comment support to the given languages for the given documentation language.\n     *\n     * @param {string[]|string} languages\n     * @param {Object} docLanguage\n     */\n    function addSupport(languages, docLanguage) {\n      if (typeof languages === 'string') {\n        languages = [languages]\n      }\n      languages.forEach(function (lang) {\n        docCommentSupport(lang, function (pattern) {\n          if (!pattern.inside) {\n            pattern.inside = {}\n          }\n          pattern.inside.rest = docLanguage\n        })\n      })\n    }\n    Object.defineProperty(javaDocLike, 'addSupport', {\n      value: addSupport\n    })\n    javaDocLike.addSupport(['java', 'javascript', 'php'], javaDocLike)\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,gBAAY,cAAc;AAC1B,gBAAY,UAAU,CAAC;AACvB,aAAS,YAAY,OAAO;AAC1B;AAAC,OAAC,SAAUA,QAAO;AACjB,YAAI,cAAeA,OAAM,UAAU,cAAc;AAAA,UAC/C,WAAW;AAAA,YACT,SACE;AAAA,YACF,YAAY;AAAA,UACd;AAAA,UACA,SAAS;AAAA;AAAA;AAAA,YAGP,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,UACA,aAAa;AAAA,QACf;AAOA,iBAAS,kBAAkB,MAAM,UAAU;AACzC,cAAI,YAAY;AAChB,cAAI,UAAUA,OAAM,UAAU,IAAI;AAClC,cAAI,CAAC,SAAS;AACZ;AAAA,UACF;AACA,cAAI,QAAQ,QAAQ,SAAS;AAC7B,cAAI,CAAC,OAAO;AAEV,gBAAI,aAAa,CAAC;AAClB,uBAAW,SAAS,IAAI;AAAA,cACtB,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,OAAO;AAAA,YACT;AACA,sBAAUA,OAAM,UAAU,aAAa,MAAM,WAAW,UAAU;AAClE,oBAAQ,QAAQ,SAAS;AAAA,UAC3B;AACA,cAAI,iBAAiB,QAAQ;AAE3B,oBAAQ,QAAQ,SAAS,IAAI;AAAA,cAC3B,SAAS;AAAA,YACX;AAAA,UACF;AACA,cAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,qBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,KAAK;AAC5C,kBAAI,MAAM,CAAC,aAAa,QAAQ;AAC9B,sBAAM,CAAC,IAAI;AAAA,kBACT,SAAS,MAAM,CAAC;AAAA,gBAClB;AAAA,cACF;AACA,uBAAS,MAAM,CAAC,CAAC;AAAA,YACnB;AAAA,UACF,OAAO;AACL,qBAAS,KAAK;AAAA,UAChB;AAAA,QACF;AAOA,iBAAS,WAAW,WAAW,aAAa;AAC1C,cAAI,OAAO,cAAc,UAAU;AACjC,wBAAY,CAAC,SAAS;AAAA,UACxB;AACA,oBAAU,QAAQ,SAAU,MAAM;AAChC,8BAAkB,MAAM,SAAU,SAAS;AACzC,kBAAI,CAAC,QAAQ,QAAQ;AACnB,wBAAQ,SAAS,CAAC;AAAA,cACpB;AACA,sBAAQ,OAAO,OAAO;AAAA,YACxB,CAAC;AAAA,UACH,CAAC;AAAA,QACH;AACA,eAAO,eAAe,aAAa,cAAc;AAAA,UAC/C,OAAO;AAAA,QACT,CAAC;AACD,oBAAY,WAAW,CAAC,QAAQ,cAAc,KAAK,GAAG,WAAW;AAAA,MACnE,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}