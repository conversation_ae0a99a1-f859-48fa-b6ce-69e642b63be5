/**
 * 🚀 CLARA ASSISTANT INPUT - REFACTORISÉ
 * Composant principal simplifié utilisant des modules spécialisés
 */

import React, { useState, useRef, useCallback, useEffect } from 'react';
import { 
  Send, 
  Square, 
  Loader2, 
  <PERSON>c<PERSON>, 
  Settings,
  Mic,
  Database
} from 'lucide-react';

// Import des types
import { 
  ClaraInputProps,
  ClaraFileAttachment,
  ClaraSessionConfig,
  ClaraProvider,
  ClaraModel,
  ClaraAIConfig
} from '../../types/clara_assistant_types';

// Import des composants refactorisés
import { Tooltip } from './ui/Tooltip';
import { FileUploadArea } from './file/FileUploadArea';
import { ProviderSelector } from './selectors/ProviderSelector';
import { ModelSelector } from './selectors/ModelSelector';

// Import des services
import { claraApiService } from '../../services/claraApiService';
import { useDocumentStore } from '../../stores/documentStore';

// Import des composants existants (à refactoriser plus tard)
import CompactRAGSelector from './CompactRAGSelector';
import RAGModeToggle from './RAGModeToggle';
import DocumentProcessingAnimation, { DocumentProcessingStatus } from './DocumentProcessingAnimation';

export const ClaraAssistantInputRefactored: React.FC<ClaraInputProps> = ({
  onSendMessage,
  isLoading = false,
  onStop,
  sessionConfig,
  onSessionConfigChange,
  placeholder = "Ask Clara anything...",
  disabled = false,
  showAdvancedOptions = true,
  showFileUpload = true,
  showVoiceInput = false,
  showRAGSelector = true,
  maxFileSize = 10 * 1024 * 1024, // 10MB
  acceptedFileTypes = ['image/*', '.pdf', '.txt', '.md', '.json', '.csv'],
  onFileProcessingStart,
  onFileProcessingComplete,
  onFileProcessingError
}) => {
  // États locaux
  const [input, setInput] = useState('');
  const [files, setFiles] = useState<File[]>([]);
  const [providers, setProviders] = useState<ClaraProvider[]>([]);
  const [models, setModels] = useState<ClaraModel[]>([]);
  const [isAdvancedOpen, setIsAdvancedOpen] = useState(false);
  const [isProcessingFiles, setIsProcessingFiles] = useState(false);
  const [processingStatus, setProcessingStatus] = useState<DocumentProcessingStatus>('idle');

  // Refs
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Document store
  const { selectedDocuments, ragMode } = useDocumentStore();

  // Charger les providers et modèles
  useEffect(() => {
    const loadProvidersAndModels = async () => {
      try {
        const [providersData, modelsData] = await Promise.all([
          claraApiService.getProviders(),
          claraApiService.getModels()
        ]);
        
        setProviders(providersData);
        setModels(modelsData);
      } catch (error) {
        console.error('Failed to load providers and models:', error);
      }
    };

    loadProvidersAndModels();
  }, []);

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [input]);

  // Handlers
  const handleSend = useCallback(async () => {
    if (!input.trim() && files.length === 0) return;
    if (isLoading || isProcessingFiles) return;

    try {
      // Process files if any
      let attachments: ClaraFileAttachment[] = [];
      if (files.length > 0) {
        setIsProcessingFiles(true);
        setProcessingStatus('processing');
        onFileProcessingStart?.(files);

        // Convert files to attachments
        attachments = await Promise.all(
          files.map(async (file) => {
            const content = await file.text();
            return {
              id: Date.now().toString() + Math.random(),
              name: file.name,
              type: file.type.startsWith('image/') ? 'image' as const : 'document' as const,
              size: file.size,
              content,
              file
            };
          })
        );

        setProcessingStatus('complete');
        onFileProcessingComplete?.(attachments);
      }

      // Send message
      await onSendMessage(input, attachments);

      // Reset form
      setInput('');
      setFiles([]);
      setIsProcessingFiles(false);
      setProcessingStatus('idle');

    } catch (error) {
      console.error('Send message error:', error);
      setIsProcessingFiles(false);
      setProcessingStatus('error');
      onFileProcessingError?.(error instanceof Error ? error : new Error('Unknown error'));
    }
  }, [input, files, isLoading, isProcessingFiles, onSendMessage, onFileProcessingStart, onFileProcessingComplete, onFileProcessingError]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  }, [handleSend]);

  const handleFilesAdded = useCallback((newFiles: File[]) => {
    // Validate file size
    const validFiles = newFiles.filter(file => {
      if (file.size > maxFileSize) {
        console.warn(`File ${file.name} is too large (${file.size} bytes)`);
        return false;
      }
      return true;
    });

    setFiles(prev => [...prev, ...validFiles]);
  }, [maxFileSize]);

  const handleFileRemoved = useCallback((index: number) => {
    setFiles(prev => prev.filter((_, i) => i !== index));
  }, []);

  const handleProviderChange = useCallback((providerId: string) => {
    if (onSessionConfigChange && sessionConfig) {
      onSessionConfigChange({
        ...sessionConfig,
        aiConfig: {
          ...sessionConfig.aiConfig,
          provider: providerId
        }
      });
    }
  }, [sessionConfig, onSessionConfigChange]);

  const handleModelChange = useCallback((modelId: string) => {
    if (onSessionConfigChange && sessionConfig) {
      onSessionConfigChange({
        ...sessionConfig,
        aiConfig: {
          ...sessionConfig.aiConfig,
          models: {
            ...sessionConfig.aiConfig.models,
            text: modelId
          }
        }
      });
    }
  }, [sessionConfig, onSessionConfigChange]);

  const canSend = input.trim() || files.length > 0;
  const showRAGIndicator = showRAGSelector && selectedDocuments.length > 0;

  return (
    <div className="relative">
      {/* Processing Animation */}
      {isProcessingFiles && (
        <DocumentProcessingAnimation 
          status={processingStatus}
          fileName={files[0]?.name}
        />
      )}

      {/* Main Input Container */}
      <div className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-xl shadow-sm">
        {/* Top Bar - Provider/Model Selection */}
        {showAdvancedOptions && (
          <div className="flex items-center gap-2 p-3 border-b border-gray-200 dark:border-gray-700">
            <ProviderSelector
              providers={providers}
              selectedProvider={sessionConfig?.aiConfig.provider || ''}
              onProviderChange={handleProviderChange}
              isLoading={isLoading}
            />
            
            <ModelSelector
              models={models}
              selectedModel={sessionConfig?.aiConfig.models.text || ''}
              onModelChange={handleModelChange}
              currentProvider={sessionConfig?.aiConfig.provider}
              isLoading={isLoading}
            />

            {showRAGSelector && (
              <RAGModeToggle />
            )}
          </div>
        )}

        {/* File Upload Area */}
        {showFileUpload && (
          <div className="relative p-3 border-b border-gray-200 dark:border-gray-700">
            <FileUploadArea
              files={files}
              onFilesAdded={handleFilesAdded}
              onFileRemoved={handleFileRemoved}
              isProcessing={isProcessingFiles}
            />
          </div>
        )}

        {/* RAG Documents Indicator */}
        {showRAGIndicator && (
          <div className="px-3 py-2 border-b border-gray-200 dark:border-gray-700">
            <CompactRAGSelector />
          </div>
        )}

        {/* Text Input Area */}
        <div className="relative">
          <textarea
            ref={textareaRef}
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            disabled={disabled || isLoading || isProcessingFiles}
            className="w-full p-4 pr-12 bg-transparent border-none outline-none resize-none text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 min-h-[60px] max-h-[200px]"
            rows={1}
          />

          {/* Action Buttons */}
          <div className="absolute bottom-3 right-3 flex items-center gap-2">
            {showVoiceInput && (
              <Tooltip content="Voice input">
                <button
                  type="button"
                  className="p-2 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
                  disabled={isLoading || isProcessingFiles}
                >
                  <Mic className="w-4 h-4" />
                </button>
              </Tooltip>
            )}

            {showFileUpload && (
              <Tooltip content="Attach files">
                <button
                  type="button"
                  onClick={() => fileInputRef.current?.click()}
                  className="p-2 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
                  disabled={isLoading || isProcessingFiles}
                >
                  <Paperclip className="w-4 h-4" />
                </button>
              </Tooltip>
            )}

            {/* Send/Stop Button */}
            {isLoading ? (
              <Tooltip content="Stop generation">
                <button
                  onClick={onStop}
                  className="p-2 bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors"
                >
                  <Square className="w-4 h-4" />
                </button>
              </Tooltip>
            ) : (
              <Tooltip content="Send message">
                <button
                  onClick={handleSend}
                  disabled={!canSend || isProcessingFiles}
                  className={`p-2 rounded-lg transition-colors ${
                    canSend && !isProcessingFiles
                      ? 'bg-sakura-500 hover:bg-sakura-600 text-white'
                      : 'bg-gray-200 dark:bg-gray-700 text-gray-400 cursor-not-allowed'
                  }`}
                >
                  {isProcessingFiles ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    <Send className="w-4 h-4" />
                  )}
                </button>
              </Tooltip>
            )}
          </div>
        </div>
      </div>

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        onChange={(e) => {
          if (e.target.files) {
            handleFilesAdded(Array.from(e.target.files));
            e.target.value = '';
          }
        }}
        className="hidden"
        accept={acceptedFileTypes.join(',')}
      />
    </div>
  );
};
