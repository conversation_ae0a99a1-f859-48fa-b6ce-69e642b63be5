{"version": 3, "sources": ["../../refractor/lang/haxe.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = haxe\nhaxe.displayName = 'haxe'\nhaxe.aliases = []\nfunction haxe(Prism) {\n  Prism.languages.haxe = Prism.languages.extend('clike', {\n    string: {\n      // Strings can be multi-line\n      pattern: /\"(?:[^\"\\\\]|\\\\[\\s\\S])*\"/,\n      greedy: true\n    },\n    'class-name': [\n      {\n        pattern:\n          /(\\b(?:abstract|class|enum|extends|implements|interface|new|typedef)\\s+)[A-Z_]\\w*/,\n        lookbehind: true\n      }, // based on naming convention\n      /\\b[A-Z]\\w*/\n    ],\n    // The final look-ahead prevents highlighting of keywords if expressions such as \"haxe.macro.Expr\"\n    keyword:\n      /\\bthis\\b|\\b(?:abstract|as|break|case|cast|catch|class|continue|default|do|dynamic|else|enum|extends|extern|final|for|from|function|if|implements|import|in|inline|interface|macro|new|null|operator|overload|override|package|private|public|return|static|super|switch|throw|to|try|typedef|untyped|using|var|while)(?!\\.)\\b/,\n    function: {\n      pattern: /\\b[a-z_]\\w*(?=\\s*(?:<[^<>]*>\\s*)?\\()/i,\n      greedy: true\n    },\n    operator: /\\.{3}|\\+\\+|--|&&|\\|\\||->|=>|(?:<<?|>{1,3}|[-+*/%!=&|^])=?|[?:~]/\n  })\n  Prism.languages.insertBefore('haxe', 'string', {\n    'string-interpolation': {\n      pattern: /'(?:[^'\\\\]|\\\\[\\s\\S])*'/,\n      greedy: true,\n      inside: {\n        interpolation: {\n          pattern: /(^|[^\\\\])\\$(?:\\w+|\\{[^{}]+\\})/,\n          lookbehind: true,\n          inside: {\n            'interpolation-punctuation': {\n              pattern: /^\\$\\{?|\\}$/,\n              alias: 'punctuation'\n            },\n            expression: {\n              pattern: /[\\s\\S]+/,\n              inside: Prism.languages.haxe\n            }\n          }\n        },\n        string: /[\\s\\S]+/\n      }\n    }\n  })\n  Prism.languages.insertBefore('haxe', 'class-name', {\n    regex: {\n      pattern: /~\\/(?:[^\\/\\\\\\r\\n]|\\\\.)+\\/[a-z]*/,\n      greedy: true,\n      inside: {\n        'regex-flags': /\\b[a-z]+$/,\n        'regex-source': {\n          pattern: /^(~\\/)[\\s\\S]+(?=\\/$)/,\n          lookbehind: true,\n          alias: 'language-regex',\n          inside: Prism.languages.regex\n        },\n        'regex-delimiter': /^~\\/|\\/$/\n      }\n    }\n  })\n  Prism.languages.insertBefore('haxe', 'keyword', {\n    preprocessor: {\n      pattern: /#(?:else|elseif|end|if)\\b.*/,\n      alias: 'property'\n    },\n    metadata: {\n      pattern: /@:?[\\w.]+/,\n      alias: 'symbol'\n    },\n    reification: {\n      pattern: /\\$(?:\\w+|(?=\\{))/,\n      alias: 'important'\n    }\n  })\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,SAAK,cAAc;AACnB,SAAK,UAAU,CAAC;AAChB,aAAS,KAAK,OAAO;AACnB,YAAM,UAAU,OAAO,MAAM,UAAU,OAAO,SAAS;AAAA,QACrD,QAAQ;AAAA;AAAA,UAEN,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,cAAc;AAAA,UACZ;AAAA,YACE,SACE;AAAA,YACF,YAAY;AAAA,UACd;AAAA;AAAA,UACA;AAAA,QACF;AAAA;AAAA,QAEA,SACE;AAAA,QACF,UAAU;AAAA,UACR,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,UAAU;AAAA,MACZ,CAAC;AACD,YAAM,UAAU,aAAa,QAAQ,UAAU;AAAA,QAC7C,wBAAwB;AAAA,UACtB,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,QAAQ;AAAA,YACN,eAAe;AAAA,cACb,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,QAAQ;AAAA,gBACN,6BAA6B;AAAA,kBAC3B,SAAS;AAAA,kBACT,OAAO;AAAA,gBACT;AAAA,gBACA,YAAY;AAAA,kBACV,SAAS;AAAA,kBACT,QAAQ,MAAM,UAAU;AAAA,gBAC1B;AAAA,cACF;AAAA,YACF;AAAA,YACA,QAAQ;AAAA,UACV;AAAA,QACF;AAAA,MACF,CAAC;AACD,YAAM,UAAU,aAAa,QAAQ,cAAc;AAAA,QACjD,OAAO;AAAA,UACL,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,QAAQ;AAAA,YACN,eAAe;AAAA,YACf,gBAAgB;AAAA,cACd,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,OAAO;AAAA,cACP,QAAQ,MAAM,UAAU;AAAA,YAC1B;AAAA,YACA,mBAAmB;AAAA,UACrB;AAAA,QACF;AAAA,MACF,CAAC;AACD,YAAM,UAAU,aAAa,QAAQ,WAAW;AAAA,QAC9C,cAAc;AAAA,UACZ,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,UAAU;AAAA,UACR,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,aAAa;AAAA,UACX,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,MACF,CAAC;AAAA,IACH;AAAA;AAAA;", "names": []}