{"version": 3, "sources": ["../../highlight.js/lib/languages/php-template.js"], "sourcesContent": ["/*\nLanguage: PHP Template\nRequires: xml.js, php.js\nAuthor: <PERSON> <<EMAIL>>\nWebsite: https://www.php.net\nCategory: common\n*/\n\nfunction phpTemplate(hljs) {\n  return {\n    name: \"PHP template\",\n    subLanguage: 'xml',\n    contains: [\n      {\n        begin: /<\\?(php|=)?/,\n        end: /\\?>/,\n        subLanguage: 'php',\n        contains: [\n          // We don't want the php closing tag ?> to close the PHP block when\n          // inside any of the following blocks:\n          {\n            begin: '/\\\\*',\n            end: '\\\\*/',\n            skip: true\n          },\n          {\n            begin: 'b\"',\n            end: '\"',\n            skip: true\n          },\n          {\n            begin: 'b\\'',\n            end: '\\'',\n            skip: true\n          },\n          hljs.inherit(hljs.APOS_STRING_MODE, {\n            illegal: null,\n            className: null,\n            contains: null,\n            skip: true\n          }),\n          hljs.inherit(hljs.QUOTE_STRING_MODE, {\n            illegal: null,\n            className: null,\n            contains: null,\n            skip: true\n          })\n        ]\n      }\n    ]\n  };\n}\n\nmodule.exports = phpTemplate;\n"], "mappings": ";;;;;AAAA;AAAA;AAQA,aAAS,YAAY,MAAM;AACzB,aAAO;AAAA,QACL,MAAM;AAAA,QACN,aAAa;AAAA,QACb,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,aAAa;AAAA,YACb,UAAU;AAAA;AAAA;AAAA,cAGR;AAAA,gBACE,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,MAAM;AAAA,cACR;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,MAAM;AAAA,cACR;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,MAAM;AAAA,cACR;AAAA,cACA,KAAK,QAAQ,KAAK,kBAAkB;AAAA,gBAClC,SAAS;AAAA,gBACT,WAAW;AAAA,gBACX,UAAU;AAAA,gBACV,MAAM;AAAA,cACR,CAAC;AAAA,cACD,KAAK,QAAQ,KAAK,mBAAmB;AAAA,gBACnC,SAAS;AAAA,gBACT,WAAW;AAAA,gBACX,UAAU;AAAA,gBACV,MAAM;AAAA,cACR,CAAC;AAAA,YACH;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}