{"version": 3, "sources": ["../../highlight.js/lib/languages/irpf90.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/*\nLanguage: IRPF90\nAuthor: <PERSON> <<EMAIL>>\nDescription: IRPF90 is an open-source Fortran code generator\nWebsite: http://irpf90.ups-tlse.fr\nCategory: scientific\n*/\n\n/** @type LanguageFn */\nfunction irpf90(hljs) {\n  const PARAMS = {\n    className: 'params',\n    begin: '\\\\(',\n    end: '\\\\)'\n  };\n\n  // regex in both fortran and irpf90 should match\n  const OPTIONAL_NUMBER_SUFFIX = /(_[a-z_\\d]+)?/;\n  const OPTIONAL_NUMBER_EXP = /([de][+-]?\\d+)?/;\n  const NUMBER = {\n    className: 'number',\n    variants: [\n      {\n        begin: concat(/\\b\\d+/, /\\.(\\d*)/, OPTIONAL_NUMBER_EXP, OPTIONAL_NUMBER_SUFFIX)\n      },\n      {\n        begin: concat(/\\b\\d+/, OPTIONAL_NUMBER_EXP, OPTIONAL_NUMBER_SUFFIX)\n      },\n      {\n        begin: concat(/\\.\\d+/, OPTIONAL_NUMBER_EXP, OPTIONAL_NUMBER_SUFFIX)\n      }\n    ],\n    relevance: 0\n  };\n\n  const F_KEYWORDS = {\n    literal: '.False. .True.',\n    keyword: 'kind do while private call intrinsic where elsewhere ' +\n      'type endtype endmodule endselect endinterface end enddo endif if forall endforall only contains default return stop then ' +\n      'public subroutine|10 function program .and. .or. .not. .le. .eq. .ge. .gt. .lt. ' +\n      'goto save else use module select case ' +\n      'access blank direct exist file fmt form formatted iostat name named nextrec number opened rec recl sequential status unformatted unit ' +\n      'continue format pause cycle exit ' +\n      'c_null_char c_alert c_backspace c_form_feed flush wait decimal round iomsg ' +\n      'synchronous nopass non_overridable pass protected volatile abstract extends import ' +\n      'non_intrinsic value deferred generic final enumerator class associate bind enum ' +\n      'c_int c_short c_long c_long_long c_signed_char c_size_t c_int8_t c_int16_t c_int32_t c_int64_t c_int_least8_t c_int_least16_t ' +\n      'c_int_least32_t c_int_least64_t c_int_fast8_t c_int_fast16_t c_int_fast32_t c_int_fast64_t c_intmax_t C_intptr_t c_float c_double ' +\n      'c_long_double c_float_complex c_double_complex c_long_double_complex c_bool c_char c_null_ptr c_null_funptr ' +\n      'c_new_line c_carriage_return c_horizontal_tab c_vertical_tab iso_c_binding c_loc c_funloc c_associated  c_f_pointer ' +\n      'c_ptr c_funptr iso_fortran_env character_storage_size error_unit file_storage_size input_unit iostat_end iostat_eor ' +\n      'numeric_storage_size output_unit c_f_procpointer ieee_arithmetic ieee_support_underflow_control ' +\n      'ieee_get_underflow_mode ieee_set_underflow_mode newunit contiguous recursive ' +\n      'pad position action delim readwrite eor advance nml interface procedure namelist include sequence elemental pure ' +\n      'integer real character complex logical dimension allocatable|10 parameter ' +\n      'external implicit|10 none double precision assign intent optional pointer ' +\n      'target in out common equivalence data ' +\n      // IRPF90 special keywords\n      'begin_provider &begin_provider end_provider begin_shell end_shell begin_template end_template subst assert touch ' +\n      'soft_touch provide no_dep free irp_if irp_else irp_endif irp_write irp_read',\n    built_in: 'alog alog10 amax0 amax1 amin0 amin1 amod cabs ccos cexp clog csin csqrt dabs dacos dasin datan datan2 dcos dcosh ddim dexp dint ' +\n      'dlog dlog10 dmax1 dmin1 dmod dnint dsign dsin dsinh dsqrt dtan dtanh float iabs idim idint idnint ifix isign max0 max1 min0 min1 sngl ' +\n      'algama cdabs cdcos cdexp cdlog cdsin cdsqrt cqabs cqcos cqexp cqlog cqsin cqsqrt dcmplx dconjg derf derfc dfloat dgamma dimag dlgama ' +\n      'iqint qabs qacos qasin qatan qatan2 qcmplx qconjg qcos qcosh qdim qerf qerfc qexp qgamma qimag qlgama qlog qlog10 qmax1 qmin1 qmod ' +\n      'qnint qsign qsin qsinh qsqrt qtan qtanh abs acos aimag aint anint asin atan atan2 char cmplx conjg cos cosh exp ichar index int log ' +\n      'log10 max min nint sign sin sinh sqrt tan tanh print write dim lge lgt lle llt mod nullify allocate deallocate ' +\n      'adjustl adjustr all allocated any associated bit_size btest ceiling count cshift date_and_time digits dot_product ' +\n      'eoshift epsilon exponent floor fraction huge iand ibclr ibits ibset ieor ior ishft ishftc lbound len_trim matmul ' +\n      'maxexponent maxloc maxval merge minexponent minloc minval modulo mvbits nearest pack present product ' +\n      'radix random_number random_seed range repeat reshape rrspacing scale scan selected_int_kind selected_real_kind ' +\n      'set_exponent shape size spacing spread sum system_clock tiny transpose trim ubound unpack verify achar iachar transfer ' +\n      'dble entry dprod cpu_time command_argument_count get_command get_command_argument get_environment_variable is_iostat_end ' +\n      'ieee_arithmetic ieee_support_underflow_control ieee_get_underflow_mode ieee_set_underflow_mode ' +\n      'is_iostat_eor move_alloc new_line selected_char_kind same_type_as extends_type_of ' +\n      'acosh asinh atanh bessel_j0 bessel_j1 bessel_jn bessel_y0 bessel_y1 bessel_yn erf erfc erfc_scaled gamma log_gamma hypot norm2 ' +\n      'atomic_define atomic_ref execute_command_line leadz trailz storage_size merge_bits ' +\n      'bge bgt ble blt dshiftl dshiftr findloc iall iany iparity image_index lcobound ucobound maskl maskr ' +\n      'num_images parity popcnt poppar shifta shiftl shiftr this_image ' +\n      // IRPF90 special built_ins\n      'IRP_ALIGN irp_here'\n  };\n  return {\n    name: 'IRPF90',\n    case_insensitive: true,\n    keywords: F_KEYWORDS,\n    illegal: /\\/\\*/,\n    contains: [\n      hljs.inherit(hljs.APOS_STRING_MODE, {\n        className: 'string',\n        relevance: 0\n      }),\n      hljs.inherit(hljs.QUOTE_STRING_MODE, {\n        className: 'string',\n        relevance: 0\n      }),\n      {\n        className: 'function',\n        beginKeywords: 'subroutine function program',\n        illegal: '[${=\\\\n]',\n        contains: [\n          hljs.UNDERSCORE_TITLE_MODE,\n          PARAMS\n        ]\n      },\n      hljs.COMMENT('!', '$', {\n        relevance: 0\n      }),\n      hljs.COMMENT('begin_doc', 'end_doc', {\n        relevance: 10\n      }),\n      NUMBER\n    ]\n  };\n}\n\nmodule.exports = irpf90;\n"], "mappings": ";;;;;AAAA;AAAA;AASA,aAAS,OAAO,IAAI;AAClB,UAAI,CAAC,GAAI,QAAO;AAChB,UAAI,OAAO,OAAO,SAAU,QAAO;AAEnC,aAAO,GAAG;AAAA,IACZ;AAMA,aAAS,UAAU,MAAM;AACvB,YAAM,SAAS,KAAK,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE;AACjD,aAAO;AAAA,IACT;AAWA,aAAS,OAAO,MAAM;AACpB,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,MACP;AAGA,YAAM,yBAAyB;AAC/B,YAAM,sBAAsB;AAC5B,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,OAAO,OAAO,SAAS,WAAW,qBAAqB,sBAAsB;AAAA,UAC/E;AAAA,UACA;AAAA,YACE,OAAO,OAAO,SAAS,qBAAqB,sBAAsB;AAAA,UACpE;AAAA,UACA;AAAA,YACE,OAAO,OAAO,SAAS,qBAAqB,sBAAsB;AAAA,UACpE;AAAA,QACF;AAAA,QACA,WAAW;AAAA,MACb;AAEA,YAAM,aAAa;AAAA,QACjB,SAAS;AAAA,QACT,SAAS;AAAA,QAuBT,UAAU;AAAA,MAoBZ;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,kBAAkB;AAAA,QAClB,UAAU;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,UACR,KAAK,QAAQ,KAAK,kBAAkB;AAAA,YAClC,WAAW;AAAA,YACX,WAAW;AAAA,UACb,CAAC;AAAA,UACD,KAAK,QAAQ,KAAK,mBAAmB;AAAA,YACnC,WAAW;AAAA,YACX,WAAW;AAAA,UACb,CAAC;AAAA,UACD;AAAA,YACE,WAAW;AAAA,YACX,eAAe;AAAA,YACf,SAAS;AAAA,YACT,UAAU;AAAA,cACR,KAAK;AAAA,cACL;AAAA,YACF;AAAA,UACF;AAAA,UACA,KAAK,QAAQ,KAAK,KAAK;AAAA,YACrB,WAAW;AAAA,UACb,CAAC;AAAA,UACD,KAAK,QAAQ,aAAa,WAAW;AAAA,YACnC,WAAW;AAAA,UACb,CAAC;AAAA,UACD;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}