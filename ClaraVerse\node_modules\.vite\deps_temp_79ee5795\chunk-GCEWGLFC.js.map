{"version": 3, "sources": ["../../highlight.js/lib/languages/protobuf.js"], "sourcesContent": ["/*\nLanguage: Protocol Buffers\nAuthor: <PERSON> <<EMAIL>>\nDescription: Protocol buffer message definition format\nWebsite: https://developers.google.com/protocol-buffers/docs/proto3\nCategory: protocols\n*/\n\nfunction protobuf(hljs) {\n  return {\n    name: 'Protocol Buffers',\n    keywords: {\n      keyword: 'package import option optional required repeated group oneof',\n      built_in: 'double float int32 int64 uint32 uint64 sint32 sint64 ' +\n        'fixed32 fixed64 sfixed32 sfixed64 bool string bytes',\n      literal: 'true false'\n    },\n    contains: [\n      hljs.QUOTE_STRING_MODE,\n      hljs.NUMBER_MODE,\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      {\n        className: 'class',\n        beginKeywords: 'message enum service', end: /\\{/,\n        illegal: /\\n/,\n        contains: [\n          hljs.inherit(hljs.TITLE_MODE, {\n            starts: {endsWithParent: true, excludeEnd: true} // hack: eating everything after the first title\n          })\n        ]\n      },\n      {\n        className: 'function',\n        beginKeywords: 'rpc',\n        end: /[{;]/, excludeEnd: true,\n        keywords: 'rpc returns'\n      },\n      { // match enum items (relevance)\n        // BLAH = ...;\n        begin: /^\\s*[A-Z_]+(?=\\s*=[^\\n]+;$)/\n      }\n    ]\n  };\n}\n\nmodule.exports = protobuf;\n"], "mappings": ";;;;;AAAA;AAAA;AAQA,aAAS,SAAS,MAAM;AACtB,aAAO;AAAA,QACL,MAAM;AAAA,QACN,UAAU;AAAA,UACR,SAAS;AAAA,UACT,UAAU;AAAA,UAEV,SAAS;AAAA,QACX;AAAA,QACA,UAAU;AAAA,UACR,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AAAA,YACE,WAAW;AAAA,YACX,eAAe;AAAA,YAAwB,KAAK;AAAA,YAC5C,SAAS;AAAA,YACT,UAAU;AAAA,cACR,KAAK,QAAQ,KAAK,YAAY;AAAA,gBAC5B,QAAQ,EAAC,gBAAgB,MAAM,YAAY,KAAI;AAAA;AAAA,cACjD,CAAC;AAAA,YACH;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,eAAe;AAAA,YACf,KAAK;AAAA,YAAQ,YAAY;AAAA,YACzB,UAAU;AAAA,UACZ;AAAA,UACA;AAAA;AAAA;AAAA,YAEE,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}