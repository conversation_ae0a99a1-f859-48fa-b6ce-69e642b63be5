/**
 * 🚀 CLARA ASSISTANT - REFACTORISÉ
 * Composant principal simplifié utilisant des modules spécialisés
 */

import React, { useState, useEffect, useCallback } from 'react';
import { Database, CheckCircle } from 'lucide-react';

// Import des composants UI
import Topbar from './Topbar';
import ClaraSidebar from './Clara_Components/ClaraSidebar';
import ClaraAssistantInput from './Clara_Components/clara_assistant_input';
import ClaraChatWindow from './Clara_Components/clara_assistant_chat_window';
import Sidebar from './Sidebar';
import DocumentChatIntegration from './DocumentChatIntegration';
import DocumentChatIndicator from './DocumentChatIndicator';
import LangfuseMonitor from './LangfuseMonitor';

// Import des gestionnaires refactorisés
import { useSessionManager } from './Clara_Components/session/SessionManager';
import { useConfigManager } from './Clara_Components/config/ConfigManager';
import { useMessageHandler } from './Clara_Components/messaging/MessageHandler';

// Import des types
import type { 
  ClaraFileAttachment,
  ClaraSessionConfig 
} from '../types/clara_assistant_types';

// Import des services
import { db } from '../db';
import { claraTTSService } from '../services/claraTTSService';
import { claraBackgroundService } from '../services/claraBackgroundService';
import { useDocumentStore, useHasDocuments, useSelectedDocuments, useDocumentPanelVisible } from '../stores/documentStore';

interface ClaraAssistantRefactoredProps {
  onPageChange: (page: string) => void;
}

// Hook pour détecter la visibilité de Clara
const useIsVisible = () => {
  const [isVisible, setIsVisible] = useState(true);
  
  useEffect(() => {
    const checkVisibility = () => {
      const claraContainer = document.querySelector('[data-clara-container]');
      if (claraContainer) {
        const isCurrentlyVisible = !claraContainer.classList.contains('hidden');
        setIsVisible(isCurrentlyVisible);
      }
    };
    
    checkVisibility();
    
    const observer = new MutationObserver(checkVisibility);
    const claraContainer = document.querySelector('[data-clara-container]');
    if (claraContainer) {
      observer.observe(claraContainer, { 
        attributes: true, 
        attributeFilter: ['class'] 
      });
    }
    
    return () => observer.disconnect();
  }, []);
  
  return isVisible;
};

export const ClaraAssistantRefactored: React.FC<ClaraAssistantRefactoredProps> = ({ onPageChange }) => {
  // Détection de visibilité
  const isVisible = useIsVisible();
  
  // États locaux
  const [userName, setUserName] = useState<string>('');
  const [wallpaperUrl, setWallpaperUrl] = useState<string | null>(null);
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);
  const [showNoModelsModal, setShowNoModelsModal] = useState(false);

  // Gestionnaires refactorisés
  const configManager = useConfigManager();
  const sessionManager = useSessionManager(configManager.sessionConfig);
  const messageHandler = useMessageHandler(
    sessionManager.addMessage,
    sessionManager.updateMessage
  );

  // Document store
  const documentStore = useDocumentStore();
  const selectedDocuments = useSelectedDocuments();
  const hasDocuments = useHasDocuments();
  const isDocumentPanelVisible = useDocumentPanelVisible();

  // Gestionnaire de toggle du panel de documents
  const handleDocumentPanelToggle = useCallback(() => {
    console.log('🔍 RAG Panel Toggle - Starting...');
    
    try {
      if (documentStore?.toggleDocumentPanel) {
        console.log('✅ Using hook-based store');
        documentStore.toggleDocumentPanel();
        return;
      }

      const directStore = useDocumentStore.getState();
      if (directStore?.toggleDocumentPanel) {
        console.log('✅ Using direct store access');
        directStore.toggleDocumentPanel();
        return;
      }

      // Fallback simple
      console.log('⚠️ Using simple toggle - PRESERVING SELECTIONS');
      const currentState = useDocumentStore.getState();
      const currentVisible = currentState?.isDocumentPanelVisible || false;

      useDocumentStore.setState({
        ...currentState,
        isDocumentPanelVisible: !currentVisible
      });
      console.log('✅ Panel toggled - selections preserved');

    } catch (error) {
      console.error('❌ Toggle failed:', error);
    }
  }, [documentStore]);

  // Charger le nom d'utilisateur
  useEffect(() => {
    const loadUserName = async () => {
      const personalInfo = await db.getPersonalInfo();
      if (personalInfo?.name) {
        const formattedName = personalInfo.name.charAt(0).toUpperCase() + personalInfo.name.slice(1).toLowerCase();
        setUserName(formattedName);
      }
    };
    loadUserName();
  }, []);

  // Charger le wallpaper
  useEffect(() => {
    const loadWallpaper = async () => {
      try {
        const wallpaper = await db.getWallpaper();
        if (wallpaper) {
          setWallpaperUrl(wallpaper);
        }
      } catch (error) {
        console.error('Error loading wallpaper:', error);
      }
    };
    loadWallpaper();
  }, []);

  // Suivre l'état de visibilité pour le service en arrière-plan
  useEffect(() => {
    claraBackgroundService.setBackgroundMode(!isVisible);
  }, [isVisible]);

  // Auto-refresh quand Clara devient visible
  useEffect(() => {
    if (isVisible && !configManager.isLoadingProviders) {
      console.log('👁️ Clara became visible - checking for updates...');
      configManager.refreshProvidersAndServices(false);
    }
  }, [isVisible, configManager.isLoadingProviders, configManager.refreshProvidersAndServices]);

  // Initialiser TTS
  useEffect(() => {
    const initializeTTS = async () => {
      try {
        await claraTTSService.initialize();
        console.log('✅ TTS service initialized');
      } catch (error) {
        console.warn('⚠️ TTS initialization failed:', error);
      }
    };
    initializeTTS();
  }, []);

  // Surveiller la disponibilité des modèles
  useEffect(() => {
    if (!configManager.isLoadingProviders) {
      const hasModels = configManager.models.length > 0;
      const hasEnabledProviders = configManager.providers.some(p => p.isEnabled);
      
      if (!hasModels || !hasEnabledProviders) {
        setShowNoModelsModal(true);
      } else {
        setShowNoModelsModal(false);
      }
    }
  }, [configManager.isLoadingProviders, configManager.models.length, configManager.providers]);

  // Gestionnaires d'événements
  const handleSendMessage = useCallback(async (content: string, attachments?: ClaraFileAttachment[]) => {
    if (!sessionManager.currentSession) {
      console.error('❌ No current session');
      return;
    }

    try {
      await messageHandler.sendMessage(
        content,
        attachments,
        sessionManager.currentSession,
        configManager.sessionConfig
      );
    } catch (error) {
      console.error('❌ Failed to send message:', error);
    }
  }, [sessionManager.currentSession, configManager.sessionConfig, messageHandler.sendMessage]);

  const handleNewChat = useCallback(async () => {
    const newSession = await sessionManager.createNewSession();
    console.log('✅ New chat created:', newSession.id);
  }, [sessionManager.createNewSession]);

  const handleConfigChange = useCallback((newConfig: Partial<ClaraSessionConfig>) => {
    configManager.updateSessionConfig(newConfig);
  }, [configManager.updateSessionConfig]);

  // Styles pour le wallpaper
  const wallpaperStyle = wallpaperUrl ? {
    backgroundImage: `url(${wallpaperUrl})`,
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    backgroundRepeat: 'no-repeat'
  } : {};

  return (
    <div 
      className="flex h-screen bg-gray-50 dark:bg-gray-900 overflow-hidden"
      data-clara-container
      style={wallpaperStyle}
    >
      {/* Sidebar gauche */}
      <Sidebar onPageChange={onPageChange} />

      {/* Contenu principal */}
      <div className="flex-1 flex flex-col">
        {/* Topbar */}
        <Topbar 
          userName={userName}
          onRefresh={() => configManager.refreshProvidersAndServices(true)}
          isRefreshing={configManager.isRefreshing}
        />

        {/* Zone de chat principale */}
        <div className="flex-1 flex overflow-hidden">
          {/* Sidebar Clara */}
          <ClaraSidebar
            sessions={sessionManager.sessions}
            currentSessionId={sessionManager.currentSession?.id || ''}
            onSelectSession={sessionManager.selectSession}
            onNewChat={handleNewChat}
            onSessionAction={sessionManager.handleSessionAction}
            isLoading={sessionManager.isLoadingSessions}
            hasMoreSessions={sessionManager.hasMoreSessions}
            onLoadMore={sessionManager.loadMoreSessions}
            isLoadingMore={sessionManager.isLoadingMoreSessions}
          />

          {/* Zone de chat */}
          <div className="flex-1 flex flex-col">
            {/* Fenêtre de chat */}
            <div className="flex-1 overflow-hidden">
              <ClaraChatWindow
                messages={sessionManager.messages}
                isLoading={messageHandler.isLoading}
                onCopyMessage={messageHandler.copyMessage}
                onRetryMessage={messageHandler.retryMessage}
                onEditMessage={messageHandler.editMessage}
                currentSession={sessionManager.currentSession}
              />
            </div>

            {/* Zone d'input */}
            <div className="border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
              <ClaraAssistantInput
                onSendMessage={handleSendMessage}
                isLoading={messageHandler.isLoading}
                onStop={messageHandler.stopGeneration}
                sessionConfig={configManager.sessionConfig}
                onSessionConfigChange={handleConfigChange}
                showAdvancedOptions={showAdvancedOptions}
                showRAGSelector={true}
              />
            </div>
          </div>

          {/* Panel de documents RAG */}
          {isDocumentPanelVisible && (
            <div className="w-80 border-l border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
              <DocumentChatIntegration />
            </div>
          )}
        </div>
      </div>

      {/* Indicateur de documents */}
      {hasDocuments && (
        <DocumentChatIndicator
          onToggle={handleDocumentPanelToggle}
          isVisible={isDocumentPanelVisible}
          documentCount={selectedDocuments.length}
        />
      )}

      {/* Moniteur Langfuse */}
      <LangfuseMonitor />

      {/* Modal "Aucun modèle" */}
      {showNoModelsModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md mx-4">
            <div className="flex items-center gap-3 mb-4">
              <Database className="w-6 h-6 text-red-500" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                No Models Available
              </h3>
            </div>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              No AI models are currently available. Please check your provider configurations.
            </p>
            <div className="flex gap-2">
              <button
                onClick={() => configManager.refreshProvidersAndServices(true)}
                className="flex-1 px-4 py-2 bg-sakura-500 text-white rounded-lg hover:bg-sakura-600 transition-colors"
              >
                Refresh
              </button>
              <button
                onClick={() => setShowNoModelsModal(false)}
                className="px-4 py-2 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white transition-colors"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ClaraAssistantRefactored;
