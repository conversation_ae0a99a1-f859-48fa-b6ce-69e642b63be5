{"version": 3, "sources": ["../../refractor/lang/avisynth.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = avisynth\navisynth.displayName = 'avisynth'\navisynth.aliases = ['avs']\nfunction avisynth(Prism) {\n  // http://avisynth.nl/index.php/The_full_AviSynth_grammar\n  ;(function (Prism) {\n    function replace(pattern, replacements) {\n      return pattern.replace(/<<(\\d+)>>/g, function (m, index) {\n        return replacements[+index]\n      })\n    }\n    function re(pattern, replacements, flags) {\n      return RegExp(replace(pattern, replacements), flags || '')\n    }\n    var types = /bool|clip|float|int|string|val/.source\n    var internals = [\n      // bools\n      /is(?:bool|clip|float|int|string)|defined|(?:(?:internal)?function|var)?exists?/\n        .source, // control\n      /apply|assert|default|eval|import|nop|select|undefined/.source, // global\n      /opt_(?:allowfloataudio|avipadscanlines|dwchannelmask|enable_(?:b64a|planartopackedrgb|v210|y3_10_10|y3_10_16)|usewaveextensible|vdubplanarhack)|set(?:cachemode|maxcpu|memorymax|planarlegacyalignment|workingdir)/\n        .source, // conv\n      /hex(?:value)?|value/.source, // numeric\n      /abs|ceil|continued(?:denominator|numerator)?|exp|floor|fmod|frac|log(?:10)?|max|min|muldiv|pi|pow|rand|round|sign|spline|sqrt/\n        .source, // trig\n      /a?sinh?|a?cosh?|a?tan[2h]?/.source, // bit\n      /(?:bit(?:and|not|x?or|[lr]?shift[aslu]?|sh[lr]|sa[lr]|[lr]rotatel?|ro[rl]|te?st|set(?:count)?|cl(?:ea)?r|ch(?:an)?ge?))/\n        .source, // runtime\n      /average(?:[bgr]|chroma[uv]|luma)|(?:[rgb]|chroma[uv]|luma|rgb|[yuv](?=difference(?:fromprevious|tonext)))difference(?:fromprevious|tonext)?|[yuvrgb]plane(?:median|min|max|minmaxdifference)/\n        .source, // script\n      /getprocessinfo|logmsg|script(?:dir(?:utf8)?|file(?:utf8)?|name(?:utf8)?)|setlogparams/\n        .source, // string\n      /chr|(?:fill|find|left|mid|replace|rev|right)str|format|[lu]case|ord|str(?:cmpi?|fromutf8|len|toutf8)|time|trim(?:all|left|right)/\n        .source, // version\n      /isversionorgreater|version(?:number|string)/.source, // helper\n      /buildpixeltype|colorspacenametopixeltype/.source, // avsplus\n      /addautoloaddir|on(?:cpu|cuda)|prefetch|setfiltermtmode/.source\n    ].join('|')\n    var properties = [\n      // content\n      /has(?:audio|video)/.source, // resolution\n      /height|width/.source, // framerate\n      /frame(?:count|rate)|framerate(?:denominator|numerator)/.source, // interlacing\n      /getparity|is(?:field|frame)based/.source, // color format\n      /bitspercomponent|componentsize|hasalpha|is(?:planar(?:rgba?)?|interleaved|rgb(?:24|32|48|64)?|y(?:8|u(?:va?|y2))?|yv(?:12|16|24|411)|420|422|444|packedrgb)|numcomponents|pixeltype/\n        .source, // audio\n      /audio(?:bits|channels|duration|length(?:[fs]|hi|lo)?|rate)|isaudio(?:float|int)/\n        .source\n    ].join('|')\n    var filters = [\n      // source\n      /avi(?:file)?source|directshowsource|image(?:reader|source|sourceanim)|opendmlsource|segmented(?:avisource|directshowsource)|wavsource/\n        .source, // color\n      /coloryuv|convertbacktoyuy2|convertto(?:RGB(?:24|32|48|64)|(?:planar)?RGBA?|Y8?|YV(?:12|16|24|411)|YUVA?(?:411|420|422|444)|YUY2)|fixluminance|gr[ae]yscale|invert|levels|limiter|mergea?rgb|merge(?:chroma|luma)|rgbadjust|show(?:alpha|blue|green|red)|swapuv|tweak|[uv]toy8?|ytouv/\n        .source, // overlay\n      /(?:colorkey|reset)mask|layer|mask(?:hs)?|merge|overlay|subtract/.source, // geometry\n      /addborders|(?:bicubic|bilinear|blackman|gauss|lanczos4|lanczos|point|sinc|spline(?:16|36|64))resize|crop(?:bottom)?|flip(?:horizontal|vertical)|(?:horizontal|vertical)?reduceby2|letterbox|skewrows|turn(?:180|left|right)/\n        .source, // pixel\n      /blur|fixbrokenchromaupsampling|generalconvolution|(?:spatial|temporal)soften|sharpen/\n        .source, // timeline\n      /trim|(?:un)?alignedsplice|(?:assume|assumescaled|change|convert)FPS|(?:delete|duplicate)frame|dissolve|fade(?:in|io|out)[02]?|freezeframe|interleave|loop|reverse|select(?:even|odd|(?:range)?every)/\n        .source, // interlace\n      /assume[bt]ff|assume(?:field|frame)based|bob|complementparity|doubleweave|peculiarblend|pulldown|separate(?:columns|fields|rows)|swapfields|weave(?:columns|rows)?/\n        .source, // audio\n      /amplify(?:db)?|assumesamplerate|audiodub(?:ex)?|audiotrim|convertaudioto(?:(?:8|16|24|32)bit|float)|converttomono|delayaudio|ensurevbrmp3sync|get(?:left|right)?channel|kill(?:audio|video)|mergechannels|mixaudio|monotostereo|normalize|resampleaudio|ssrc|supereq|timestretch/\n        .source, // conditional\n      /animate|applyrange|conditional(?:filter|reader|select)|frameevaluate|scriptclip|tcp(?:server|source)|writefile(?:end|if|start)?/\n        .source, // export\n      /imagewriter/.source, // debug\n      /blackness|blankclip|colorbars(?:hd)?|compare|dumpfiltergraph|echo|histogram|info|messageclip|preroll|setgraphanalysis|show(?:framenumber|smpte|time)|showfiveversions|stack(?:horizontal|vertical)|subtitle|tone|version/\n        .source\n    ].join('|')\n    var allinternals = [internals, properties, filters].join('|')\n    Prism.languages.avisynth = {\n      comment: [\n        {\n          // Matches [* *] nestable block comments, but only supports 1 level of nested comments\n          // /\\[\\*(?:[^\\[*]|\\[(?!\\*)|\\*(?!\\])|<self>)*\\*\\]/\n          pattern:\n            /(^|[^\\\\])\\[\\*(?:[^\\[*]|\\[(?!\\*)|\\*(?!\\])|\\[\\*(?:[^\\[*]|\\[(?!\\*)|\\*(?!\\]))*\\*\\])*\\*\\]/,\n          lookbehind: true,\n          greedy: true\n        },\n        {\n          // Matches /* */ block comments\n          pattern: /(^|[^\\\\])\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n          lookbehind: true,\n          greedy: true\n        },\n        {\n          // Matches # comments\n          pattern: /(^|[^\\\\$])#.*/,\n          lookbehind: true,\n          greedy: true\n        }\n      ],\n      // Handle before strings because optional arguments are surrounded by double quotes\n      argument: {\n        pattern: re(/\\b(?:<<0>>)\\s+(\"?)\\w+\\1/.source, [types], 'i'),\n        inside: {\n          keyword: /^\\w+/\n        }\n      },\n      // Optional argument assignment\n      'argument-label': {\n        pattern: /([,(][\\s\\\\]*)\\w+\\s*=(?!=)/,\n        lookbehind: true,\n        inside: {\n          'argument-name': {\n            pattern: /^\\w+/,\n            alias: 'punctuation'\n          },\n          punctuation: /=$/\n        }\n      },\n      string: [\n        {\n          // triple double-quoted\n          pattern: /\"\"\"[\\s\\S]*?\"\"\"/,\n          greedy: true\n        },\n        {\n          // single double-quoted\n          pattern: /\"(?:\\\\(?:\\r\\n|[\\s\\S])|[^\"\\\\\\r\\n])*\"/,\n          greedy: true,\n          inside: {\n            constant: {\n              // These *are* case-sensitive!\n              pattern:\n                /\\b(?:DEFAULT_MT_MODE|(?:MAINSCRIPT|PROGRAM|SCRIPT)DIR|(?:MACHINE|USER)_(?:CLASSIC|PLUS)_PLUGINS)\\b/\n            }\n          }\n        }\n      ],\n      // The special \"last\" variable that takes the value of the last implicitly returned clip\n      variable: /\\b(?:last)\\b/i,\n      boolean: /\\b(?:false|no|true|yes)\\b/i,\n      keyword:\n        /\\b(?:catch|else|for|function|global|if|return|try|while|__END__)\\b/i,\n      constant: /\\bMT_(?:MULTI_INSTANCE|NICE_FILTER|SERIALIZED|SPECIAL_MT)\\b/,\n      // AviSynth's internal functions, filters, and properties\n      'builtin-function': {\n        pattern: re(/\\b(?:<<0>>)\\b/.source, [allinternals], 'i'),\n        alias: 'function'\n      },\n      'type-cast': {\n        pattern: re(/\\b(?:<<0>>)(?=\\s*\\()/.source, [types], 'i'),\n        alias: 'keyword'\n      },\n      // External/user-defined filters\n      function: {\n        pattern: /\\b[a-z_]\\w*(?=\\s*\\()|(\\.)[a-z_]\\w*\\b/i,\n        lookbehind: true\n      },\n      // Matches a \\ as the first or last character on a line\n      'line-continuation': {\n        pattern: /(^[ \\t]*)\\\\|\\\\(?=[ \\t]*$)/m,\n        lookbehind: true,\n        alias: 'punctuation'\n      },\n      number:\n        /\\B\\$(?:[\\da-f]{6}|[\\da-f]{8})\\b|(?:(?:\\b|\\B-)\\d+(?:\\.\\d*)?\\b|\\B\\.\\d+\\b)/i,\n      operator: /\\+\\+?|[!=<>]=?|&&|\\|\\||[?:*/%-]/,\n      punctuation: /[{}\\[\\]();,.]/\n    }\n    Prism.languages.avs = Prism.languages.avisynth\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,aAAS,cAAc;AACvB,aAAS,UAAU,CAAC,KAAK;AACzB,aAAS,SAAS,OAAO;AAEvB;AAAC,OAAC,SAAUA,QAAO;AACjB,iBAAS,QAAQ,SAAS,cAAc;AACtC,iBAAO,QAAQ,QAAQ,cAAc,SAAU,GAAG,OAAO;AACvD,mBAAO,aAAa,CAAC,KAAK;AAAA,UAC5B,CAAC;AAAA,QACH;AACA,iBAAS,GAAG,SAAS,cAAc,OAAO;AACxC,iBAAO,OAAO,QAAQ,SAAS,YAAY,GAAG,SAAS,EAAE;AAAA,QAC3D;AACA,YAAI,QAAQ,iCAAiC;AAC7C,YAAI,YAAY;AAAA;AAAA,UAEd,iFACG;AAAA;AAAA,UACH,wDAAwD;AAAA;AAAA,UACxD,qNACG;AAAA;AAAA,UACH,sBAAsB;AAAA;AAAA,UACtB,gIACG;AAAA;AAAA,UACH,6BAA6B;AAAA;AAAA,UAC7B,0HACG;AAAA;AAAA,UACH,+LACG;AAAA;AAAA,UACH,wFACG;AAAA;AAAA,UACH,mIACG;AAAA;AAAA,UACH,8CAA8C;AAAA;AAAA,UAC9C,2CAA2C;AAAA;AAAA,UAC3C,yDAAyD;AAAA,QAC3D,EAAE,KAAK,GAAG;AACV,YAAI,aAAa;AAAA;AAAA,UAEf,qBAAqB;AAAA;AAAA,UACrB,eAAe;AAAA;AAAA,UACf,yDAAyD;AAAA;AAAA,UACzD,mCAAmC;AAAA;AAAA,UACnC,sLACG;AAAA;AAAA,UACH,kFACG;AAAA,QACL,EAAE,KAAK,GAAG;AACV,YAAI,UAAU;AAAA;AAAA,UAEZ,wIACG;AAAA;AAAA,UACH,uRACG;AAAA;AAAA,UACH,kEAAkE;AAAA;AAAA,UAClE,8NACG;AAAA;AAAA,UACH,uFACG;AAAA;AAAA,UACH,uMACG;AAAA;AAAA,UACH,oKACG;AAAA;AAAA,UACH,mRACG;AAAA;AAAA,UACH,kIACG;AAAA;AAAA,UACH,cAAc;AAAA;AAAA,UACd,2NACG;AAAA,QACL,EAAE,KAAK,GAAG;AACV,YAAI,eAAe,CAAC,WAAW,YAAY,OAAO,EAAE,KAAK,GAAG;AAC5D,QAAAA,OAAM,UAAU,WAAW;AAAA,UACzB,SAAS;AAAA,YACP;AAAA;AAAA;AAAA,cAGE,SACE;AAAA,cACF,YAAY;AAAA,cACZ,QAAQ;AAAA,YACV;AAAA,YACA;AAAA;AAAA,cAEE,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,QAAQ;AAAA,YACV;AAAA,YACA;AAAA;AAAA,cAEE,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,QAAQ;AAAA,YACV;AAAA,UACF;AAAA;AAAA,UAEA,UAAU;AAAA,YACR,SAAS,GAAG,0BAA0B,QAAQ,CAAC,KAAK,GAAG,GAAG;AAAA,YAC1D,QAAQ;AAAA,cACN,SAAS;AAAA,YACX;AAAA,UACF;AAAA;AAAA,UAEA,kBAAkB;AAAA,YAChB,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,QAAQ;AAAA,cACN,iBAAiB;AAAA,gBACf,SAAS;AAAA,gBACT,OAAO;AAAA,cACT;AAAA,cACA,aAAa;AAAA,YACf;AAAA,UACF;AAAA,UACA,QAAQ;AAAA,YACN;AAAA;AAAA,cAEE,SAAS;AAAA,cACT,QAAQ;AAAA,YACV;AAAA,YACA;AAAA;AAAA,cAEE,SAAS;AAAA,cACT,QAAQ;AAAA,cACR,QAAQ;AAAA,gBACN,UAAU;AAAA;AAAA,kBAER,SACE;AAAA,gBACJ;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA;AAAA,UAEA,UAAU;AAAA,UACV,SAAS;AAAA,UACT,SACE;AAAA,UACF,UAAU;AAAA;AAAA,UAEV,oBAAoB;AAAA,YAClB,SAAS,GAAG,gBAAgB,QAAQ,CAAC,YAAY,GAAG,GAAG;AAAA,YACvD,OAAO;AAAA,UACT;AAAA,UACA,aAAa;AAAA,YACX,SAAS,GAAG,uBAAuB,QAAQ,CAAC,KAAK,GAAG,GAAG;AAAA,YACvD,OAAO;AAAA,UACT;AAAA;AAAA,UAEA,UAAU;AAAA,YACR,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA;AAAA,UAEA,qBAAqB;AAAA,YACnB,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,UACA,QACE;AAAA,UACF,UAAU;AAAA,UACV,aAAa;AAAA,QACf;AACA,QAAAA,OAAM,UAAU,MAAMA,OAAM,UAAU;AAAA,MACxC,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}