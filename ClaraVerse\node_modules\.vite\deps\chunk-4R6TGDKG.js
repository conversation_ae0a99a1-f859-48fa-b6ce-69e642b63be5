import {
  require_zig
} from "./chunk-XQAGKARA.js";
import {
  require_core
} from "./chunk-6EMJMJVC.js";
import {
  require_wiki
} from "./chunk-LXBRPGIL.js";
import {
  require_wolfram
} from "./chunk-AWSWD2WT.js";
import {
  require_wren
} from "./chunk-GDCMDM4B.js";
import {
  require_xeora
} from "./chunk-F3JAPJ4G.js";
import {
  require_xml_doc
} from "./chunk-7SQGKJ7W.js";
import {
  require_xojo
} from "./chunk-QIQ34BJR.js";
import {
  require_xquery
} from "./chunk-BHEEJUUD.js";
import {
  require_yang
} from "./chunk-MXUM6X3Z.js";
import {
  require_velocity
} from "./chunk-K2GSCG6I.js";
import {
  require_verilog
} from "./chunk-Z4YOXAWB.js";
import {
  require_vhdl
} from "./chunk-GQOIBPXS.js";
import {
  require_vim
} from "./chunk-FGSIVZ4K.js";
import {
  require_visual_basic
} from "./chunk-DTESLVSS.js";
import {
  require_warpscript
} from "./chunk-KDQZ45TO.js";
import {
  require_wasm
} from "./chunk-O5HELI4H.js";
import {
  require_web_idl
} from "./chunk-52TE5VS5.js";
import {
  require_tt2
} from "./chunk-PZKZY2BW.js";
import {
  require_twig
} from "./chunk-N5ZUFW5U.js";
import {
  require_typoscript
} from "./chunk-SSVU4GJM.js";
import {
  require_unrealscript
} from "./chunk-TT5V55F6.js";
import {
  require_uorazor
} from "./chunk-NKQKTCDR.js";
import {
  require_uri
} from "./chunk-QHA7NSRA.js";
import {
  require_v
} from "./chunk-SYQLKMDL.js";
import {
  require_vala
} from "./chunk-MUZNNLQE.js";
import {
  require_t4_vb
} from "./chunk-XD7EFZTJ.js";
import {
  require_tap
} from "./chunk-PQRSX3CH.js";
import {
  require_yaml
} from "./chunk-KXRFYER5.js";
import {
  require_tcl
} from "./chunk-ZGWF3DLR.js";
import {
  require_textile
} from "./chunk-FHFK6KX6.js";
import {
  require_toml
} from "./chunk-6KX5G5OJ.js";
import {
  require_tremor
} from "./chunk-KDQ2FMZP.js";
import {
  require_tsx
} from "./chunk-L5BZ4PBQ.js";
import {
  require_squirrel
} from "./chunk-HZVR3VD5.js";
import {
  require_stan
} from "./chunk-G52DOELM.js";
import {
  require_stylus
} from "./chunk-3DXF3A6R.js";
import {
  require_swift
} from "./chunk-XLK4HZHK.js";
import {
  require_systemd
} from "./chunk-YMJQZ54S.js";
import {
  require_t4_cs
} from "./chunk-UIWUHFQG.js";
import {
  require_t4_templating
} from "./chunk-ZFTS5JP7.js";
import {
  require_vbnet
} from "./chunk-CEQ2XFBP.js";
import {
  require_sml
} from "./chunk-KVINS2HT.js";
import {
  require_solidity
} from "./chunk-5SEOMLGU.js";
import {
  require_solution_file
} from "./chunk-42QIUNT7.js";
import {
  require_soy
} from "./chunk-JR7QOMCT.js";
import {
  require_sparql
} from "./chunk-WBRGPZBJ.js";
import {
  require_turtle
} from "./chunk-O73XRS2G.js";
import {
  require_splunk_spl
} from "./chunk-GJPUHG6M.js";
import {
  require_sqf
} from "./chunk-QP3WIVIG.js";
import {
  require_sas
} from "./chunk-Z74GLTQF.js";
import {
  require_sass
} from "./chunk-ANEH5JJK.js";
import {
  require_scala
} from "./chunk-FO6BJFLV.js";
import {
  require_scss
} from "./chunk-JZY3NTIY.js";
import {
  require_shell_session
} from "./chunk-AWFZM4OA.js";
import {
  require_smali
} from "./chunk-M53RN4KJ.js";
import {
  require_smalltalk
} from "./chunk-UIZMWFAN.js";
import {
  require_smarty
} from "./chunk-6B3VQCUW.js";
import {
  require_regex
} from "./chunk-S2ZOFJRB.js";
import {
  require_rego
} from "./chunk-C6V2CGFI.js";
import {
  require_renpy
} from "./chunk-7PBQXFDX.js";
import {
  require_rest
} from "./chunk-53T7EXMJ.js";
import {
  require_rip
} from "./chunk-XBXPKR57.js";
import {
  require_roboconf
} from "./chunk-LS4DEZND.js";
import {
  require_robotframework
} from "./chunk-D4ZMLHO2.js";
import {
  require_rust
} from "./chunk-5GRFX5V6.js";
import {
  require_python
} from "./chunk-NTWTRAZQ.js";
import {
  require_q
} from "./chunk-VSHONYAT.js";
import {
  require_qml
} from "./chunk-6EBBI35T.js";
import {
  require_qore
} from "./chunk-LEBXBJOA.js";
import {
  require_qsharp
} from "./chunk-LPSKVTRA.js";
import {
  require_r
} from "./chunk-OKG2UTMW.js";
import {
  require_racket
} from "./chunk-FR6E66DP.js";
import {
  require_reason
} from "./chunk-62IHVYM3.js";
import {
  require_properties
} from "./chunk-HT55W7LJ.js";
import {
  require_protobuf
} from "./chunk-VJWHXYHO.js";
import {
  require_psl
} from "./chunk-ULESJUNQ.js";
import {
  require_pug
} from "./chunk-K3Q5XOEF.js";
import {
  require_puppet
} from "./chunk-56X5BFRC.js";
import {
  require_pure
} from "./chunk-TVD5SKE4.js";
import {
  require_purebasic
} from "./chunk-ID4BUIA6.js";
import {
  require_purescript
} from "./chunk-7DZUOKUG.js";
import {
  require_php_extras
} from "./chunk-4SZ2UHLZ.js";
import {
  require_phpdoc
} from "./chunk-3WZJGW5Y.js";
import {
  require_plsql
} from "./chunk-3GSC5HRY.js";
import {
  require_powerquery
} from "./chunk-XTGJQLE3.js";
import {
  require_powershell
} from "./chunk-ZVMETYJY.js";
import {
  require_processing
} from "./chunk-MAGAQ3WX.js";
import {
  require_prolog
} from "./chunk-NXBUWKAP.js";
import {
  require_promql
} from "./chunk-6IICP4J6.js";
import {
  require_oz
} from "./chunk-2HCI5CKQ.js";
import {
  require_parigp
} from "./chunk-72DHGODW.js";
import {
  require_parser
} from "./chunk-2IDKTKBT.js";
import {
  require_pascal
} from "./chunk-CJPCUJWU.js";
import {
  require_pascaligo
} from "./chunk-NMZ332WT.js";
import {
  require_pcaxis
} from "./chunk-NVOTRJCT.js";
import {
  require_peoplecode
} from "./chunk-755L3YTI.js";
import {
  require_perl
} from "./chunk-FAPD6LKT.js";
import {
  require_nginx
} from "./chunk-7CILBDQH.js";
import {
  require_nim
} from "./chunk-FNRV3PO5.js";
import {
  require_nix
} from "./chunk-KFEBTAW4.js";
import {
  require_nsis
} from "./chunk-IC6FCBDG.js";
import {
  require_objectivec
} from "./chunk-YD64VAZ7.js";
import {
  require_ocaml
} from "./chunk-6E3SR47H.js";
import {
  require_opencl
} from "./chunk-EBZPSLNN.js";
import {
  require_openqasm
} from "./chunk-ZLQEXUUM.js";
import {
  require_moonscript
} from "./chunk-WG2XSXNX.js";
import {
  require_n1ql
} from "./chunk-PWA27SWW.js";
import {
  require_n4js
} from "./chunk-RADZAVAA.js";
import {
  require_nand2tetris_hdl
} from "./chunk-KJSYL23O.js";
import {
  require_naniscript
} from "./chunk-RTJ2LH2D.js";
import {
  require_nasm
} from "./chunk-CK5O5BR2.js";
import {
  require_neon
} from "./chunk-7JSV4PO2.js";
import {
  require_nevod
} from "./chunk-L6SIMNWH.js";
import {
  require_matlab
} from "./chunk-LBGWAG3R.js";
import {
  require_maxscript
} from "./chunk-5IRFS4OO.js";
import {
  require_mel
} from "./chunk-A6Y7J3OP.js";
import {
  require_mermaid
} from "./chunk-6FW33E4O.js";
import {
  require_mizar
} from "./chunk-6HWIRPAC.js";
import {
  require_mongodb
} from "./chunk-D6HDTURP.js";
import {
  require_monkey
} from "./chunk-CDWW7O7W.js";
import {
  require_lisp
} from "./chunk-YX6KXDAM.js";
import {
  require_livescript
} from "./chunk-L4WYU4KE.js";
import {
  require_llvm
} from "./chunk-GTNICSBG.js";
import {
  require_log
} from "./chunk-74KIOVDB.js";
import {
  require_lolcode
} from "./chunk-6FIHL77N.js";
import {
  require_magma
} from "./chunk-YMMBMS6R.js";
import {
  require_makefile
} from "./chunk-6I43AXYZ.js";
import {
  require_markdown
} from "./chunk-QOA7DR7G.js";
import {
  require_kusto
} from "./chunk-KAWLZ5AG.js";
import {
  require_latex
} from "./chunk-MIXG3RLJ.js";
import {
  require_latte
} from "./chunk-MQQ7263E.js";
import {
  require_php
} from "./chunk-IK4RKEFX.js";
import {
  require_less
} from "./chunk-TMTLVH2K.js";
import {
  require_lilypond
} from "./chunk-VEEZV5QV.js";
import {
  require_scheme
} from "./chunk-2DN6PWXF.js";
import {
  require_liquid
} from "./chunk-WH363WLW.js";
import {
  require_jsonp
} from "./chunk-BMU6RMKM.js";
import {
  require_jsstacktrace
} from "./chunk-BXF73ZKS.js";
import {
  require_jsx
} from "./chunk-BAETLIOU.js";
import {
  require_julia
} from "./chunk-SR5AMM2M.js";
import {
  require_keepalived
} from "./chunk-ORUMLSC7.js";
import {
  require_keyman
} from "./chunk-F2KNUHY7.js";
import {
  require_kotlin
} from "./chunk-6U2KUBGW.js";
import {
  require_kumir
} from "./chunk-IPAUZQAY.js";
import {
  require_jolie
} from "./chunk-KHHIRXGK.js";
import {
  require_jq
} from "./chunk-FBP4RADD.js";
import {
  require_js_extras
} from "./chunk-NMY2OGJK.js";
import {
  require_js_templates
} from "./chunk-ALIJ3JNK.js";
import {
  require_jsdoc
} from "./chunk-WF4M6IUH.js";
import {
  require_typescript
} from "./chunk-GVQ6M47V.js";
import {
  require_json5
} from "./chunk-AZDWGE5A.js";
import {
  require_json
} from "./chunk-4GULR4QN.js";
import {
  require_io
} from "./chunk-Y7S5WODU.js";
import {
  require_j
} from "./chunk-HVEV5KTY.js";
import {
  require_javadoc
} from "./chunk-CGDLJEVZ.js";
import {
  require_java
} from "./chunk-4LJPMT5E.js";
import {
  require_javadoclike
} from "./chunk-YBDRPDOY.js";
import {
  require_javastacktrace
} from "./chunk-EQWWG3YB.js";
import {
  require_jexl
} from "./chunk-AG43BOKP.js";
import {
  require_ichigojam
} from "./chunk-5EQYMSTP.js";
import {
  require_icon
} from "./chunk-AFLT6WOE.js";
import {
  require_icu_message_format
} from "./chunk-LCCPGS4K.js";
import {
  require_idris
} from "./chunk-JA24ZBWY.js";
import {
  require_iecst
} from "./chunk-BPWWOLV7.js";
import {
  require_ignore
} from "./chunk-6DDOHGK5.js";
import {
  require_inform7
} from "./chunk-VC5HABXQ.js";
import {
  require_ini
} from "./chunk-ZTAVAL2G.js";
import {
  require_haskell
} from "./chunk-AVONTJ4Y.js";
import {
  require_haxe
} from "./chunk-Q4U2LGTR.js";
import {
  require_hcl
} from "./chunk-PFPRV243.js";
import {
  require_hlsl
} from "./chunk-WR46HNPL.js";
import {
  require_hoon
} from "./chunk-2W5IJ3N6.js";
import {
  require_hpkp
} from "./chunk-NFXA757O.js";
import {
  require_hsts
} from "./chunk-6B2NPMBB.js";
import {
  require_http
} from "./chunk-UIAVJ5SQ.js";
import {
  require_gml
} from "./chunk-PJ67AQBO.js";
import {
  require_gn
} from "./chunk-MNQWWXQJ.js";
import {
  require_go_module
} from "./chunk-PHIGAOOY.js";
import {
  require_go
} from "./chunk-DVG246OO.js";
import {
  require_graphql
} from "./chunk-5DEM27M7.js";
import {
  require_groovy
} from "./chunk-TBONM5HD.js";
import {
  require_haml
} from "./chunk-ILXC4E2S.js";
import {
  require_handlebars
} from "./chunk-BL3ILTVC.js";
import {
  require_ftl
} from "./chunk-JPOF6W5M.js";
import {
  require_gap
} from "./chunk-J7ADYIHA.js";
import {
  require_gcode
} from "./chunk-UTBBERMD.js";
import {
  require_gdscript
} from "./chunk-ASQAWLU5.js";
import {
  require_gedcom
} from "./chunk-MXGJ23YG.js";
import {
  require_gherkin
} from "./chunk-QBOR5KDC.js";
import {
  require_git
} from "./chunk-T7SA6UKJ.js";
import {
  require_glsl
} from "./chunk-WAEPEAUV.js";
import {
  require_etlua
} from "./chunk-ACLXOBJW.js";
import {
  require_excel_formula
} from "./chunk-42TXJH3E.js";
import {
  require_factor
} from "./chunk-CJBIP5Q4.js";
import {
  require_false
} from "./chunk-P5QI22OK.js";
import {
  require_firestore_security_rules
} from "./chunk-TU73372X.js";
import {
  require_flow
} from "./chunk-CBGCFXQ4.js";
import {
  require_fortran
} from "./chunk-5LBXGSHI.js";
import {
  require_fsharp
} from "./chunk-Q3OW4WAU.js";
import {
  require_editorconfig
} from "./chunk-X5RBA4JE.js";
import {
  require_eiffel
} from "./chunk-K5H5MFDO.js";
import {
  require_ejs
} from "./chunk-OLWATA7H.js";
import {
  require_elixir
} from "./chunk-SQQTCVXV.js";
import {
  require_elm
} from "./chunk-SVX75LSG.js";
import {
  require_erb
} from "./chunk-5J7MRS7E.js";
import {
  require_erlang
} from "./chunk-46WQSXPA.js";
import {
  require_lua
} from "./chunk-7G7TCCU4.js";
import {
  require_dhall
} from "./chunk-YGMSR63I.js";
import {
  require_diff
} from "./chunk-4WWR3WYA.js";
import {
  require_django
} from "./chunk-63755SH4.js";
import {
  require_markup_templating
} from "./chunk-U5I27B2T.js";
import {
  require_dns_zone_file
} from "./chunk-WVDACNYA.js";
import {
  require_docker
} from "./chunk-GDEPBIUU.js";
import {
  require_dot
} from "./chunk-IM454A6A.js";
import {
  require_ebnf
} from "./chunk-PSFRJSLZ.js";
import {
  require_css_extras
} from "./chunk-GVMGQJZ7.js";
import {
  require_csv
} from "./chunk-AKERJZXX.js";
import {
  require_cypher
} from "./chunk-X5LGEGD4.js";
import {
  require_d
} from "./chunk-OHDN6DAP.js";
import {
  require_dart
} from "./chunk-LCJGG4FE.js";
import {
  require_dataweave
} from "./chunk-D6EPE7IU.js";
import {
  require_dax
} from "./chunk-COH7RFRV.js";
import {
  require_cobol
} from "./chunk-6IBXG27B.js";
import {
  require_coffeescript
} from "./chunk-KN25MMEF.js";
import {
  require_concurnas
} from "./chunk-H5QFZELO.js";
import {
  require_coq
} from "./chunk-NXT4RJZB.js";
import {
  require_crystal
} from "./chunk-DLJIPYTH.js";
import {
  require_ruby
} from "./chunk-ZW67UUBT.js";
import {
  require_cshtml
} from "./chunk-7W62CGFK.js";
import {
  require_csp
} from "./chunk-G7JVTXUA.js";
import {
  require_bro
} from "./chunk-B5CX6FSP.js";
import {
  require_bsl
} from "./chunk-7GPQRZ7R.js";
import {
  require_cfscript
} from "./chunk-PHHO4GJQ.js";
import {
  require_chaiscript
} from "./chunk-MAZZZEKZ.js";
import {
  require_cil
} from "./chunk-6WOQV4X3.js";
import {
  require_clojure
} from "./chunk-6LLXCXHQ.js";
import {
  require_cmake
} from "./chunk-GMIF6C6W.js";
import {
  require_batch
} from "./chunk-Y3PLNOQK.js";
import {
  require_bbcode
} from "./chunk-OVJHDC57.js";
import {
  require_bicep
} from "./chunk-T7HWPZGS.js";
import {
  require_birb
} from "./chunk-S4J77WPV.js";
import {
  require_bison
} from "./chunk-O5F3WDBA.js";
import {
  require_bnf
} from "./chunk-DJRTWQZV.js";
import {
  require_brainfuck
} from "./chunk-T3Y5HHO7.js";
import {
  require_brightscript
} from "./chunk-XEBCUH4C.js";
import {
  require_aspnet
} from "./chunk-UNB2UGBI.js";
import {
  require_csharp
} from "./chunk-ELDYMZX7.js";
import {
  require_autohotkey
} from "./chunk-RALRJ2YR.js";
import {
  require_autoit
} from "./chunk-WDSKXJCQ.js";
import {
  require_avisynth
} from "./chunk-X26GM43P.js";
import {
  require_avro_idl
} from "./chunk-HGR66ZOL.js";
import {
  require_bash
} from "./chunk-IFXAMT73.js";
import {
  require_basic
} from "./chunk-4XXHVJBM.js";
import {
  require_aql
} from "./chunk-SOXKRWDO.js";
import {
  require_arduino
} from "./chunk-7T7FTMMP.js";
import {
  require_cpp
} from "./chunk-2O3OVL74.js";
import {
  require_c
} from "./chunk-PYRUEMXT.js";
import {
  require_arff
} from "./chunk-K5B2AVPF.js";
import {
  require_asciidoc
} from "./chunk-MWQCV2KZ.js";
import {
  require_asm6502
} from "./chunk-KT3NMSFM.js";
import {
  require_asmatmel
} from "./chunk-J7IOT6CR.js";
import {
  require_agda
} from "./chunk-K6TPO6X5.js";
import {
  require_al
} from "./chunk-W5KH4LJT.js";
import {
  require_antlr4
} from "./chunk-4E5HK5T5.js";
import {
  require_apacheconf
} from "./chunk-W4I4RMLB.js";
import {
  require_apex
} from "./chunk-TJWEX3Q4.js";
import {
  require_sql
} from "./chunk-G7EGQT3D.js";
import {
  require_apl
} from "./chunk-465TC5RO.js";
import {
  require_applescript
} from "./chunk-AOWYDR6K.js";
import {
  require_abap
} from "./chunk-X4ENR32A.js";
import {
  require_abnf
} from "./chunk-TMVFV523.js";
import {
  require_actionscript
} from "./chunk-MA4Z7II3.js";
import {
  require_ada
} from "./chunk-FKZOPHGW.js";
import {
  __commonJS
} from "./chunk-OL46QLBJ.js";

// node_modules/refractor/index.js
var require_refractor = __commonJS({
  "node_modules/refractor/index.js"(exports, module) {
    var refractor = require_core();
    module.exports = refractor;
    refractor.register(require_abap());
    refractor.register(require_abnf());
    refractor.register(require_actionscript());
    refractor.register(require_ada());
    refractor.register(require_agda());
    refractor.register(require_al());
    refractor.register(require_antlr4());
    refractor.register(require_apacheconf());
    refractor.register(require_apex());
    refractor.register(require_apl());
    refractor.register(require_applescript());
    refractor.register(require_aql());
    refractor.register(require_arduino());
    refractor.register(require_arff());
    refractor.register(require_asciidoc());
    refractor.register(require_asm6502());
    refractor.register(require_asmatmel());
    refractor.register(require_aspnet());
    refractor.register(require_autohotkey());
    refractor.register(require_autoit());
    refractor.register(require_avisynth());
    refractor.register(require_avro_idl());
    refractor.register(require_bash());
    refractor.register(require_basic());
    refractor.register(require_batch());
    refractor.register(require_bbcode());
    refractor.register(require_bicep());
    refractor.register(require_birb());
    refractor.register(require_bison());
    refractor.register(require_bnf());
    refractor.register(require_brainfuck());
    refractor.register(require_brightscript());
    refractor.register(require_bro());
    refractor.register(require_bsl());
    refractor.register(require_c());
    refractor.register(require_cfscript());
    refractor.register(require_chaiscript());
    refractor.register(require_cil());
    refractor.register(require_clojure());
    refractor.register(require_cmake());
    refractor.register(require_cobol());
    refractor.register(require_coffeescript());
    refractor.register(require_concurnas());
    refractor.register(require_coq());
    refractor.register(require_cpp());
    refractor.register(require_crystal());
    refractor.register(require_csharp());
    refractor.register(require_cshtml());
    refractor.register(require_csp());
    refractor.register(require_css_extras());
    refractor.register(require_csv());
    refractor.register(require_cypher());
    refractor.register(require_d());
    refractor.register(require_dart());
    refractor.register(require_dataweave());
    refractor.register(require_dax());
    refractor.register(require_dhall());
    refractor.register(require_diff());
    refractor.register(require_django());
    refractor.register(require_dns_zone_file());
    refractor.register(require_docker());
    refractor.register(require_dot());
    refractor.register(require_ebnf());
    refractor.register(require_editorconfig());
    refractor.register(require_eiffel());
    refractor.register(require_ejs());
    refractor.register(require_elixir());
    refractor.register(require_elm());
    refractor.register(require_erb());
    refractor.register(require_erlang());
    refractor.register(require_etlua());
    refractor.register(require_excel_formula());
    refractor.register(require_factor());
    refractor.register(require_false());
    refractor.register(require_firestore_security_rules());
    refractor.register(require_flow());
    refractor.register(require_fortran());
    refractor.register(require_fsharp());
    refractor.register(require_ftl());
    refractor.register(require_gap());
    refractor.register(require_gcode());
    refractor.register(require_gdscript());
    refractor.register(require_gedcom());
    refractor.register(require_gherkin());
    refractor.register(require_git());
    refractor.register(require_glsl());
    refractor.register(require_gml());
    refractor.register(require_gn());
    refractor.register(require_go_module());
    refractor.register(require_go());
    refractor.register(require_graphql());
    refractor.register(require_groovy());
    refractor.register(require_haml());
    refractor.register(require_handlebars());
    refractor.register(require_haskell());
    refractor.register(require_haxe());
    refractor.register(require_hcl());
    refractor.register(require_hlsl());
    refractor.register(require_hoon());
    refractor.register(require_hpkp());
    refractor.register(require_hsts());
    refractor.register(require_http());
    refractor.register(require_ichigojam());
    refractor.register(require_icon());
    refractor.register(require_icu_message_format());
    refractor.register(require_idris());
    refractor.register(require_iecst());
    refractor.register(require_ignore());
    refractor.register(require_inform7());
    refractor.register(require_ini());
    refractor.register(require_io());
    refractor.register(require_j());
    refractor.register(require_java());
    refractor.register(require_javadoc());
    refractor.register(require_javadoclike());
    refractor.register(require_javastacktrace());
    refractor.register(require_jexl());
    refractor.register(require_jolie());
    refractor.register(require_jq());
    refractor.register(require_js_extras());
    refractor.register(require_js_templates());
    refractor.register(require_jsdoc());
    refractor.register(require_json());
    refractor.register(require_json5());
    refractor.register(require_jsonp());
    refractor.register(require_jsstacktrace());
    refractor.register(require_jsx());
    refractor.register(require_julia());
    refractor.register(require_keepalived());
    refractor.register(require_keyman());
    refractor.register(require_kotlin());
    refractor.register(require_kumir());
    refractor.register(require_kusto());
    refractor.register(require_latex());
    refractor.register(require_latte());
    refractor.register(require_less());
    refractor.register(require_lilypond());
    refractor.register(require_liquid());
    refractor.register(require_lisp());
    refractor.register(require_livescript());
    refractor.register(require_llvm());
    refractor.register(require_log());
    refractor.register(require_lolcode());
    refractor.register(require_lua());
    refractor.register(require_magma());
    refractor.register(require_makefile());
    refractor.register(require_markdown());
    refractor.register(require_markup_templating());
    refractor.register(require_matlab());
    refractor.register(require_maxscript());
    refractor.register(require_mel());
    refractor.register(require_mermaid());
    refractor.register(require_mizar());
    refractor.register(require_mongodb());
    refractor.register(require_monkey());
    refractor.register(require_moonscript());
    refractor.register(require_n1ql());
    refractor.register(require_n4js());
    refractor.register(require_nand2tetris_hdl());
    refractor.register(require_naniscript());
    refractor.register(require_nasm());
    refractor.register(require_neon());
    refractor.register(require_nevod());
    refractor.register(require_nginx());
    refractor.register(require_nim());
    refractor.register(require_nix());
    refractor.register(require_nsis());
    refractor.register(require_objectivec());
    refractor.register(require_ocaml());
    refractor.register(require_opencl());
    refractor.register(require_openqasm());
    refractor.register(require_oz());
    refractor.register(require_parigp());
    refractor.register(require_parser());
    refractor.register(require_pascal());
    refractor.register(require_pascaligo());
    refractor.register(require_pcaxis());
    refractor.register(require_peoplecode());
    refractor.register(require_perl());
    refractor.register(require_php_extras());
    refractor.register(require_php());
    refractor.register(require_phpdoc());
    refractor.register(require_plsql());
    refractor.register(require_powerquery());
    refractor.register(require_powershell());
    refractor.register(require_processing());
    refractor.register(require_prolog());
    refractor.register(require_promql());
    refractor.register(require_properties());
    refractor.register(require_protobuf());
    refractor.register(require_psl());
    refractor.register(require_pug());
    refractor.register(require_puppet());
    refractor.register(require_pure());
    refractor.register(require_purebasic());
    refractor.register(require_purescript());
    refractor.register(require_python());
    refractor.register(require_q());
    refractor.register(require_qml());
    refractor.register(require_qore());
    refractor.register(require_qsharp());
    refractor.register(require_r());
    refractor.register(require_racket());
    refractor.register(require_reason());
    refractor.register(require_regex());
    refractor.register(require_rego());
    refractor.register(require_renpy());
    refractor.register(require_rest());
    refractor.register(require_rip());
    refractor.register(require_roboconf());
    refractor.register(require_robotframework());
    refractor.register(require_ruby());
    refractor.register(require_rust());
    refractor.register(require_sas());
    refractor.register(require_sass());
    refractor.register(require_scala());
    refractor.register(require_scheme());
    refractor.register(require_scss());
    refractor.register(require_shell_session());
    refractor.register(require_smali());
    refractor.register(require_smalltalk());
    refractor.register(require_smarty());
    refractor.register(require_sml());
    refractor.register(require_solidity());
    refractor.register(require_solution_file());
    refractor.register(require_soy());
    refractor.register(require_sparql());
    refractor.register(require_splunk_spl());
    refractor.register(require_sqf());
    refractor.register(require_sql());
    refractor.register(require_squirrel());
    refractor.register(require_stan());
    refractor.register(require_stylus());
    refractor.register(require_swift());
    refractor.register(require_systemd());
    refractor.register(require_t4_cs());
    refractor.register(require_t4_templating());
    refractor.register(require_t4_vb());
    refractor.register(require_tap());
    refractor.register(require_tcl());
    refractor.register(require_textile());
    refractor.register(require_toml());
    refractor.register(require_tremor());
    refractor.register(require_tsx());
    refractor.register(require_tt2());
    refractor.register(require_turtle());
    refractor.register(require_twig());
    refractor.register(require_typescript());
    refractor.register(require_typoscript());
    refractor.register(require_unrealscript());
    refractor.register(require_uorazor());
    refractor.register(require_uri());
    refractor.register(require_v());
    refractor.register(require_vala());
    refractor.register(require_vbnet());
    refractor.register(require_velocity());
    refractor.register(require_verilog());
    refractor.register(require_vhdl());
    refractor.register(require_vim());
    refractor.register(require_visual_basic());
    refractor.register(require_warpscript());
    refractor.register(require_wasm());
    refractor.register(require_web_idl());
    refractor.register(require_wiki());
    refractor.register(require_wolfram());
    refractor.register(require_wren());
    refractor.register(require_xeora());
    refractor.register(require_xml_doc());
    refractor.register(require_xojo());
    refractor.register(require_xquery());
    refractor.register(require_yaml());
    refractor.register(require_yang());
    refractor.register(require_zig());
  }
});

export {
  require_refractor
};
//# sourceMappingURL=chunk-4R6TGDKG.js.map
