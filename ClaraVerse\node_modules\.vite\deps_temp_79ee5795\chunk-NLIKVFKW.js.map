{"version": 3, "sources": ["../../highlight.js/lib/languages/julia.js"], "sourcesContent": ["/*\nLanguage: Julia\nDescription: Julia is a high-level, high-performance, dynamic programming language.\nAuthor: <PERSON><PERSON> <<EMAIL>>\nContributors: <PERSON> <<EMAIL>>, <PERSON><PERSON> <ekrefred<PERSON>@gmail.com>\nWebsite: https://julialang.org\n*/\n\nfunction julia(hljs) {\n  // Since there are numerous special names in <PERSON>, it is too much trouble\n  // to maintain them by hand. Hence these names (i.e. keywords, literals and\n  // built-ins) are automatically generated from Julia 1.5.2 itself through\n  // the following scripts for each.\n\n  // ref: https://docs.julialang.org/en/v1/manual/variables/#Allowed-Variable-Names\n  var VARIABLE_NAME_RE = '[A-Za-z_\\\\u00A1-\\\\uFFFF][A-Za-z_0-9\\\\u00A1-\\\\uFFFF]*';\n\n  // # keyword generator, multi-word keywords handled manually below (Julia 1.5.2)\n  // import REPL.REPLCompletions\n  // res = String[\"in\", \"isa\", \"where\"]\n  // for kw in collect(x.keyword for x in REPLCompletions.complete_keyword(\"\"))\n  //     if !(contains(kw, \" \") || kw == \"struct\")\n  //         push!(res, kw)\n  //     end\n  // end\n  // sort!(unique!(res))\n  // foreach(x -> println(\"\\'\", x, \"\\',\"), res)\n  var KEYWORD_LIST = [\n    'baremodule',\n    'begin',\n    'break',\n    'catch',\n    'ccall',\n    'const',\n    'continue',\n    'do',\n    'else',\n    'elseif',\n    'end',\n    'export',\n    'false',\n    'finally',\n    'for',\n    'function',\n    'global',\n    'if',\n    'import',\n    'in',\n    'isa',\n    'let',\n    'local',\n    'macro',\n    'module',\n    'quote',\n    'return',\n    'true',\n    'try',\n    'using',\n    'where',\n    'while',\n  ];\n\n  // # literal generator (Julia 1.5.2)\n  // import REPL.REPLCompletions\n  // res = String[\"true\", \"false\"]\n  // for compl in filter!(x -> isa(x, REPLCompletions.ModuleCompletion) && (x.parent === Base || x.parent === Core),\n  //                     REPLCompletions.completions(\"\", 0)[1])\n  //     try\n  //         v = eval(Symbol(compl.mod))\n  //         if !(v isa Function || v isa Type || v isa TypeVar || v isa Module || v isa Colon)\n  //             push!(res, compl.mod)\n  //         end\n  //     catch e\n  //     end\n  // end\n  // sort!(unique!(res))\n  // foreach(x -> println(\"\\'\", x, \"\\',\"), res)\n  var LITERAL_LIST = [\n    'ARGS',\n    'C_NULL',\n    'DEPOT_PATH',\n    'ENDIAN_BOM',\n    'ENV',\n    'Inf',\n    'Inf16',\n    'Inf32',\n    'Inf64',\n    'InsertionSort',\n    'LOAD_PATH',\n    'MergeSort',\n    'NaN',\n    'NaN16',\n    'NaN32',\n    'NaN64',\n    'PROGRAM_FILE',\n    'QuickSort',\n    'RoundDown',\n    'RoundFromZero',\n    'RoundNearest',\n    'RoundNearestTiesAway',\n    'RoundNearestTiesUp',\n    'RoundToZero',\n    'RoundUp',\n    'VERSION|0',\n    'devnull',\n    'false',\n    'im',\n    'missing',\n    'nothing',\n    'pi',\n    'stderr',\n    'stdin',\n    'stdout',\n    'true',\n    'undef',\n    'π',\n    'ℯ',\n  ];\n\n  // # built_in generator (Julia 1.5.2)\n  // import REPL.REPLCompletions\n  // res = String[]\n  // for compl in filter!(x -> isa(x, REPLCompletions.ModuleCompletion) && (x.parent === Base || x.parent === Core),\n  //                     REPLCompletions.completions(\"\", 0)[1])\n  //     try\n  //         v = eval(Symbol(compl.mod))\n  //         if (v isa Type || v isa TypeVar) && (compl.mod != \"=>\")\n  //             push!(res, compl.mod)\n  //         end\n  //     catch e\n  //     end\n  // end\n  // sort!(unique!(res))\n  // foreach(x -> println(\"\\'\", x, \"\\',\"), res)\n  var BUILT_IN_LIST = [\n    'AbstractArray',\n    'AbstractChannel',\n    'AbstractChar',\n    'AbstractDict',\n    'AbstractDisplay',\n    'AbstractFloat',\n    'AbstractIrrational',\n    'AbstractMatrix',\n    'AbstractRange',\n    'AbstractSet',\n    'AbstractString',\n    'AbstractUnitRange',\n    'AbstractVecOrMat',\n    'AbstractVector',\n    'Any',\n    'ArgumentError',\n    'Array',\n    'AssertionError',\n    'BigFloat',\n    'BigInt',\n    'BitArray',\n    'BitMatrix',\n    'BitSet',\n    'BitVector',\n    'Bool',\n    'BoundsError',\n    'CapturedException',\n    'CartesianIndex',\n    'CartesianIndices',\n    'Cchar',\n    'Cdouble',\n    'Cfloat',\n    'Channel',\n    'Char',\n    'Cint',\n    'Cintmax_t',\n    'Clong',\n    'Clonglong',\n    'Cmd',\n    'Colon',\n    'Complex',\n    'ComplexF16',\n    'ComplexF32',\n    'ComplexF64',\n    'CompositeException',\n    'Condition',\n    'Cptrdiff_t',\n    'Cshort',\n    'Csize_t',\n    'Cssize_t',\n    'Cstring',\n    'Cuchar',\n    'Cuint',\n    'Cuintmax_t',\n    'Culong',\n    'Culonglong',\n    'Cushort',\n    'Cvoid',\n    'Cwchar_t',\n    'Cwstring',\n    'DataType',\n    'DenseArray',\n    'DenseMatrix',\n    'DenseVecOrMat',\n    'DenseVector',\n    'Dict',\n    'DimensionMismatch',\n    'Dims',\n    'DivideError',\n    'DomainError',\n    'EOFError',\n    'Enum',\n    'ErrorException',\n    'Exception',\n    'ExponentialBackOff',\n    'Expr',\n    'Float16',\n    'Float32',\n    'Float64',\n    'Function',\n    'GlobalRef',\n    'HTML',\n    'IO',\n    'IOBuffer',\n    'IOContext',\n    'IOStream',\n    'IdDict',\n    'IndexCartesian',\n    'IndexLinear',\n    'IndexStyle',\n    'InexactError',\n    'InitError',\n    'Int',\n    'Int128',\n    'Int16',\n    'Int32',\n    'Int64',\n    'Int8',\n    'Integer',\n    'InterruptException',\n    'InvalidStateException',\n    'Irrational',\n    'KeyError',\n    'LinRange',\n    'LineNumberNode',\n    'LinearIndices',\n    'LoadError',\n    'MIME',\n    'Matrix',\n    'Method',\n    'MethodError',\n    'Missing',\n    'MissingException',\n    'Module',\n    'NTuple',\n    'NamedTuple',\n    'Nothing',\n    'Number',\n    'OrdinalRange',\n    'OutOfMemoryError',\n    'OverflowError',\n    'Pair',\n    'PartialQuickSort',\n    'PermutedDimsArray',\n    'Pipe',\n    'ProcessFailedException',\n    'Ptr',\n    'QuoteNode',\n    'Rational',\n    'RawFD',\n    'ReadOnlyMemoryError',\n    'Real',\n    'ReentrantLock',\n    'Ref',\n    'Regex',\n    'RegexMatch',\n    'RoundingMode',\n    'SegmentationFault',\n    'Set',\n    'Signed',\n    'Some',\n    'StackOverflowError',\n    'StepRange',\n    'StepRangeLen',\n    'StridedArray',\n    'StridedMatrix',\n    'StridedVecOrMat',\n    'StridedVector',\n    'String',\n    'StringIndexError',\n    'SubArray',\n    'SubString',\n    'SubstitutionString',\n    'Symbol',\n    'SystemError',\n    'Task',\n    'TaskFailedException',\n    'Text',\n    'TextDisplay',\n    'Timer',\n    'Tuple',\n    'Type',\n    'TypeError',\n    'TypeVar',\n    'UInt',\n    'UInt128',\n    'UInt16',\n    'UInt32',\n    'UInt64',\n    'UInt8',\n    'UndefInitializer',\n    'UndefKeywordError',\n    'UndefRefError',\n    'UndefVarError',\n    'Union',\n    'UnionAll',\n    'UnitRange',\n    'Unsigned',\n    'Val',\n    'Vararg',\n    'VecElement',\n    'VecOrMat',\n    'Vector',\n    'VersionNumber',\n    'WeakKeyDict',\n    'WeakRef',\n  ];\n\n  var KEYWORDS = {\n    $pattern: VARIABLE_NAME_RE,\n    keyword: KEYWORD_LIST,\n    literal: LITERAL_LIST,\n    built_in: BUILT_IN_LIST,\n  };\n\n  // placeholder for recursive self-reference\n  var DEFAULT = {\n    keywords: KEYWORDS, illegal: /<\\//\n  };\n\n  // ref: https://docs.julialang.org/en/v1/manual/integers-and-floating-point-numbers/\n  var NUMBER = {\n    className: 'number',\n    // supported numeric literals:\n    //  * binary literal (e.g. 0x10)\n    //  * octal literal (e.g. 0o76543210)\n    //  * hexadecimal literal (e.g. 0xfedcba876543210)\n    //  * hexadecimal floating point literal (e.g. 0x1p0, 0x1.2p2)\n    //  * decimal literal (e.g. 9876543210, 100_000_000)\n    //  * floating pointe literal (e.g. 1.2, 1.2f, .2, 1., 1.2e10, 1.2e-10)\n    begin: /(\\b0x[\\d_]*(\\.[\\d_]*)?|0x\\.\\d[\\d_]*)p[-+]?\\d+|\\b0[box][a-fA-F0-9][a-fA-F0-9_]*|(\\b\\d[\\d_]*(\\.[\\d_]*)?|\\.\\d[\\d_]*)([eEfF][-+]?\\d+)?/,\n    relevance: 0\n  };\n\n  var CHAR = {\n    className: 'string',\n    begin: /'(.|\\\\[xXuU][a-zA-Z0-9]+)'/\n  };\n\n  var INTERPOLATION = {\n    className: 'subst',\n    begin: /\\$\\(/, end: /\\)/,\n    keywords: KEYWORDS\n  };\n\n  var INTERPOLATED_VARIABLE = {\n    className: 'variable',\n    begin: '\\\\$' + VARIABLE_NAME_RE\n  };\n\n  // TODO: neatly escape normal code in string literal\n  var STRING = {\n    className: 'string',\n    contains: [hljs.BACKSLASH_ESCAPE, INTERPOLATION, INTERPOLATED_VARIABLE],\n    variants: [\n      { begin: /\\w*\"\"\"/, end: /\"\"\"\\w*/, relevance: 10 },\n      { begin: /\\w*\"/, end: /\"\\w*/ }\n    ]\n  };\n\n  var COMMAND = {\n    className: 'string',\n    contains: [hljs.BACKSLASH_ESCAPE, INTERPOLATION, INTERPOLATED_VARIABLE],\n    begin: '`', end: '`'\n  };\n\n  var MACROCALL = {\n    className: 'meta',\n    begin: '@' + VARIABLE_NAME_RE\n  };\n\n  var COMMENT = {\n    className: 'comment',\n    variants: [\n      { begin: '#=', end: '=#', relevance: 10 },\n      { begin: '#', end: '$' }\n    ]\n  };\n\n  DEFAULT.name = 'Julia';\n  DEFAULT.contains = [\n    NUMBER,\n    CHAR,\n    STRING,\n    COMMAND,\n    MACROCALL,\n    COMMENT,\n    hljs.HASH_COMMENT_MODE,\n    {\n      className: 'keyword',\n      begin:\n        '\\\\b(((abstract|primitive)\\\\s+)type|(mutable\\\\s+)?struct)\\\\b'\n    },\n    {begin: /<:/}  // relevance booster\n  ];\n  INTERPOLATION.contains = DEFAULT.contains;\n\n  return DEFAULT;\n}\n\nmodule.exports = julia;\n"], "mappings": ";;;;;AAAA;AAAA;AAQA,aAAS,MAAM,MAAM;AAOnB,UAAI,mBAAmB;AAYvB,UAAI,eAAe;AAAA,QACjB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAiBA,UAAI,eAAe;AAAA,QACjB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAiBA,UAAI,gBAAgB;AAAA,QAClB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,UAAI,WAAW;AAAA,QACb,UAAU;AAAA,QACV,SAAS;AAAA,QACT,SAAS;AAAA,QACT,UAAU;AAAA,MACZ;AAGA,UAAI,UAAU;AAAA,QACZ,UAAU;AAAA,QAAU,SAAS;AAAA,MAC/B;AAGA,UAAI,SAAS;AAAA,QACX,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAQX,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAEA,UAAI,OAAO;AAAA,QACT,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AAEA,UAAI,gBAAgB;AAAA,QAClB,WAAW;AAAA,QACX,OAAO;AAAA,QAAQ,KAAK;AAAA,QACpB,UAAU;AAAA,MACZ;AAEA,UAAI,wBAAwB;AAAA,QAC1B,WAAW;AAAA,QACX,OAAO,QAAQ;AAAA,MACjB;AAGA,UAAI,SAAS;AAAA,QACX,WAAW;AAAA,QACX,UAAU,CAAC,KAAK,kBAAkB,eAAe,qBAAqB;AAAA,QACtE,UAAU;AAAA,UACR,EAAE,OAAO,UAAU,KAAK,UAAU,WAAW,GAAG;AAAA,UAChD,EAAE,OAAO,QAAQ,KAAK,OAAO;AAAA,QAC/B;AAAA,MACF;AAEA,UAAI,UAAU;AAAA,QACZ,WAAW;AAAA,QACX,UAAU,CAAC,KAAK,kBAAkB,eAAe,qBAAqB;AAAA,QACtE,OAAO;AAAA,QAAK,KAAK;AAAA,MACnB;AAEA,UAAI,YAAY;AAAA,QACd,WAAW;AAAA,QACX,OAAO,MAAM;AAAA,MACf;AAEA,UAAI,UAAU;AAAA,QACZ,WAAW;AAAA,QACX,UAAU;AAAA,UACR,EAAE,OAAO,MAAM,KAAK,MAAM,WAAW,GAAG;AAAA,UACxC,EAAE,OAAO,KAAK,KAAK,IAAI;AAAA,QACzB;AAAA,MACF;AAEA,cAAQ,OAAO;AACf,cAAQ,WAAW;AAAA,QACjB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,KAAK;AAAA,QACL;AAAA,UACE,WAAW;AAAA,UACX,OACE;AAAA,QACJ;AAAA,QACA,EAAC,OAAO,KAAI;AAAA;AAAA,MACd;AACA,oBAAc,WAAW,QAAQ;AAEjC,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}