/**
 * 🔌 PROVIDER MANAGER
 * Gère les providers et modèles de manière isolée
 */

import { AssistantAPIClient } from '../../utils/AssistantAPIClient';
import { OllamaClient } from '../../utils/OllamaClient';
import type { Clara<PERSON>rovider, ClaraModel } from '../../types/clara_assistant_types';

export class ProviderManager {
  private providersCache: { data: ClaraProvider[], timestamp: number } | null = null;
  private modelsCache: Map<string, { data: ClaraModel[], timestamp: number }> = new Map();
  private readonly CACHE_DURATION = 30000; // 30 secondes

  /**
   * Clear all caches
   */
  public clearCache(): void {
    console.log('🗑️ Clearing provider caches');
    this.providersCache = null;
    this.modelsCache.clear();
  }

  /**
   * Get available providers with caching
   */
  public async getProviders(): Promise<ClaraProvider[]> {
    try {
      // Check cache first
      if (this.providersCache && (Date.now() - this.providersCache.timestamp) < this.CACHE_DURATION) {
        console.log('✅ Providers retrieved from cache');
        return this.providersCache.data;
      }

      // Try backend proxy for auto-discovery
      try {
        console.log('🔄 Fetching providers via backend proxy...');
        const response = await fetch('http://localhost:8000/proxy/providers', {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' },
          mode: 'cors'
        });

        if (response.ok) {
          const data = await response.json();
          const backendProviders = data.providers || [];

          if (backendProviders.length > 0) {
            console.log(`✅ Found ${backendProviders.length} providers via backend proxy`);

            // Cache results
            this.providersCache = {
              data: backendProviders,
              timestamp: Date.now()
            };

            console.log('✅ Auto-discovered providers:', backendProviders.map(p => p.name));
            return backendProviders;
          }
        }
      } catch (proxyError) {
        console.warn('Backend proxy unavailable, using fallback:', proxyError);
      }

      // Fallback: return empty array if no backend
      console.warn('No providers available - backend proxy not accessible');
      return [];
    } catch (error) {
      console.error('Failed to get providers:', error);
      return [];
    }
  }

  /**
   * Get available models with caching
   */
  public async getModels(providerId?: string): Promise<ClaraModel[]> {
    const cacheKey = providerId || 'all';

    // Check cache first
    if (this.modelsCache.has(cacheKey)) {
      const cached = this.modelsCache.get(cacheKey)!;
      if ((Date.now() - cached.timestamp) < this.CACHE_DURATION) {
        console.log(`✅ Models retrieved from cache for: ${cacheKey}`);
        return cached.data;
      }
    }

    const models: ClaraModel[] = [];
    const providers = await this.getProviders();

    // Filter providers based on providerId parameter
    const targetProviders = providerId
      ? providers.filter(p => p.id === providerId && p.isEnabled)
      : providers.filter(p => p.isEnabled);

    for (const provider of targetProviders) {
      try {
        // Create temporary OllamaClient for this provider
        const tempClient = new OllamaClient(provider.baseUrl || '', {
          apiKey: provider.apiKey || '',
          type: provider.type,
          providerId: provider.id
        });

        const providerModels = await tempClient.listModels();

        for (const model of providerModels) {
          const claraModel: ClaraModel = {
            id: `${provider.id}:${model.id}`,
            name: model.name || model.id,
            provider: provider.id,
            type: this.detectModelType(model.name || model.id),
            size: model.size,
            supportsVision: this.supportsVision(model.name || model.id),
            supportsCode: this.supportsCode(model.name || model.id),
            supportsTools: this.supportsTools(model.name || model.id),
            metadata: {
              digest: model.digest,
              modified_at: model.modified_at
            }
          };

          models.push(claraModel);
        }
      } catch (error) {
        console.warn(`Failed to get models from provider ${provider.name}:`, error);
      }
    }

    // Cache results
    this.modelsCache.set(cacheKey, {
      data: models,
      timestamp: Date.now()
    });

    console.log(`✅ Models cached for: ${cacheKey} (${models.length} models)`);
    return models;
  }

  /**
   * Get primary provider
   */
  public async getPrimaryProvider(): Promise<ClaraProvider | null> {
    try {
      const providers = await this.getProviders();
      return providers.find(p => p.isEnabled && p.isPrimary) || providers.find(p => p.isEnabled) || null;
    } catch (error) {
      console.error('Failed to get primary provider:', error);
      return null;
    }
  }

  /**
   * Test provider connectivity
   */
  public async testProvider(provider: ClaraProvider): Promise<boolean> {
    try {
      const client = new AssistantAPIClient(provider.baseUrl || '', {
        apiKey: provider.apiKey || '',
        providerId: provider.id
      });

      // Simple health check
      const response = await client.sendMessage('test', 'Test message', {
        temperature: 0.1,
        maxTokens: 10
      });

      return !!response;
    } catch (error) {
      console.warn(`Provider ${provider.name} test failed:`, error);
      return false;
    }
  }

  /**
   * Detect model type based on name
   */
  private detectModelType(modelName: string): 'text' | 'vision' | 'code' | 'embedding' {
    const name = modelName.toLowerCase();
    
    if (name.includes('vision') || name.includes('llava') || name.includes('gpt-4-vision')) {
      return 'vision';
    }
    
    if (name.includes('code') || name.includes('codellama') || name.includes('starcoder')) {
      return 'code';
    }
    
    if (name.includes('embed') || name.includes('bge') || name.includes('e5')) {
      return 'embedding';
    }
    
    return 'text';
  }

  /**
   * Check if model supports vision
   */
  private supportsVision(modelName: string): boolean {
    const name = modelName.toLowerCase();
    return name.includes('vision') || name.includes('llava') || name.includes('gpt-4-vision');
  }

  /**
   * Check if model supports code
   */
  private supportsCode(modelName: string): boolean {
    const name = modelName.toLowerCase();
    return name.includes('code') || name.includes('codellama') || name.includes('starcoder') || 
           name.includes('qwen') || name.includes('deepseek');
  }

  /**
   * Check if model supports tools
   */
  private supportsTools(modelName: string): boolean {
    const name = modelName.toLowerCase();
    return name.includes('qwen') || name.includes('llama') || name.includes('mistral') || 
           name.includes('gpt') || name.includes('claude');
  }
}

// Export singleton instance
export const providerManager = new ProviderManager();
